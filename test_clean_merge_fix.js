const axios = require('axios');

const headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'x-organization-id': 'default-org-id',
  'Content-Type': 'application/json'
};

async function testCleanMergeFix() {
  try {
    console.log('=== Testing Clean Merge Fix ===\n');
    
    // Get current inventory
    const inventoryResponse = await axios.get('http://localhost:9000/api/inventory', { headers });
    
    console.log('Current inventory:');
    inventoryResponse.data.forEach(item => {
      console.log(`- ${item.product_name} (${item.sku_code}): ${item.available_quantity}`);
    });
    
    // Get Orange inventory with SKU "hg"
    const orangeInventory = inventoryResponse.data.find(item => item.product_name === 'Orange' && item.sku_code === 'hg');
    const orangeQuantityBefore = orangeInventory ? orangeInventory.available_quantity : 0;
    
    // Create a unique test product
    const timestamp = Date.now();
    console.log('\nCreating unique test product...');
    
    const productData = {
      name: `TestMerge${timestamp}`,
      description: 'Test product for merge',
      status: 'active'
    };
    
    const productResponse = await axios.post('http://localhost:9000/api/products', productData, { headers });
    const testProduct = productResponse.data;
    console.log('Created product:', testProduct.name);
    
    // Create SKU
    const skuData = {
      product_id: testProduct.id,
      code: `tm-${timestamp}`,
      unit_type: 'box',
      unit_weight: 1,
      status: 'active'
    };
    
    const skuResponse = await axios.post('http://localhost:9000/api/products/skus', skuData, { headers });
    const testSku = skuResponse.data;
    console.log('Created SKU:', testSku.code);
    
    // Create inventory with quantity 250
    const inventoryData = {
      product_id: testProduct.id,
      sku_id: testSku.id,
      product_name: testProduct.name,
      sku_code: testSku.code,
      unit_type: testSku.unit_type,
      available_quantity: 250,
      total_weight: 250
    };
    
    await axios.post('http://localhost:9000/api/inventory', inventoryData, { headers });
    console.log('Created inventory with 250 quantity');
    
    console.log(`\nOrange (hg) quantity before merge: ${orangeQuantityBefore}`);
    console.log(`${testProduct.name} (${testSku.code}) quantity: 250`);
    console.log(`Expected Orange (hg) after merge: ${orangeQuantityBefore + 250}`);
    
    // Test 1: Merge with both product name and SKU code change
    console.log('\n=== Test 1: Both Product Name and SKU Code Change ===');
    const mergeData1 = {
      current_product_id: testProduct.id,
      current_sku_id: testSku.id,
      reason: "Testing both changes",
      new_product_name: "Orange",
      new_sku_code: "hg"
    };
    
    try {
      const mergeResponse = await axios.post('http://localhost:9000/api/inventory/update-sku', mergeData1, { headers });
      console.log('✅ Merge successful!');
      console.log('Response:', mergeResponse.data.message);
      
      // Check final inventory
      const finalInventoryResponse = await axios.get('http://localhost:9000/api/inventory', { headers });
      const orangeAfter = finalInventoryResponse.data.find(item => item.product_name === 'Orange' && item.sku_code === 'hg');
      
      console.log(`\nOrange (hg) quantity after merge: ${orangeAfter?.available_quantity}`);
      console.log(`Expected: ${orangeQuantityBefore + 250}`);
      
      if (orangeAfter && orangeAfter.available_quantity === (orangeQuantityBefore + 250)) {
        console.log('✅ SUCCESS: Merge worked correctly! Quantities were properly added.');
      } else {
        console.log('❌ FAIL: Merge quantities do not match!');
      }
      
      // Show merge details
      if (mergeResponse.data.merge_details) {
        console.log('\nMerge details:');
        console.log('Inventory merged:', JSON.stringify(mergeResponse.data.merge_details.inventory_merged, null, 2));
      }
      
    } catch (error) {
      console.error('❌ Merge failed:', error.response?.data || error.message);
    }
    
    // Test 2: Create another product and test with negative quantity
    console.log('\n\n=== Test 2: Negative Quantity Merge ===');
    
    const productData2 = {
      name: `TestNegative${timestamp}`,
      description: 'Test product with negative quantity',
      status: 'active'
    };
    
    const productResponse2 = await axios.post('http://localhost:9000/api/products', productData2, { headers });
    const testProduct2 = productResponse2.data;
    console.log('Created product:', testProduct2.name);
    
    // Create SKU
    const skuData2 = {
      product_id: testProduct2.id,
      code: `tn-${timestamp}`,
      unit_type: 'box',
      unit_weight: 1,
      status: 'active'
    };
    
    const skuResponse2 = await axios.post('http://localhost:9000/api/products/skus', skuData2, { headers });
    const testSku2 = skuResponse2.data;
    console.log('Created SKU:', testSku2.code);
    
    // Create inventory with negative quantity
    const inventoryData2 = {
      product_id: testProduct2.id,
      sku_id: testSku2.id,
      product_name: testProduct2.name,
      sku_code: testSku2.code,
      unit_type: testSku2.unit_type,
      available_quantity: -100,
      total_weight: 0
    };
    
    await axios.post('http://localhost:9000/api/inventory', inventoryData2, { headers });
    console.log('Created inventory with -100 quantity');
    
    // Get current Orange quantity
    const inventoryResponse2 = await axios.get('http://localhost:9000/api/inventory', { headers });
    const orangeInventory2 = inventoryResponse2.data.find(item => item.product_name === 'Orange' && item.sku_code === 'hg');
    const orangeQuantityBefore2 = orangeInventory2 ? orangeInventory2.available_quantity : 0;
    
    console.log(`\nOrange (hg) quantity before merge: ${orangeQuantityBefore2}`);
    console.log(`${testProduct2.name} (${testSku2.code}) quantity: -100`);
    console.log(`Expected Orange (hg) after merge: ${orangeQuantityBefore2 + (-100)}`);
    
    const mergeData2 = {
      current_product_id: testProduct2.id,
      current_sku_id: testSku2.id,
      reason: "Testing negative quantity merge",
      new_product_name: "Orange",
      new_sku_code: "hg"
    };
    
    try {
      const mergeResponse2 = await axios.post('http://localhost:9000/api/inventory/update-sku', mergeData2, { headers });
      console.log('✅ Merge successful!');
      
      // Check final inventory
      const finalInventoryResponse2 = await axios.get('http://localhost:9000/api/inventory', { headers });
      const orangeAfter2 = finalInventoryResponse2.data.find(item => item.product_name === 'Orange' && item.sku_code === 'hg');
      
      console.log(`\nOrange (hg) quantity after merge: ${orangeAfter2?.available_quantity}`);
      console.log(`Expected: ${orangeQuantityBefore2 + (-100)}`);
      
      if (orangeAfter2 && orangeAfter2.available_quantity === (orangeQuantityBefore2 + (-100))) {
        console.log('✅ SUCCESS: Negative quantity merge worked correctly!');
      } else {
        console.log('❌ FAIL: Negative quantity merge failed!');
      }
      
    } catch (error) {
      console.error('❌ Merge failed:', error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

// Run the test
testCleanMergeFix();
