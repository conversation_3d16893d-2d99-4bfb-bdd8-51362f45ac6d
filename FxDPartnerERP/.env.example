# API Configuration
VITE_API_URL=http://localhost:9000
VITE_ADMIN_API_URL=http://localhost:9000
VITE_ORGANIZATION_ID=default-org-id
VITE_API_TIMEOUT=10000
VITE_APP_NAME=FxD Partner
VITE_APP_VERSION=1.0.0
VITE_ENABLE_DEBUG=false
VITE_ENABLE_ANALYTICS=false

# Supabase Configuration (if needed)
# VITE_SUPABASE_URL=your_supabase_project_url
# VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Example:
# VITE_SUPABASE_URL=https://your-project-id.supabase.co
# VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Note: These are public keys and safe to use in frontend applications
# The actual values should be placed in your .env file (which is gitignored)
