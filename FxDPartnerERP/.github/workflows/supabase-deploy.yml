name: Deploy Supabase Migrations

on:
  push:
    branches: [ main ]
    paths:
      - 'supabase/migrations/**'
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Supabase CLI
      uses: supabase/setup-cli@v1
      with:
        version: latest

    - name: Verify Supabase CLI installation
      run: |
        supabase --version
        echo "Supabase CLI installed successfully"

    - name: Debug environment variables
      run: |
        echo "SUPABASE_PROJECT_ID is set: ${{ secrets.SUPABASE_PROJECT_ID != '' }}"
        echo "SUPABASE_ACCESS_TOKEN is set: ${{ secrets.SUPABASE_ACCESS_TOKEN != '' }}"
        echo "SUPABASE_DB_PASSWORD is set: ${{ secrets.SUPABASE_DB_PASSWORD != '' }}"

    - name: Link Supabase project
      run: |
        echo "Linking to Supabase project..."
        supabase link --project-ref $SUPABASE_PROJECT_ID --debug
      env:
        SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
        SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}

    - name: Check migration status
      run: |
        echo "Checking migration status..."
        supabase migration list
      env:
        SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

    - name: Deploy migrations
      run: |
        echo "Deploying migrations..."
        supabase db push --debug
      env:
        SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
        SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD }}

    - name: Verify deployment
      if: success()
      run: |
        echo "✅ Migrations deployed successfully!"
        supabase migration list

    - name: Notify deployment status
      if: always()
      run: |
        if [ ${{ job.status }} == 'success' ]; then
          echo "✅ Supabase migrations deployed successfully!"
        else
          echo "❌ Supabase migration deployment failed!"
          echo "Please check the logs above for details."
          exit 1
        fi
