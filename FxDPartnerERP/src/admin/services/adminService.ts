const API_BASE_URL = `${import.meta.env.VITE_ADMIN_API_URL || 'http://localhost:9000'}/api/admin`;

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('admin_token');
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Helper function to handle API responses
const handleResponse = async (response: Response) => {
  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(data.message || 'API request failed');
  }
  
  return data;
};

// Organization API calls
export const organizationService = {
  // Get all organizations
  getAll: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }) => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.status) queryParams.append('status', params.status);

    const response = await fetch(`${API_BASE_URL}/organizations?${queryParams}`, {
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  },

  // Get organization by ID
  getById: async (id: string) => {
    const response = await fetch(`${API_BASE_URL}/organizations/${id}`, {
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  },

  // Create organization
  create: async (data: { name: string; address?: string }) => {
    const response = await fetch(`${API_BASE_URL}/organizations`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data)
    });
    
    return handleResponse(response);
  },

  // Update organization
  update: async (id: string, data: { name?: string; address?: string; status?: string }) => {
    const response = await fetch(`${API_BASE_URL}/organizations/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data)
    });
    
    return handleResponse(response);
  },

  // Delete organization
  delete: async (id: string) => {
    const response = await fetch(`${API_BASE_URL}/organizations/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  },

  // Get organization statistics
  getStats: async () => {
    const response = await fetch(`${API_BASE_URL}/organizations/stats`, {
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  }
};

// User API calls
export const userService = {
  // Get all users
  getAll: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    status?: string;
    organizationId?: string;
  }) => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.role) queryParams.append('role', params.role);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.organizationId) queryParams.append('organizationId', params.organizationId);

    const response = await fetch(`${API_BASE_URL}/users?${queryParams}`, {
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  },

  // Get user by ID
  getById: async (id: string) => {
    const response = await fetch(`${API_BASE_URL}/users/${id}`, {
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  },

  // Create user
  create: async (data: {
    organization_id: string;
    email: string;
    password: string;
    first_name: string;
    phone?: string;
    role?: string;
    status?: string;
  }) => {
    const response = await fetch(`${API_BASE_URL}/users`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data)
    });
    
    return handleResponse(response);
  },

  // Update user
  update: async (id: string, data: {
    first_name?: string;
    email?: string;
    phone?: string;
    role?: string;
    status?: string;
    organization_id?: string;
  }) => {
    const response = await fetch(`${API_BASE_URL}/users/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data)
    });
    
    return handleResponse(response);
  },

  // Delete user
  delete: async (id: string) => {
    const response = await fetch(`${API_BASE_URL}/users/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  },

  // Get user page permissions
  getPermissions: async (userId: string) => {
    const response = await fetch(`${API_BASE_URL}/users/${userId}/permissions`, {
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  },

  // Update user page permissions
  updatePermissions: async (userId: string, permissions: Array<{
    page_id: string;
    can_view: boolean;
    can_edit: boolean;
  }>) => {
    const response = await fetch(`${API_BASE_URL}/users/${userId}/permissions`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify({ permissions })
    });
    
    return handleResponse(response);
  },

  // Get user statistics
  getStats: async () => {
    const response = await fetch(`${API_BASE_URL}/users/stats`, {
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  },

  // Get users by organization
  getByOrganization: async (organizationId: string, params?: {
    page?: number;
    limit?: number;
  }) => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const response = await fetch(`${API_BASE_URL}/users/organization/${organizationId}?${queryParams}`, {
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  }
};

// Role API calls
export const roleService = {
  // Get all roles
  getAll: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    organizationId?: string;
    status?: string;
  }) => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.organizationId) queryParams.append('organizationId', params.organizationId);
    if (params?.status) queryParams.append('status', params.status);

    const response = await fetch(`${API_BASE_URL}/roles?${queryParams}`, {
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  },

  // Get role by ID
  getById: async (id: string) => {
    const response = await fetch(`${API_BASE_URL}/roles/${id}`, {
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  },

  // Update role
  update: async (id: string, data: {
    display_name?: string;
    description?: string;
    permissions?: string[];
    status?: string;
  }) => {
    const response = await fetch(`${API_BASE_URL}/roles/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data)
    });
    
    return handleResponse(response);
  },

  // Get available permissions
  getPermissions: async () => {
    const response = await fetch(`${API_BASE_URL}/roles/permissions`, {
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  },

  // Get role statistics
  getStats: async () => {
    const response = await fetch(`${API_BASE_URL}/roles/stats`, {
      headers: getAuthHeaders()
    });
    
    return handleResponse(response);
  }
};

// Generic HTTP client for direct API calls
const httpClient = {
  get: async <T = any>(url: string, options?: { params?: Record<string, any> }): Promise<{ data: T }> => {
    let fullUrl = `${API_BASE_URL}${url}`;
    
    if (options?.params) {
      const queryParams = new URLSearchParams();
      Object.entries(options.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
      if (queryParams.toString()) {
        fullUrl += `?${queryParams.toString()}`;
      }
    }

    const response = await fetch(fullUrl, {
      headers: getAuthHeaders()
    });
    
    const data = await handleResponse(response);
    return { data };
  },

  post: async <T = any>(url: string, body?: any): Promise<{ data: T }> => {
    const response = await fetch(`${API_BASE_URL}${url}`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: body ? JSON.stringify(body) : undefined
    });
    
    const data = await handleResponse(response);
    return { data };
  },

  put: async <T = any>(url: string, body?: any): Promise<{ data: T }> => {
    const response = await fetch(`${API_BASE_URL}${url}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: body ? JSON.stringify(body) : undefined
    });
    
    const data = await handleResponse(response);
    return { data };
  },

  delete: async <T = any>(url: string): Promise<{ data: T }> => {
    const response = await fetch(`${API_BASE_URL}${url}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });
    
    const data = await handleResponse(response);
    return { data };
  }
};

// Export all services
export const adminService = {
  organizations: organizationService,
  users: userService,
  roles: roleService,
  // Generic HTTP methods
  get: httpClient.get,
  post: httpClient.post,
  put: httpClient.put,
  delete: httpClient.delete
};

export default adminService;
