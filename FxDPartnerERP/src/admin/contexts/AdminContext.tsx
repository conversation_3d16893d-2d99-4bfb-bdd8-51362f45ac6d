import React, { createContext, useContext, useState, useEffect } from 'react';

interface Admin {
  username: string;
  loginTime: string;
}

interface AdminContextType {
  admin: Admin | null;
  loading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
}

const AdminContext = createContext<AdminContextType>({
  admin: null,
  loading: true,
  login: async () => {},
  logout: () => {},
  isAuthenticated: false,
});

export const useAdmin = () => useContext(AdminContext);

export const AdminProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [admin, setAdmin] = useState<Admin | null>(null);
  const [loading, setLoading] = useState(true);

  // Get admin API URL from environment variables
  const adminApiUrl = import.meta.env.VITE_ADMIN_API_URL || 'http://localhost:9000';

  // Check if admin is logged in on initial load
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const storedToken = localStorage.getItem('admin_token');
        const storedAdmin = localStorage.getItem('admin_user');
        
        if (storedToken && storedAdmin) {
          // Verify token with backend
          const response = await fetch(`${adminApiUrl}/api/admin/auth/verify`, {
            headers: {
              'Authorization': `Bearer ${storedToken}`,
              'Content-Type': 'application/json'
            }
          });

          if (response.ok) {
            const data = await response.json();
            setAdmin(JSON.parse(storedAdmin));
          } else {
            // Token is invalid, clear storage
            localStorage.removeItem('admin_token');
            localStorage.removeItem('admin_user');
          }
        }
      } catch (error) {
        console.error('Admin auth check error:', error);
        // Clear invalid auth data
        localStorage.removeItem('admin_token');
        localStorage.removeItem('admin_user');
      } finally {
        setLoading(false);
      }
    };
    
    checkAuth();
  }, [adminApiUrl]);

  const login = async (username: string, password: string) => {
    setLoading(true);
    try {
      const response = await fetch(`${adminApiUrl}/api/admin/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Login failed');
      }

      // Store auth data
      localStorage.setItem('admin_token', data.data.token);
      localStorage.setItem('admin_user', JSON.stringify(data.data.admin));

      // Update state
      setAdmin(data.data.admin);
    } catch (error) {
      console.error('Admin login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      
      if (token) {
        // Call logout endpoint
        await fetch(`${adminApiUrl}/api/admin/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
      }
    } catch (error) {
      console.error('Admin logout error:', error);
    } finally {
      // Clear local storage and state regardless of API call result
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');
      setAdmin(null);
    }
  };

  return (
    <AdminContext.Provider 
      value={{ 
        admin, 
        loading, 
        login, 
        logout, 
        isAuthenticated: !!admin
      }}
    >
      {children}
    </AdminContext.Provider>
  );
};
