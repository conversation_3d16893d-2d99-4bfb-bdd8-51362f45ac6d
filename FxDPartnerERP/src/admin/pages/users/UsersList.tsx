import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { adminService } from '../../services/adminService';

// Feature flag to show/hide roles - set to false to hide role functionality
const SHOW_ROLES = false;

interface Organization {
  id: string;
  name: string;
}

// ROLES HIDDEN - Role interfaces kept for future use
interface Role {
  id: string;
  name: string;
  display_name: string;
  organization_id?: string;
}

interface UserOrganization {
  id: string;
  user_id: string;
  organization_id: string;
  is_primary: boolean;
  status: string;
  organization: Organization;
}

// ROLES HIDDEN - UserRole interface kept for future use
interface UserRole {
  id: string;
  user_id: string;
  role_id: string;
  organization_id: string;
  is_primary: boolean;
  status: string;
  role: Role;
  organization: Organization;
}

interface User {
  id: string;
  first_name: string;
  email: string;
  status: string;
  created_at: string;
  user_organizations?: UserOrganization[];
  user_roles?: UserRole[]; // ROLES HIDDEN - kept for future use
}

interface UsersResponse {
  users: User[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

const UsersList: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [dropdownPosition, setDropdownPosition] = useState<{ top: number; right: number } | null>(null);
  const [assignmentData, setAssignmentData] = useState({
    selectedOrganizations: [] as string[],
    selectedRoles: [] as string[],
    primaryOrganization: '',
    primaryRole: ''
  });
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  });
  const [filters, setFilters] = useState({
    search: '',
    status: 'all'
  });

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await adminService.get<{ data: UsersResponse }>('/users', {
        params: {
          page: pagination.page,
          limit: pagination.limit,
          ...filters
        }
      });
      
      setUsers(response.data.data.users);
      setPagination(response.data.data.pagination);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchOrganizations();
    fetchRoles();
  }, [pagination.page, filters]);

  const fetchOrganizations = async () => {
    try {
      const response = await adminService.get<{ data: { organizations: Organization[] } }>('/organizations');
      setOrganizations(response.data.data.organizations);
    } catch (err) {
      console.error('Failed to fetch organizations:', err);
    }
  };

  const fetchRoles = async () => {
    try {
      const response = await adminService.get<{ data: { roles: Role[] } }>('/roles');
      setRoles(response.data.data.roles);
    } catch (err) {
      console.error('Failed to fetch roles:', err);
    }
  };

  const handleDelete = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return;
    
    try {
      await adminService.delete(`/users/${userId}`);
      fetchUsers(); // Refresh the list
    } catch (err: any) {
      alert(err.response?.data?.message || 'Failed to delete user');
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
  };

  const openAssignModal = (user: User) => {
    setSelectedUser(user);
    setAssignmentData({
      selectedOrganizations: user.user_organizations?.map(uo => uo.organization_id) || [],
      selectedRoles: user.user_roles?.map(ur => ur.role_id) || [],
      primaryOrganization: user.user_organizations?.find(uo => uo.is_primary)?.organization_id || '',
      primaryRole: user.user_roles?.find(ur => ur.is_primary)?.role_id || ''
    });
    setShowAssignModal(true);
  };

  const closeAssignModal = () => {
    setShowAssignModal(false);
    setSelectedUser(null);
    setAssignmentData({
      selectedOrganizations: [],
      selectedRoles: [],
      primaryOrganization: '',
      primaryRole: ''
    });
  };

  const handleOrganizationChange = (orgId: string, checked: boolean) => {
    setAssignmentData(prev => {
      const newOrgs = checked 
        ? [...prev.selectedOrganizations, orgId]
        : prev.selectedOrganizations.filter(id => id !== orgId);
      
      // If removing primary organization, clear primary
      const newPrimary = checked || prev.primaryOrganization !== orgId 
        ? prev.primaryOrganization 
        : '';

      return {
        ...prev,
        selectedOrganizations: newOrgs,
        primaryOrganization: newPrimary
      };
    });
  };

  const handleRoleChange = (roleId: string, checked: boolean) => {
    setAssignmentData(prev => {
      const newRoles = checked 
        ? [...prev.selectedRoles, roleId]
        : prev.selectedRoles.filter(id => id !== roleId);
      
      // If removing primary role, clear primary
      const newPrimary = checked || prev.primaryRole !== roleId 
        ? prev.primaryRole 
        : '';

      return {
        ...prev,
        selectedRoles: newRoles,
        primaryRole: newPrimary
      };
    });
  };

  const saveAssignments = async () => {
    if (!selectedUser) return;

    try {
      // Build the assignments data structure - roles are now independent of organizations
      const assignments = {
        organizations: assignmentData.selectedOrganizations.map(orgId => ({
          organization_id: orgId,
          is_primary: orgId === assignmentData.primaryOrganization,
          roles: assignmentData.selectedRoles.map(roleId => ({
            role_id: roleId,
            is_primary: roleId === assignmentData.primaryRole
          }))
        }))
      };

      // Validate that at least one organization is selected
      if (assignments.organizations.length === 0) {
        alert('Please select at least one organization');
        return;
      }

      // Use the new atomic assignment endpoint
      await adminService.put(`/users/${selectedUser.id}/assignments`, {
        assignments
      });

      closeAssignModal();
      fetchUsers(); // Refresh the list
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to update user assignments';
      alert(errorMessage);
      console.error('Assignment update error:', err);
    }
  };

  const getUserOrganizations = (user: User): string => {
    if (!user.user_organizations || user.user_organizations.length === 0) {
      return 'No Organizations';
    }
    
    return user.user_organizations
      .map(uo => `${uo.organization.name}${uo.is_primary ? ' (Primary)' : ''}`)
      .join(', ');
  };

  const getUserRoles = (user: User): string => {
    if (!user.user_roles || user.user_roles.length === 0) {
      return 'No Roles';
    }
    
    return user.user_roles
      .map(ur => `${ur.role.display_name}${ur.is_primary ? ' (Primary)' : ''}`)
      .join(', ');
  };

  const toggleDropdown = (userId: string) => {
    setOpenDropdown(openDropdown === userId ? null : userId);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setOpenDropdown(null);
    };

    if (openDropdown) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [openDropdown]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="text-red-800">{error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Users Management</h1>
          <p className="text-gray-600">Manage system users and their permissions</p>
        </div>
        <Link
          to="/admin/users/create"
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Add New User
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search
              </label>
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Search by name or email..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="pending">Pending</option>
              </select>
            </div>
          </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Organizations
                </th>
                {/* ROLES HIDDEN - Uncomment below to show roles column
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Roles
                </th>
                */}
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 max-w-[200px]">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {user.first_name}
                      </div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {getUserOrganizations(user)}
                    </div>
                  </td>
                  {/* ROLES HIDDEN - Uncomment below to show roles data
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {getUserRoles(user)}
                    </div>
                  </td>
                  */}
                  <td className="px-6 py-4 max-w-[200px]">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.status === 'active' ? 'bg-green-100 text-green-800' :
                      user.status === 'inactive' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {user.status.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 max-w-[200px] text-sm text-gray-500">
                    {new Date(user.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 max-w-[200px] text-right text-sm font-medium">
                    <div className="relative">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          const rect = e.currentTarget.getBoundingClientRect();
                          setDropdownPosition({
                            top: rect.bottom + window.scrollY,
                            right: window.innerWidth - rect.right
                          });
                          toggleDropdown(user.id);
                        }}
                        className="inline-flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <svg className="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                disabled={pagination.page === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.totalPages, prev.page + 1) }))}
                disabled={pagination.page === pagination.totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{((pagination.page - 1) * pagination.limit) + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(pagination.page * pagination.limit, pagination.total)}
                  </span>{' '}
                  of <span className="font-medium">{pagination.total}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                    disabled={pagination.page === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.totalPages, prev.page + 1) }))}
                    disabled={pagination.page === pagination.totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Dropdown Menu */}
      {openDropdown && dropdownPosition && (
        <div 
          className="fixed bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none w-48 py-1"
          style={{
            zIndex: 9999,
            top: `${dropdownPosition.top}px`,
            right: `${dropdownPosition.right}px`,
            maxHeight: '300px',
            overflow: 'visible'
          }}
        >
          <button
            onClick={(e) => {
              e.stopPropagation();
              const user = users.find(u => u.id === openDropdown);
              if (user) openAssignModal(user);
              setOpenDropdown(null);
            }}
            className="flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-gray-50 hover:text-green-900"
          >
            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            Assign
          </button>
          <Link
            to={`/admin/users/${openDropdown}/permissions`}
            onClick={() => setOpenDropdown(null)}
            className="flex items-center w-full px-4 py-2 text-sm text-blue-600 hover:bg-gray-50 hover:text-blue-900"
          >
            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
            Permissions
          </Link>
          <Link
            to={`/admin/users/${openDropdown}/edit`}
            onClick={() => setOpenDropdown(null)}
            className="flex items-center w-full px-4 py-2 text-sm text-indigo-600 hover:bg-gray-50 hover:text-indigo-900"
          >
            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Edit
          </Link>
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (openDropdown) handleDelete(openDropdown);
              setOpenDropdown(null);
            }}
            className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-50 hover:text-red-900"
          >
            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Delete
          </button>
        </div>
      )}

      {/* Assignment Modal */}
      {showAssignModal && selectedUser && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Assign Organizations to {selectedUser.first_name}
                </h3>
                <button
                  onClick={closeAssignModal}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-6">
                {/* Organizations Section */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3">Organizations</h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {organizations.map((org) => (
                      <label key={org.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={assignmentData.selectedOrganizations.includes(org.id)}
                          onChange={(e) => handleOrganizationChange(org.id, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">{org.name}</span>
                        {assignmentData.selectedOrganizations.includes(org.id) && (
                          <label className="flex items-center space-x-1 ml-4">
                            <input
                              type="radio"
                              name="primaryOrganization"
                              checked={assignmentData.primaryOrganization === org.id}
                              onChange={() => setAssignmentData(prev => ({ ...prev, primaryOrganization: org.id }))}
                              className="text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-xs text-blue-600">Primary</span>
                          </label>
                        )}
                      </label>
                    ))}
                  </div>
                </div>

                {/* ROLES HIDDEN - Uncomment below to show roles section
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3">Roles</h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {roles.map((role) => (
                      <label key={role.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={assignmentData.selectedRoles.includes(role.id)}
                          onChange={(e) => handleRoleChange(role.id, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">{role.display_name}</span>
                        {assignmentData.selectedRoles.includes(role.id) && (
                          <label className="flex items-center space-x-1 ml-4">
                            <input
                              type="radio"
                              name="primaryRole"
                              checked={assignmentData.primaryRole === role.id}
                              onChange={() => setAssignmentData(prev => ({ ...prev, primaryRole: role.id }))}
                              className="text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-xs text-blue-600">Primary</span>
                          </label>
                        )}
                      </label>
                    ))}
                  </div>
                </div>
                */}

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <button
                    onClick={closeAssignModal}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={saveAssignments}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Save Assignments
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UsersList;
