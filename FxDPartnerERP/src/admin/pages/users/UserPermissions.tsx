import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { adminService } from '../../services/adminService';

interface PagePermission {
  page_id: string;
  page_name: string;
  display_name: string;
  category: string;
  can_view: boolean;
  can_edit: boolean;
}

interface User {
  id: string;
  first_name: string;
  email: string;
  role: string;
}

interface UserPermissionsData {
  user: User;
  page_permissions: PagePermission[];
}

const UserPermissions: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState<UserPermissionsData | null>(null);
  const [permissions, setPermissions] = useState<PagePermission[]>([]);

  useEffect(() => {
    if (userId) {
      fetchUserPermissions();
    }
  }, [userId]);

  const fetchUserPermissions = async () => {
    try {
      setLoading(true);
      const response = await adminService.users.getPermissions(userId!);
      setUserData(response.data);
      setPermissions(response.data.page_permissions);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch user permissions');
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionChange = (pageId: string, field: 'can_view' | 'can_edit', value: boolean) => {
    setPermissions(prev => prev.map(permission => {
      if (permission.page_id === pageId) {
        const updated = { ...permission, [field]: value };
        
        // Auto-select view if edit is selected
        if (field === 'can_edit' && value) {
          updated.can_view = true;
        }
        
        // Auto-unselect edit if view is unselected
        if (field === 'can_view' && !value) {
          updated.can_edit = false;
        }
        
        return updated;
      }
      return permission;
    }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      await adminService.users.updatePermissions(userId!, permissions);
      navigate('/admin/users');
    } catch (err: any) {
      alert(err.message || 'Failed to update permissions');
    } finally {
      setSaving(false);
    }
  };

  const groupedPermissions = permissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, PagePermission[]>);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="text-red-800">{error}</div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div className="text-yellow-800">User not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Permissions</h1>
          <p className="text-gray-600">
            Manage page access permissions for {userData.user.first_name}
          </p>
        </div>
        <div className="flex space-x-3">
          <Link
            to="/admin/users"
            className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
          >
            Cancel
          </Link>
          <button
            onClick={handleSave}
            disabled={saving}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {saving ? 'Saving...' : 'Save Permissions'}
          </button>
        </div>
      </div>

      {/* User Info */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Name</label>
            <div className="text-sm text-gray-900">
              {userData.user.first_name}
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <div className="text-sm text-gray-900">{userData.user.email}</div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Role</label>
            <div className="text-sm text-gray-900">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                userData.user.role === 'super_admin' ? 'bg-purple-100 text-purple-800' :
                userData.user.role === 'admin' ? 'bg-red-100 text-red-800' :
                userData.user.role === 'manager' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {userData.user.role ? userData.user.role.replace('_', ' ').toUpperCase() : 'NO ROLE ASSIGNED'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Permissions Matrix */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Page Permissions</h3>
          <p className="text-sm text-gray-600">
            Select which pages this user can view and edit. Edit permission automatically includes view permission.
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Page
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  View
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Edit
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
                <React.Fragment key={category}>
                  {/* Category Header */}
                  <tr className="bg-gray-100">
                    <td colSpan={3} className="px-6 py-3 text-sm font-medium text-gray-700 uppercase tracking-wider">
                      {category}
                    </td>
                  </tr>
                  {/* Category Pages */}
                  {categoryPermissions.map((permission) => (
                    <tr key={permission.page_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 max-w-[200px]">
                        <div className="text-sm font-medium text-gray-900">
                          {permission.display_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {permission.page_name}
                        </div>
                      </td>
                      <td className="px-6 py-4 max-w-[200px] text-center">
                        <input
                          type="checkbox"
                          checked={permission.can_view}
                          onChange={(e) => handlePermissionChange(permission.page_id, 'can_view', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 max-w-[200px] text-center">
                        <input
                          type="checkbox"
                          checked={permission.can_edit}
                          onChange={(e) => handlePermissionChange(permission.page_id, 'can_edit', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                    </tr>
                  ))}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Quick Actions</h4>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => {
              setPermissions(prev => prev.map(p => ({ ...p, can_view: true, can_edit: false })));
            }}
            className="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200"
          >
            Grant All View
          </button>
          <button
            onClick={() => {
              setPermissions(prev => prev.map(p => ({ ...p, can_view: true, can_edit: true })));
            }}
            className="px-3 py-1 text-xs bg-green-100 text-green-800 rounded-md hover:bg-green-200"
          >
            Grant All Edit
          </button>
          <button
            onClick={() => {
              setPermissions(prev => prev.map(p => ({ 
                ...p, 
                can_view: p.page_name === 'dashboard', 
                can_edit: false 
              })));
            }}
            className="px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200"
          >
            Reset to Default
          </button>
          <button
            onClick={() => {
              setPermissions(prev => prev.map(p => ({ ...p, can_view: false, can_edit: false })));
            }}
            className="px-3 py-1 text-xs bg-red-100 text-red-800 rounded-md hover:bg-red-200"
          >
            Remove All
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserPermissions;
