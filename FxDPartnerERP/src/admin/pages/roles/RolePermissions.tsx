import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { adminService } from '../../services/adminService';

interface Role {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  permissions: string[];
  pages: string[];
  status: string;
  is_system_role: boolean;
  organization: {
    id: string;
    name: string;
  } | null;
}

interface Permission {
  key: string;
  label: string;
  description: string;
}

interface PermissionGroup {
  [category: string]: Permission[];
}

interface Page {
  id: string;
  name: string;
  display_name: string;
  route_path?: string;
  icon?: string;
}

interface PageGroup {
  [category: string]: Page[];
}

const RolePermissions: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [role, setRole] = useState<Role | null>(null);
  const [permissionGroups, setPermissionGroups] = useState<PermissionGroup>({});
  const [pageGroups, setPageGroups] = useState<PageGroup>({});
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [selectedPages, setSelectedPages] = useState<string[]>([]);

  useEffect(() => {
    if (id) {
      fetchData();
    }
  }, [id]);

  const fetchData = async () => {
    try {
      setInitialLoading(true);
      // Fetch role, permissions, and pages
      const [roleResponse, permissionsResponse, pagesResponse] = await Promise.all([
        adminService.get<{ data: { role: Role } }>(`/roles/${id}`),
        adminService.get<{ data: { permissionGroups: PermissionGroup } }>('/roles/permissions'),
        adminService.get<{ data: { pageGroups: PageGroup } }>('/roles/pages')
      ]);

      const roleData = roleResponse.data.data.role;
      setRole(roleData);
      setPermissionGroups(permissionsResponse.data.data.permissionGroups);
      setPageGroups(pagesResponse.data.data.pageGroups);
      setSelectedPermissions(roleData.permissions || []);
      setSelectedPages(roleData.pages || []);
    } catch (error) {
      console.error('Failed to fetch data:', error);
      navigate('/admin/roles');
    } finally {
      setInitialLoading(false);
    }
  };

  const handlePermissionChange = (permissionKey: string, checked: boolean) => {
    setSelectedPermissions(prev => 
      checked
        ? [...prev, permissionKey]
        : prev.filter(p => p !== permissionKey)
    );
  };

  const handlePageChange = (pageId: string, checked: boolean) => {
    setSelectedPages(prev => 
      checked
        ? [...prev, pageId]
        : prev.filter(p => p !== pageId)
    );
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      await adminService.put(`/roles/${id}`, {
        display_name: role?.display_name,
        description: role?.description,
        permissions: selectedPermissions,
        pages: selectedPages,
        status: role?.status
      });
      navigate('/admin/roles');
    } catch (error: any) {
      alert(error.response?.data?.message || 'Failed to update role permissions');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!role) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="text-red-800">Role not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Role Permissions</h1>
          <p className="text-gray-600">
            Manage permissions for <span className="font-medium">{role.display_name}</span>
          </p>
        </div>
        <button
          onClick={() => navigate('/admin/roles')}
          className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
        >
          Back to Roles
        </button>
      </div>

      {/* Role Info */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Role Name</label>
            <div className="mt-1 text-sm text-gray-900">{role.name}</div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Display Name</label>
            <div className="mt-1 text-sm text-gray-900">{role.display_name}</div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Organization</label>
            <div className="mt-1 text-sm text-gray-900">{role.organization?.name || 'N/A'}</div>
          </div>
        </div>
        {role.description && (
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700">Description</label>
            <div className="mt-1 text-sm text-gray-900">{role.description}</div>
          </div>
        )}
        {role.is_system_role && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  This is a system role. Changes to permissions may affect system functionality.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Page Access */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Page Access</h2>
          <p className="text-sm text-gray-600 mb-4">
            Selected: {selectedPages.length} pages
          </p>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {Object.entries(pageGroups).map(([category, pages]) => (
              <div key={category} className="border rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-3">{category}</h3>
                <div className="space-y-2">
                  {pages.map(page => (
                    <label key={page.id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={selectedPages.includes(page.id)}
                        onChange={(e) => handlePageChange(page.id, e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{page.display_name}</span>
                    </label>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Permissions */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Action Permissions</h2>
          <p className="text-sm text-gray-600 mb-4">
            Selected: {selectedPermissions.length} permissions
          </p>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {Object.entries(permissionGroups).map(([category, permissions]) => (
              <div key={category} className="border rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-3">{category}</h3>
                <div className="space-y-2">
                  {permissions.map(permission => (
                    <label key={permission.key} className="flex items-start space-x-2">
                      <input
                        type="checkbox"
                        checked={selectedPermissions.includes(permission.key)}
                        onChange={(e) => handlePermissionChange(permission.key, e.target.checked)}
                        className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-700">{permission.label}</div>
                        <div className="text-xs text-gray-500">{permission.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-blue-900 mb-2">Permission Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-blue-800">Page Access:</span>
            <span className="ml-2 text-blue-700">{selectedPages.length} pages selected</span>
          </div>
          <div>
            <span className="font-medium text-blue-800">Action Permissions:</span>
            <span className="ml-2 text-blue-700">{selectedPermissions.length} permissions selected</span>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end space-x-4">
        <button
          onClick={() => navigate('/admin/roles')}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Saving...' : 'Save Permissions'}
        </button>
      </div>
    </div>
  );
};

export default RolePermissions;
