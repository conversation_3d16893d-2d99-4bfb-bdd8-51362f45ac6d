import React, { useState } from 'react';
import { FileText, Upload as UploadIcon } from 'lucide-react';
import FileUpload from './FileUpload';
import DocumentViewer from './DocumentViewer';
import { FileUploadResult, DocumentData } from '../../services/api/documentService';

interface DocumentManagerProps {
  entityType: string;
  entityId: string;
  title?: string;
  allowUpload?: boolean;
  allowDelete?: boolean;
  multiple?: boolean;
  maxFiles?: number;
  className?: string;
}

const DocumentManager: React.FC<DocumentManagerProps> = ({
  entityType,
  entityId,
  title = 'Documents',
  allowUpload = true,
  allowDelete = true,
  multiple = true,
  maxFiles = 10,
  className = ''
}) => {
  const [showUpload, setShowUpload] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  // Handle successful upload
  const handleUploadComplete = (files: FileUploadResult[] | DocumentData[]) => {
    console.log('Upload completed:', files);
    setShowUpload(false);
    // Trigger refresh of document viewer
    setRefreshKey(prev => prev + 1);
  };

  // Handle document deletion
  const handleDocumentDeleted = (documentId: string) => {
    console.log('Document deleted:', documentId);
    // Trigger refresh of document viewer
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <FileText className="w-5 h-5 text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
        </div>
        
        {allowUpload && (
          <button
            onClick={() => setShowUpload(!showUpload)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <UploadIcon className="w-4 h-4" />
            <span>{showUpload ? 'Cancel Upload' : 'Upload Files'}</span>
          </button>
        )}
      </div>

      {/* Upload Section */}
      {showUpload && allowUpload && (
        <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
          <h3 className="text-md font-medium text-gray-900 mb-4">Upload New Documents</h3>
          <FileUpload
            entityType={entityType}
            entityId={entityId}
            multiple={multiple}
            maxFiles={maxFiles}
            autoAttach={true}
            onUploadComplete={handleUploadComplete}
            onError={(error) => {
              console.error('Upload error:', error);
            }}
          />
        </div>
      )}

      {/* Document Viewer */}
      <DocumentViewer
        key={refreshKey}
        entityType={entityType}
        entityId={entityId}
        allowDelete={allowDelete}
        showUploader={!allowUpload}
        onDocumentDeleted={handleDocumentDeleted}
      />
    </div>
  );
};

export default DocumentManager;
