import React, { useState, useEffect } from 'react';
import { Download, Trash2, Eye, FileText, Image, File } from 'lucide-react';
import { DocumentService, DocumentData } from '../../services/api/documentService';
import toast from 'react-hot-toast';

interface DocumentViewerProps {
  entityType: string;
  entityId: string;
  onDocumentDeleted?: (documentId: string) => void;
  className?: string;
  showUploader?: boolean;
  allowDelete?: boolean;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  entityType,
  entityId,
  onDocumentDeleted,
  className = '',
  showUploader = false,
  allowDelete = true
}) => {
  const [documents, setDocuments] = useState<DocumentData[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState<string | null>(null);

  // Load documents
  const loadDocuments = async () => {
    try {
      setLoading(true);
      const docs = await DocumentService.getEntityDocuments(entityType, entityId);
      setDocuments(docs);
    } catch (error) {
      console.error('Error loading documents:', error);
      toast.error('Failed to load documents');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDocuments();
  }, [entityType, entityId]);

  // Delete document
  const handleDelete = async (documentId: string) => {
    if (!confirm('Are you sure you want to delete this document?')) {
      return;
    }

    try {
      setDeleting(documentId);
      await DocumentService.deleteDocument(documentId);
      setDocuments(prev => prev.filter(doc => doc.id !== documentId));
      toast.success('Document deleted successfully');
      
      if (onDocumentDeleted) {
        onDocumentDeleted(documentId);
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      toast.error('Failed to delete document');
    } finally {
      setDeleting(null);
    }
  };

  // Get file icon
  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith('image/')) {
      return <Image className="w-5 h-5 text-blue-500" />;
    } else if (contentType === 'application/pdf') {
      return <FileText className="w-5 h-5 text-red-500" />;
    } else {
      return <File className="w-5 h-5 text-gray-500" />;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className={`${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="flex items-center space-x-3 p-3 bg-gray-50 rounded">
                <div className="w-5 h-5 bg-gray-300 rounded"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-300 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">
          Documents ({documents.length})
        </h3>
      </div>

      {documents.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>No documents uploaded yet</p>
          {showUploader && (
            <p className="text-sm mt-2">Use the upload section above to add documents</p>
          )}
        </div>
      ) : (
        <div className="space-y-2">
          {documents.map((document) => (
            <div
              key={document.id}
              className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50"
            >
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                {getFileIcon(document.content_type)}
                
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {document.display_name || document.file_name}
                  </p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>{DocumentService.formatFileSize(document.file_size)}</span>
                    <span>{formatDate(document.created_at)}</span>
                    {document.uploader && (
                      <span>by {document.uploader.first_name}</span>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {/* View/Download Button */}
                {document.file_url && (
                  <a
                    href={document.file_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded"
                    title="View document"
                  >
                    <Eye className="w-4 h-4" />
                  </a>
                )}

                {/* Download Button */}
                {document.file_url && (
                  <a
                    href={document.file_url}
                    download={document.file_name}
                    className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded"
                    title="Download document"
                  >
                    <Download className="w-4 h-4" />
                  </a>
                )}

                {/* Delete Button */}
                {allowDelete && (
                  <button
                    onClick={() => handleDelete(document.id)}
                    disabled={deleting === document.id}
                    className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Delete document"
                  >
                    {deleting === document.id ? (
                      <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      <Trash2 className="w-4 h-4" />
                    )}
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DocumentViewer;
