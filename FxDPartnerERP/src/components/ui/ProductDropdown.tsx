import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Package, AlertTriangle } from 'lucide-react';

interface InventoryItem {
  id: string;
  product_id: string;
  product_name: string;
  product_category: string;
  sku_id: string;
  sku_code: string;
  unit_type: string;
  available_quantity: number;
  total_weight: number;
}

interface ProductDropdownProps {
  inventory: InventoryItem[];
  value: string;
  onChange: (inventoryId: string, selectedItem?: InventoryItem) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  onAdjustInventory?: (item: InventoryItem) => void;
}

const ProductDropdown: React.FC<ProductDropdownProps> = ({
  inventory,
  value,
  onChange,
  placeholder = 'Select a product',
  className = '',
  disabled = false,
  onAdjustInventory
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [inputValue, setInputValue] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const selectedItem = inventory.find(item => item.id === value);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        // Check if we should create a new product
        if (inputValue && !selectedItem) {
          handleCreateNewProduct();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [inputValue, selectedItem]);

  // Update input value when selection changes
  useEffect(() => {
    if (selectedItem) {
      setInputValue(`${selectedItem.product_name} ${selectedItem.sku_code}`);
    } else if (!isOpen) {
      setInputValue('');
    }
  }, [selectedItem, isOpen]);



  const handleSelect = (item: InventoryItem) => {
    onChange(item.id, item);
    setIsOpen(false);
    setSearchTerm('');
    setInputValue(`${item.product_name} ${item.sku_code}`);
  };

  // Check if input matches new product format: "product_name sku_code"
  const getNewProductFromInput = () => {
    const trimmedInput = inputValue.trim();
    if (!trimmedInput) return null;
    
    // Match pattern: "product_name sku_code" (last word is sku_code)
    const lastSpaceIndex = trimmedInput.lastIndexOf(' ');
    if (lastSpaceIndex > 0) {
      const productName = trimmedInput.substring(0, lastSpaceIndex).trim();
      const skuCode = trimmedInput.substring(lastSpaceIndex + 1).trim();
      
      if (productName && skuCode) {
        return {
          productName,
          skuCode
        };
      }
    }
    return null;
  };

  const handleCreateNewProduct = () => {
    const newProduct = getNewProductFromInput();
    if (!newProduct) return;

    // Create a mock inventory item for new product
    const newProductItem: InventoryItem = {
      id: 'NEW_PRODUCT',
      product_id: 'NEW_PRODUCT',
      product_name: newProduct.productName,
      product_category: 'general',
      sku_id: 'NEW_SKU',
      sku_code: newProduct.skuCode,
      unit_type: 'box',
      available_quantity: 0,
      total_weight: 0
    };

    onChange('NEW_PRODUCT', newProductItem);
    setInputValue(`${newProduct.productName} ${newProduct.skuCode}`);
  };

  const handleAdjustInventory = (e: React.MouseEvent, item: InventoryItem) => {
    e.stopPropagation();
    if (onAdjustInventory) {
      onAdjustInventory(item);
    }
  };

  // Filter items based on search term
  const filteredItems = React.useMemo(() => {
    const searchValue = searchTerm || inputValue;
    if (!searchValue.trim()) {
      return inventory.slice(0, 100); // Show first 100 items when no search
    }

    const searchTerms = searchValue.toLowerCase().split(/\s+/);
    return inventory.filter(item => {
      const searchableText = [
        item.product_name,
        item.sku_code,
        item.product_category
      ].map(text => (text || '').toLowerCase()).join(' ');

      return searchTerms.every(term => searchableText.includes(term));
    }).slice(0, 100); // Limit results to 100 items
  }, [searchTerm, inputValue, inventory]);

  // Check if we should show the "Create new product" option
  const shouldShowCreateOption = React.useMemo(() => {
    const newProduct = getNewProductFromInput();
    if (!newProduct) return false;
    
    // Don't show if we already have exact matches in inventory
    const exactMatch = inventory.find(item => 
      item.product_name.toLowerCase() === newProduct.productName.toLowerCase() &&
      item.sku_code.toLowerCase() === newProduct.skuCode.toLowerCase()
    );
    
    return !exactMatch && filteredItems.length === 0;
  }, [inputValue, inventory, filteredItems]);

  const getDropdownPosition = () => {
    return {
      position: 'absolute' as const,
      top: '100%',
      left: '0',
      right: '0',
      marginTop: '4px'
    };
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Package className="h-4 w-4 text-gray-400" />
        </div>
        <input
          ref={searchInputRef}
          type="text"
          value={inputValue}
          onChange={(e) => {
            const newValue = e.target.value;
            setInputValue(newValue);
            setSearchTerm(newValue);
            if (!isOpen) setIsOpen(true);
            
            // Clear selection if user is typing something different
            if (selectedItem && !newValue.includes(selectedItem.product_name)) {
              onChange('', undefined);
            }
          }}
          onFocus={() => {
            setIsOpen(true);
            if (selectedItem) {
              // Keep the selected item text when focusing
              setSearchTerm('');
            } else {
              setSearchTerm(inputValue);
            }
          }}
          onBlur={(e) => {
            // Don't process blur if clicking inside dropdown
            if (dropdownRef.current?.contains(e.relatedTarget as Node)) {
              return;
            }
            
            // Use timeout to allow click events to process
            setTimeout(() => {
              if (!selectedItem && inputValue) {
                handleCreateNewProduct();
              }
            }, 200);
          }}
          placeholder={placeholder}
          disabled={disabled}
          className={`
            block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md shadow-sm
            focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500
            ${disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : ''}
            sm:text-sm
          `}
        />
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <ChevronDown 
            className={`h-4 w-4 text-gray-400 transition-transform duration-200 ${
              isOpen ? 'transform rotate-180' : ''
            }`} 
          />
        </div>
      </div>

      {isOpen && (
        <div
          className="z-[9999] w-full bg-white shadow-lg rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-hidden focus:outline-none sm:text-sm"
          style={{
            ...getDropdownPosition(),
            maxHeight: '400px',
            minWidth: '100%'
          }}
          role="listbox"
        >
          {/* Product list */}
          <div className="max-h-96 overflow-auto">
            {filteredItems.length > 0 ? (
              filteredItems.map((item) => (
                <div
                  key={item.id}
                  onClick={() => handleSelect(item)}
                  className={`
                    cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50
                    ${value === item.id ? 'text-green-900 bg-green-50' : 'text-gray-900'}
                  `}
                  role="option"
                  aria-selected={value === item.id}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center flex-1 min-w-0">
                      <Package className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {item.product_name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {item.sku_code} - {item.product_category || 'Uncategorized'} - {item.unit_type}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 ml-2 flex-shrink-0">
                      <div className={`text-xs ${
                        item.available_quantity < 0 ? 'text-red-600 font-medium' : 'text-gray-500'
                      }`}>
                        {item.available_quantity} {item.unit_type === 'box' ? 'boxes' : 'kg'}
                      </div>
                      {item.available_quantity < 0 && onAdjustInventory && (
                        <button
                          onClick={(e) => handleAdjustInventory(e, item)}
                          className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 transition-colors"
                          title="Adjust Inventory"
                        >
                          <AlertTriangle className="h-3 w-3" />
                        </button>
                      )}
                    </div>
                  </div>
                  {value === item.id && (
                    <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-green-600">
                      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </span>
                  )}
                </div>
              ))
            ) : (
              <div className="px-3 py-4 text-sm text-gray-500 text-center">
                {shouldShowCreateOption && getNewProductFromInput() ? (
                  <div
                    onClick={() => {
                      handleCreateNewProduct();
                      setIsOpen(false);
                    }}
                    className="cursor-pointer text-blue-600 hover:text-blue-800"
                  >
                    <div className="font-medium">
                      Create new product: {getNewProductFromInput()?.productName} {getNewProductFromInput()?.skuCode}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Click to create with negative inventory
                    </div>
                  </div>
                ) : (
                  'No products found'
                )}
              </div>
            )}
          </div>

          {/* Results info */}
          {filteredItems.length > 0 && (
            <div className="px-3 py-2 text-xs text-gray-500 border-t border-gray-200">
              Showing {filteredItems.length} of {inventory.length} products
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProductDropdown;
