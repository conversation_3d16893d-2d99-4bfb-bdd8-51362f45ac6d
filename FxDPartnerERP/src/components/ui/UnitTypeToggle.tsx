import React from 'react';

interface UnitTypeToggleProps {
  value: 'box' | 'loose';
  onChange: (value: 'box' | 'loose') => void;
  disabled?: boolean;
  className?: string;
}

const UnitTypeToggle: React.FC<UnitTypeToggleProps> = ({
  value,
  onChange,
  disabled = false,
  className = ''
}) => {
  const handleToggle = () => {
    if (!disabled) {
      onChange(value === 'box' ? 'loose' : 'box');
    }
  };

  return (
    <div className={`inline-flex items-center gap-2 justify-center ${className}`}>
      <span 
        className={`text-sm font-normal transition-all duration-200 cursor-pointer select-none
          ${value === 'box' ? 'text-gray-900' : 'text-gray-400'}`}
        onClick={() => !disabled && onChange('box')}
      >
        Box/Crate
      </span>
      
      <div
        className={`
          relative inline-flex h-6 w-11 items-center rounded-full bg-gray-300 p-0.5
          ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
          shadow-inner
        `}
        onClick={handleToggle}
        role="switch"
        aria-checked={value === 'loose'}
      >
        <span
          className={`
            ${value === 'box' ? 'translate-x-0' : 'translate-x-5'}
            relative inline-block h-5 w-5 transform rounded-full 
            bg-white shadow-sm
            transition duration-200 ease-out
          `}
        />
      </div>
      
      <span 
        className={`text-sm font-normal transition-all duration-200 cursor-pointer select-none
          ${value === 'loose' ? 'text-gray-900' : 'text-gray-400'}`}
        onClick={() => !disabled && onChange('loose')}
      >
        Loose
      </span>
    </div>
  );
};

export default UnitTypeToggle;
