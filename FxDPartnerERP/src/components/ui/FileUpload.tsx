import React, { useState, useRef, useCallback } from 'react';
import { Upload, X, File, Image, FileText } from 'lucide-react';
import { DocumentService, FileUploadResult, DocumentData } from '../../services/api/documentService';
import toast from 'react-hot-toast';

interface FileUploadProps {
  entityType?: string;
  entityId?: string;
  multiple?: boolean;
  maxFiles?: number;
  maxFileSize?: number; // in bytes
  acceptedFileTypes?: string[];
  onUploadComplete?: (files: FileUploadResult[] | DocumentData[]) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  className?: string;
  autoAttach?: boolean; // If true, automatically attach to entity after upload
}

interface SelectedFile extends File {
  id: string;
  preview?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  entityType,
  entityId,
  multiple = false,
  maxFiles = 10,
  maxFileSize = 50 * 1024 * 1024, // 50MB
  acceptedFileTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/csv'
  ],
  onUploadComplete,
  onError,
  disabled = false,
  className = '',
  autoAttach = false
}) => {
  const [selectedFiles, setSelectedFiles] = useState<SelectedFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Generate unique ID for files
  const generateFileId = () => Math.random().toString(36).substr(2, 9);

  // Validate file
  const validateFile = (file: File): string | null => {
    if (!acceptedFileTypes.includes(file.type)) {
      return `File type ${file.type} is not supported`;
    }
    if (file.size > maxFileSize) {
      return `File size exceeds ${DocumentService.formatFileSize(maxFileSize)} limit`;
    }
    return null;
  };

  // Handle file selection
  const handleFileSelect = useCallback((files: FileList) => {
    const newFiles: SelectedFile[] = [];
    const errors: string[] = [];

    Array.from(files).forEach((file) => {
      // Check file limit
      if (selectedFiles.length + newFiles.length >= maxFiles) {
        errors.push(`Maximum ${maxFiles} files allowed`);
        return;
      }

      // Validate file
      const error = validateFile(file);
      if (error) {
        errors.push(`${file.name}: ${error}`);
        return;
      }

      // Create file with ID and preview
      const fileWithId = Object.assign(file, {
        id: generateFileId(),
        preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined
      });

      newFiles.push(fileWithId);
    });

    if (errors.length > 0) {
      const errorMessage = errors.join(', ');
      toast.error(errorMessage);
      if (onError) onError(errorMessage);
    }

    if (newFiles.length > 0) {
      setSelectedFiles(prev => multiple ? [...prev, ...newFiles] : newFiles);
    }
  }, [selectedFiles.length, maxFiles, multiple, acceptedFileTypes, maxFileSize, onError]);

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // Handle drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handleFileSelect(files);
    }
  }, [disabled, handleFileSelect]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFileSelect(e.target.files);
    }
  };

  // Remove file
  const removeFile = (fileId: string) => {
    setSelectedFiles(prev => {
      const updated = prev.filter(f => f.id !== fileId);
      // Revoke object URL for images
      const fileToRemove = prev.find(f => f.id === fileId);
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      return updated;
    });
  };

  // Upload files
  const uploadFiles = async () => {
    if (selectedFiles.length === 0) return;

    setUploading(true);
    try {
      let results: FileUploadResult[] | DocumentData[];

      if (autoAttach && entityType && entityId) {
        // Upload and attach to entity
        if (multiple) {
          results = await DocumentService.uploadMultipleAndAttach(
            selectedFiles,
            entityType,
            entityId
          );
        } else {
          const result = await DocumentService.uploadAndAttach(
            selectedFiles[0],
            entityType,
            entityId
          );
          results = [result];
        }
      } else {
        // Just upload files
        if (multiple) {
          results = await DocumentService.uploadMultipleFiles(selectedFiles);
        } else {
          const result = await DocumentService.uploadFile(selectedFiles[0]);
          results = [result];
        }
      }

      toast.success(`Successfully uploaded ${results.length} file(s)`);
      
      if (onUploadComplete) {
        onUploadComplete(results);
      }

      // Clear selected files
      selectedFiles.forEach(file => {
        if (file.preview) {
          URL.revokeObjectURL(file.preview);
        }
      });
      setSelectedFiles([]);

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      toast.error(errorMessage);
      if (onError) onError(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  // Get file icon
  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <Image className="w-8 h-8 text-blue-500" />;
    } else if (file.type === 'application/pdf') {
      return <FileText className="w-8 h-8 text-red-500" />;
    } else {
      return <File className="w-8 h-8 text-gray-500" />;
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Drop Zone */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center transition-colors
          ${dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-gray-400'}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={acceptedFileTypes.join(',')}
          onChange={handleInputChange}
          disabled={disabled}
          className="hidden"
        />

        <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-lg font-medium text-gray-700 mb-2">
          {dragActive ? 'Drop files here' : 'Click to upload or drag and drop'}
        </p>
        <p className="text-sm text-gray-500">
          {multiple ? `Up to ${maxFiles} files` : 'Single file'} • Max {DocumentService.formatFileSize(maxFileSize)} each
        </p>
        <p className="text-xs text-gray-400 mt-1">
          Supported: PDF, Images, Excel, Word, Text files
        </p>
      </div>

      {/* Selected Files */}
      {selectedFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-medium text-gray-700">Selected Files ({selectedFiles.length})</h4>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {selectedFiles.map((file) => (
              <div key={file.id} className="bg-white border border-gray-200 rounded-lg p-3 shadow-sm">
                <div className="flex items-center space-x-3">
                  {file.preview ? (
                    <img
                      src={file.preview}
                      alt={file.name}
                      className="w-12 h-12 object-cover rounded"
                    />
                  ) : (
                    getFileIcon(file)
                  )}
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {DocumentService.formatFileSize(file.size)} • {file.type}
                    </p>
                  </div>

                  <button
                    onClick={() => removeFile(file.id)}
                    disabled={uploading}
                    className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Upload Button */}
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => {
                selectedFiles.forEach(file => {
                  if (file.preview) {
                    URL.revokeObjectURL(file.preview);
                  }
                });
                setSelectedFiles([]);
                if (fileInputRef.current) {
                  fileInputRef.current.value = '';
                }
              }}
              disabled={uploading}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Clear All
            </button>
            <button
              onClick={uploadFiles}
              disabled={uploading || selectedFiles.length === 0}
              className="min-w-[100px] px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {uploading ? 'Uploading...' : `Upload ${selectedFiles.length} file(s)`}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
