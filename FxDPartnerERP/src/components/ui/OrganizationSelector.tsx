import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { ChevronDown, Building2, Check } from 'lucide-react';

const OrganizationSelector: React.FC = () => {
  const { organization, userOrganizations, switchOrganization, loading } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [switching, setSwitching] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Don't show if user has only one organization or no organizations
  if (!userOrganizations || userOrganizations.length <= 1) {
    return null;
  }

  const handleOrganizationSwitch = async (organizationId: string) => {
    if (organizationId === organization?.id || switching) return;

    try {
      setSwitching(true);
      await switchOrganization(organizationId);
      setIsOpen(false);
      
      // Reload the page to refresh all data
      window.location.reload();
    } catch (error) {
      console.error('Failed to switch organization:', error);
      // You could show a toast notification here
    } finally {
      setSwitching(false);
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={loading || switching}
        className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <Building2 size={16} className="text-gray-500" />
        <span className="hidden sm:block max-w-32 truncate">
          {organization?.name || 'Select Organization'}
        </span>
        <ChevronDown 
          size={16} 
          className={`text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
        />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
          <div className="px-4 py-2 border-b border-gray-100">
            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
              Switch Organization
            </p>
          </div>
          
          <div className="max-h-60 overflow-y-auto">
            {userOrganizations.map((org) => (
              <button
                key={org.id}
                onClick={() => handleOrganizationSwitch(org.id)}
                disabled={switching}
                className="w-full text-left px-4 py-3 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-between"
              >
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {org.name}
                  </p>
                  <p className="text-xs text-gray-500 capitalize">
                    {org.type} • {org.status}
                  </p>
                </div>
                
                {organization?.id === org.id && (
                  <Check size={16} className="text-green-600 flex-shrink-0 ml-2" />
                )}
              </button>
            ))}
          </div>
          
          {switching && (
            <div className="px-4 py-2 border-t border-gray-100">
              <p className="text-xs text-gray-500 text-center">
                Switching organization...
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default OrganizationSelector;
