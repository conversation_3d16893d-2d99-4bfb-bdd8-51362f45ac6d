import React from 'react';

interface Column {
  key: string;
  label: string;
  render?: (value: any, item: any) => React.ReactNode;
  className?: string;
  mobileLabel?: string;
  priority?: 'high' | 'medium' | 'low'; // For responsive column priority
  hideOnMobile?: boolean;
}

interface MobileTableProps {
  columns: Column[];
  data: any[];
  onRowClick?: (item: any) => void;
  emptyState?: React.ReactNode;
  loading?: boolean;
  cardLayout?: 'default' | 'compact' | 'detailed';
}

const MobileTable: React.FC<MobileTableProps> = ({
  columns,
  data,
  onRowClick,
  emptyState,
  loading,
  cardLayout = 'detailed'
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-pulse">
          <div className="flex space-x-4">
            <div className="rounded-full bg-gray-300 h-10 w-10"></div>
            <div className="flex-1 space-y-2 py-1">
              <div className="h-4 bg-gray-300 rounded w-3/4"></div>
              <div className="h-4 bg-gray-300 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        {emptyState || (
          <div className="py-12 text-center text-gray-500">
            <p>No data available</p>
          </div>
        )}
      </div>
    );
  }

  // Filter columns for mobile based on priority and hideOnMobile
  const mobileColumns = columns.filter(col => !col.hideOnMobile);
  const highPriorityColumns = mobileColumns.filter(col => col.priority === 'high' || !col.priority);

  return (
    <div className="bg-white shadow-sm rounded-lg overflow-hidden">
      {/* Desktop Table View */}
      <div className="hidden lg:block overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  scope="col"
                  className={`px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider ${column.className || 'table-col-md'}`}
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((item, index) => (
              <tr
                key={index}
                className={`hover:bg-gray-50 transition-colors duration-150 ${onRowClick ? 'cursor-pointer' : ''}`}
                onClick={() => onRowClick?.(item)}
              >
                {columns.map((column) => (
                  <td key={column.key} className={`px-6 py-4 text-center ${column.className || 'table-col-md'}`}>
                    <div className="break-words">
                      {column.render
                        ? column.render(item[column.key], item)
                        : item[column.key] || ''
                      }
                    </div>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Tablet View */}
      <div className="hidden md:block lg:hidden overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
                {highPriorityColumns.map((column) => (
                  <th
                    key={column.key}
                    scope="col"
                    className={`px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider ${column.className || 'table-col-md'}`}
                  >
                    {column.label}
                  </th>
                ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((item, index) => (
              <tr
                key={index}
                className={`hover:bg-gray-50 transition-colors duration-150 ${onRowClick ? 'cursor-pointer' : ''}`}
                onClick={() => onRowClick?.(item)}
              >
                {highPriorityColumns.map((column) => (
                  <td key={column.key} className={`px-4 py-3 text-center ${column.className || 'table-col-md'}`}>
                    <div className="break-words">
                      {column.render
                        ? column.render(item[column.key], item)
                        : item[column.key] || ''
                      }
                    </div>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden">
        {cardLayout === 'compact' ? (
          // Compact Card Layout
          <div className="divide-y divide-gray-200">
            {data.map((item, index) => (
              <div
                key={index}
                className={`p-4 hover:bg-gray-50 transition-colors duration-150 ${
                  onRowClick ? 'cursor-pointer active:bg-gray-100' : ''
                }`}
                onClick={() => onRowClick?.(item)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="break-words word-break break-all">
                      {columns[0] && columns[0].render 
                        ? columns[0].render(item[columns[0].key], item)
                        : item[columns[0].key]
                      }
                    </div>
                  </div>
                  {columns.find(col => col.key === 'actions') && (
                    <div className="ml-4 flex-shrink-0">
                      {columns.find(col => col.key === 'actions')?.render?.(item.actions, item)}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          // Detailed Card Layout
          <div className="p-4 space-y-4">
            {data.map((item, index) => (
              <div
                key={index}
                className={`bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden transition-all duration-200 ${
                  onRowClick ? 'cursor-pointer hover:shadow-md hover:border-gray-300 active:shadow-lg transform hover:-translate-y-0.5' : ''
                }`}
                onClick={() => onRowClick?.(item)}
              >
                {/* Card Header */}
                {columns[0] && (
                  <div className="bg-gradient-to-r from-gray-50 to-white px-4 py-4 border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="break-words word-break break-all">
                          {columns[0].render 
                            ? columns[0].render(item[columns[0].key], item)
                            : item[columns[0].key]
                          }
                        </div>
                      </div>
                      {/* Status badges in header for quick identification */}
                      {columns.find(col => col.key === 'status') && (
                        <div className="ml-4 flex-shrink-0">
                          {columns.find(col => col.key === 'status')?.render?.(item.status, item)}
                        </div>
                      )}
                    </div>
                  </div>
                )}
                
                {/* Card Body */}
                <div className="p-4">
                  <div className="grid grid-cols-1 gap-4">
                    {/* Primary Information Grid */}
                    <div className="grid grid-cols-2 gap-4">
                      {mobileColumns.slice(1).filter(col => 
                        col.key !== 'actions' && 
                        col.key !== 'status' && 
                        (col.priority === 'high' || !col.priority)
                      ).slice(0, 4).map((column) => {
                        const renderedContent = column.render 
                          ? column.render(item[column.key], item)
                          : item[column.key];
                        
                        if (!renderedContent && renderedContent !== 0) return null;
                        
                        return (
                          <div key={column.key} className="min-w-0">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">
                              {column.mobileLabel || column.label}
                            </div>
                            <div className="text-sm text-gray-900 font-medium break-words word-break break-all">
                              {renderedContent}
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    {/* Secondary Information - Full Width */}
                    {mobileColumns.filter(col => 
                      col.key !== 'actions' && 
                      col.key !== 'status' && 
                      col.priority === 'medium'
                    ).map((column) => {
                      const renderedContent = column.render 
                        ? column.render(item[column.key], item)
                        : item[column.key];
                      
                      if (!renderedContent && renderedContent !== 0) return null;
                      
                      return (
                        <div key={column.key} className="border-t border-gray-100 pt-3">
                          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
                            {column.mobileLabel || column.label}
                          </div>
                          <div className="text-sm text-gray-900 break-words word-break break-all">
                            {renderedContent}
                          </div>
                        </div>
                      );
                    })}

                    {/* Actions Footer */}
                    {columns.find(col => col.key === 'actions') && (
                      <div className="border-t border-gray-100 pt-3 mt-2">
                        <div className="flex justify-center space-x-4">
                          {columns.find(col => col.key === 'actions')?.render?.(item.actions, item)}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileTable;
