import React, { useState } from 'react';
import { X, FileText } from 'lucide-react';

interface PurchaseRecordCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (closureType: 'partial_closure' | 'full_closure', closureNotes?: string) => void;
  isSubmitting: boolean;
}

const PurchaseRecordCreationModal: React.FC<PurchaseRecordCreationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  isSubmitting
}) => {
  const [closureType, setClosureType] = useState<'partial_closure' | 'full_closure'>('partial_closure');
  const [closureNotes, setClosureNotes] = useState('');

  const handleConfirm = () => {
    onConfirm(closureType, closureType === 'full_closure' ? closureNotes : undefined);
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setClosureType('partial_closure');
      setClosureNotes('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 sm:p-6 border-b">
          <div className="flex items-center">
            <FileText className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Create Purchase Record</h3>
          </div>
          {!isSubmitting && (
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>

        <div className="p-4 sm:p-6">
          <div className="mb-6">
            <p className="text-sm text-gray-600 mb-4">
              How do you want to create this Purchase Record?
            </p>
            
            <div className="space-y-3">
              <label className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="closureType"
                  value="partial_closure"
                  checked={closureType === 'partial_closure'}
                  onChange={(e) => setClosureType(e.target.value as 'partial_closure')}
                  className="mt-1 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                  disabled={isSubmitting}
                />
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">Partial Closure (Editable)</div>
                  <div className="text-xs text-gray-500 mt-1">
                    Record can be edited later but is treated as completed for finance calculations.
                  </div>
                </div>
              </label>

              <label className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="closureType"
                  value="full_closure"
                  checked={closureType === 'full_closure'}
                  onChange={(e) => setClosureType(e.target.value as 'full_closure')}
                  className="mt-1 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                  disabled={isSubmitting}
                />
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">Full Closure (Read-only)</div>
                  <div className="text-xs text-gray-500 mt-1">
                    Record cannot be edited once created. Choose this for final records.
                  </div>
                </div>
              </label>
            </div>
          </div>

          {closureType === 'full_closure' && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Closure Notes (Optional)
              </label>
              <textarea
                value={closureNotes}
                onChange={(e) => setClosureNotes(e.target.value)}
                rows={3}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                placeholder="Add notes about why this record is being fully closed..."
                disabled={isSubmitting}
              />
            </div>
          )}

          <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed order-2 sm:order-1"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleConfirm}
              disabled={isSubmitting}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-400 disabled:cursor-not-allowed order-1 sm:order-2"
            >
              {isSubmitting ? 'Creating Record...' : 'Create Purchase Record'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PurchaseRecordCreationModal;
