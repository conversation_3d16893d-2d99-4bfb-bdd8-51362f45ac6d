import React, { useState } from 'react';
import { X, AlertTriangle, Settings } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { inventoryService } from '../../services/api/inventoryService';

interface InventoryItem {
  id: string;
  productId: string;
  productName: string;
  skuId: string;
  skuCode: string;
  unitType: 'box' | 'loose';
  currentQuantity: number;
  currentWeight: number;
}

interface ManualAdjustmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: InventoryItem | null;
  onSuccess: () => void;
}

const ADJUSTMENT_TYPES = [
  { value: 'add', label: 'Add Stock', description: 'Increase inventory quantity' },
  { value: 'subtract', label: 'Subtract Stock', description: 'Decrease inventory quantity' },
  { value: 'set', label: 'Set Stock', description: 'Set exact inventory quantity' }
];

const ADJUSTMENT_REASONS = [
  'Stock count correction',
  'Damaged items found',
  'Items found in storage',
  'System error correction',
  'Transfer from another location',
  'Return from customer',
  'Other'
];

const ManualAdjustmentModal: React.FC<ManualAdjustmentModalProps> = ({
  isOpen,
  onClose,
  item,
  onSuccess
}) => {
  const [adjustmentType, setAdjustmentType] = useState<'add' | 'subtract' | 'set'>('add');
  const [quantityChange, setQuantityChange] = useState<number>(0);
  const [weightChange, setWeightChange] = useState<number>(0);
  const [reason, setReason] = useState<string>('');
  const [customReason, setCustomReason] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!item || !quantityChange || !reason) {
      toast.error('Please fill in all required fields');
      return;
    }

    const finalReason = reason === 'Other' ? customReason : reason;
    if (!finalReason) {
      toast.error('Please provide a reason for the adjustment');
      return;
    }

    setLoading(true);
    
    try {
      await inventoryService.manualAdjust({
        product_id: item.productId,
        sku_id: item.skuId,
        adjustment_type: adjustmentType,
        quantity_change: quantityChange,
        weight_change: weightChange || undefined,
        reason: finalReason,
        notes: notes || undefined
      });

      toast.success('Manual adjustment completed successfully');
      onSuccess();
      handleClose();
    } catch (error) {
      console.error('Error performing manual adjustment:', error);
      toast.error('Failed to perform manual adjustment');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setAdjustmentType('add');
    setQuantityChange(0);
    setWeightChange(0);
    setReason('');
    setCustomReason('');
    setNotes('');
    onClose();
  };

  const calculateNewQuantity = () => {
    if (!item) return 0;
    
    switch (adjustmentType) {
      case 'add':
        return item.currentQuantity + quantityChange;
      case 'subtract':
        return item.currentQuantity - quantityChange;
      case 'set':
        return quantityChange;
      default:
        return item.currentQuantity;
    }
  };

  if (!isOpen || !item) return null;

  const newQuantity = calculateNewQuantity();

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-blue-100 text-blue-600">
                <Settings className="h-5 w-5" />
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900">Manual Adjustment</h3>
                <p className="text-sm text-gray-500">
                  {item.productName} - {item.skuCode}
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="px-6 py-4 space-y-4">
          {/* Current Stock Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
            <div className="flex items-center">
              <AlertTriangle className="h-4 w-4 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-blue-800">Current Stock</span>
            </div>
            <div className="mt-1 text-sm text-blue-700">
              {item.currentQuantity} {item.unitType === 'box' ? 'boxes' : 'kg'}
              {item.currentWeight > 0 && ` (${item.currentWeight} kg total weight)`}
            </div>
          </div>

          {/* Adjustment Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Adjustment Type *
            </label>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              {ADJUSTMENT_TYPES.map((type) => (
                <label key={type.value} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="radio"
                    name="adjustmentType"
                    value={type.value}
                    checked={adjustmentType === type.value}
                    onChange={(e) => setAdjustmentType(e.target.value as 'add' | 'subtract' | 'set')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <div className="ml-3">
                    <div className="text-sm font-medium text-gray-900">{type.label}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Quantity Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {adjustmentType === 'set' ? 'New Quantity *' : 'Quantity Change *'}
            </label>
            <input
              type="number"
              min="0"
              value={quantityChange || ''}
              onChange={(e) => setQuantityChange(parseInt(e.target.value) || 0)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder={adjustmentType === 'set' ? 'Enter new quantity' : 'Enter quantity to adjust'}
              required
            />
          </div>

          {/* Weight Input (optional) */}
          {item.unitType === 'loose' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {adjustmentType === 'set' ? 'New Weight (kg)' : 'Weight Change (kg)'}
              </label>
              <input
                type="number"
                min="0"
                step="0.001"
                value={weightChange || ''}
                onChange={(e) => setWeightChange(parseFloat(e.target.value) || 0)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Optional"
              />
            </div>
          )}

          {/* Reason Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Reason *
            </label>
            <select
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Select reason</option>
              {ADJUSTMENT_REASONS.map((r) => (
                <option key={r} value={r}>
                  {r}
                </option>
              ))}
            </select>
          </div>

          {/* Custom Reason Input */}
          {reason === 'Other' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Custom Reason *
              </label>
              <input
                type="text"
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter custom reason"
                required
              />
            </div>
          )}

          {/* Additional Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Additional Notes
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Optional additional details..."
            />
          </div>

          {/* Impact Preview */}
          {quantityChange > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
              <div className="text-sm font-medium text-yellow-800 mb-1">Impact Preview</div>
              <div className="text-sm text-yellow-700">
                Current: {item.currentQuantity} → After adjustment: {newQuantity}
                {newQuantity < 0 && (
                  <span className="text-red-600 font-medium"> (Warning: Negative stock!)</span>
                )}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || !quantityChange || !reason || (reason === 'Other' && !customReason)}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Adjusting...' : 'Apply Adjustment'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ManualAdjustmentModal;
