import React, { useState } from 'react';
import { X, AlertTriangle, Trash2 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { inventoryService } from '../../services/api/inventoryService';

interface InventoryItem {
  id: string;
  productId: string;
  productName: string;
  skuId: string;
  skuCode: string;
  unitType: 'box' | 'loose';
  currentQuantity: number;
  currentWeight: number;
}

interface MarkDumpModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: InventoryItem | null;
  onSuccess: () => void;
}

const DUMP_REASONS = [
  { value: 'damaged', label: 'Damaged' },
  { value: 'expired', label: 'Expired' },
  { value: 'quality_issue', label: 'Quality Issue' },
  { value: 'contaminated', label: 'Contaminated' },
  { value: 'other', label: 'Other' }
];

const MarkDumpModal: React.FC<MarkDumpModalProps> = ({
  isOpen,
  onClose,
  item,
  onSuccess
}) => {
  const [quantity, setQuantity] = useState<number>(0);
  const [weight, setWeight] = useState<number>(0);
  const [reason, setReason] = useState<string>('');
  const [reasonNotes, setReasonNotes] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!item || !quantity || !reason) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (quantity > item.currentQuantity) {
      toast.error(`Quantity cannot exceed available stock (${item.currentQuantity})`);
      return;
    }

    setLoading(true);
    
    try {
      await inventoryService.markAsDump({
        product_id: item.productId,
        sku_id: item.skuId,
        quantity,
        weight: weight || undefined,
        reason,
        reason_notes: reasonNotes || undefined
      });

      toast.success('Inventory marked as dump successfully');
      onSuccess();
      handleClose();
    } catch (error) {
      console.error('Error marking inventory as dump:', error);
      toast.error('Failed to mark inventory as dump');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setQuantity(0);
    setWeight(0);
    setReason('');
    setReasonNotes('');
    onClose();
  };

  if (!isOpen || !item) return null;

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-red-100 text-red-600">
                <Trash2 className="h-5 w-5" />
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900">Mark as Dump</h3>
                <p className="text-sm text-gray-500">
                  {item.productName} - {item.skuCode}
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="px-6 py-4 space-y-4">
          {/* Current Stock Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
            <div className="flex items-center">
              <AlertTriangle className="h-4 w-4 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-blue-800">Current Stock</span>
            </div>
            <div className="mt-1 text-sm text-blue-700">
              {item.currentQuantity} {item.unitType === 'box' ? 'boxes' : 'kg'}
              {item.currentWeight > 0 && ` (${item.currentWeight} kg total weight)`}
            </div>
          </div>

          {/* Quantity Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Quantity to Dump *
            </label>
            <input
              type="number"
              min="1"
              max={item.currentQuantity}
              value={quantity || ''}
              onChange={(e) => setQuantity(parseInt(e.target.value) || 0)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
              placeholder={`Max: ${item.currentQuantity}`}
              required
            />
          </div>

          {/* Weight Input (optional) */}
          {item.unitType === 'loose' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Weight (kg)
              </label>
              <input
                type="number"
                min="0"
                step="0.001"
                value={weight || ''}
                onChange={(e) => setWeight(parseFloat(e.target.value) || 0)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                placeholder="Optional"
              />
            </div>
          )}

          {/* Reason Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Reason *
            </label>
            <select
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
              required
            >
              <option value="">Select reason</option>
              {DUMP_REASONS.map((r) => (
                <option key={r.value} value={r.value}>
                  {r.label}
                </option>
              ))}
            </select>
          </div>

          {/* Additional Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Additional Notes
            </label>
            <textarea
              value={reasonNotes}
              onChange={(e) => setReasonNotes(e.target.value)}
              rows={3}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
              placeholder="Optional additional details..."
            />
          </div>

          {/* Impact Preview */}
          {quantity > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
              <div className="text-sm font-medium text-yellow-800 mb-1">Impact Preview</div>
              <div className="text-sm text-yellow-700">
                Current: {item.currentQuantity} → After dump: {item.currentQuantity - quantity}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || !quantity || !reason}
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Marking as Dump...' : 'Mark as Dump'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MarkDumpModal;
