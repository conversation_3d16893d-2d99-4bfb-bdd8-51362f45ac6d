import React, { useState } from 'react';
import { Package, X, ArrowRight } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { markSalesOrderGRNComplete } from '../../services/api';

interface SalesOrderData {
  id: string;
  order_number: string;
  customer: {
    name: string;
  };
  total_amount: number;
  items?: Array<{
    id: string;
    product_name: string;
    sku_code: string;
    quantity: number;
    unit_type: string;
    unit_price: number;
    total_price: number;
  }>;
}

interface GRNCompleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  salesOrder: SalesOrderData;
  onSuccess: () => void;
  onOpenReturnPDD?: () => void;
}

const GRNCompleteModal: React.FC<GRNCompleteModalProps> = ({
  isOpen,
  onClose,
  salesOrder,
  onSuccess,
  onOpenReturnPDD
}) => {
  const [showReturnPDD, setShowReturnPDD] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showReturnPDDModal, setShowReturnPDDModal] = useState(false);

  const handleMarkGRNComplete = async () => {
    setIsProcessing(true);
    try {
      await markSalesOrderGRNComplete(salesOrder.id);
      toast.success('Order marked as GRN Complete successfully');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error marking GRN complete:', error);
      toast.error('Failed to mark as GRN Complete');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRaiseReturnPDD = () => {
    onClose(); // Close GRN modal first
    // Trigger the parent to open Return/PDD modal
    if (onOpenReturnPDD) {
      onOpenReturnPDD();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-purple-100">
                <Package className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">
                  GRN Complete - {salesOrder.order_number}
                </h3>
                <p className="text-sm text-gray-500">
                  Customer: {salesOrder.customer.name}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Order Summary */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Order Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Order Number:</span>
                <span className="ml-2 font-medium">{salesOrder.order_number}</span>
              </div>
              <div>
                <span className="text-gray-500">Total Amount:</span>
                <span className="ml-2 font-medium">₹{salesOrder.total_amount?.toLocaleString()}</span>
              </div>
              <div>
                <span className="text-gray-500">Customer:</span>
                <span className="ml-2 font-medium">{salesOrder.customer.name}</span>
              </div>
              <div>
                <span className="text-gray-500">Items:</span>
                <span className="ml-2 font-medium">{salesOrder.items?.length || 0} items</span>
              </div>
            </div>
          </div>

          {/* Return/PDD Option */}
          <div className="mb-6">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showReturnPDD}
                onChange={(e) => setShowReturnPDD(e.target.checked)}
                className="mr-3 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
              />
              <span className="text-sm font-medium text-gray-700">
                Raise Return/PDD
              </span>
            </label>
            <p className="text-xs text-gray-500 mt-1 ml-7">
              Check this option if you need to process returns or post-delivery discounts
            </p>
          </div>

          {/* Information Box */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <Package className="h-5 w-5 text-blue-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  What happens when you mark as GRN Complete?
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <ul className="list-disc list-inside space-y-1">
                    <li>The order status will be updated to "GRN Complete"</li>
                    <li>This indicates goods have been received and verified</li>
                    {showReturnPDD && (
                      <li className="text-orange-700 font-medium">
                        You will be able to process returns and post-delivery discounts
                      </li>
                    )}
                    <li>The order will be ready for final closure</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled={isProcessing}
            >
              Cancel
            </button>
            
            {showReturnPDD ? (
              <button
                type="button"
                onClick={handleRaiseReturnPDD}
                disabled={isProcessing}
                className="px-4 py-2 bg-orange-600 text-white rounded-md text-sm font-medium hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center"
              >
                {isProcessing ? (
                  'Processing...'
                ) : (
                  <>
                    Proceed to Return/PDD
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </>
                )}
              </button>
            ) : (
              <button
                type="button"
                onClick={handleMarkGRNComplete}
                disabled={isProcessing}
                className="px-4 py-2 bg-purple-600 text-white rounded-md text-sm font-medium hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isProcessing ? 'Processing...' : 'Mark as GRN Complete'}
              </button>
            )}
          </div>
        </div>
      </div>

    </div>
  );
};

export default GRNCompleteModal;
