import React, { useState, useEffect } from 'react';
import { X, Download, Eye, FileText, Image } from 'lucide-react';
import { PaymentDocument } from '../../types/payment.types';

interface DocumentViewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  documents: PaymentDocument[];
  title?: string;
}

const DocumentViewerModal: React.FC<DocumentViewerModalProps> = ({
  isOpen,
  onClose,
  documents,
  title = 'Documents'
}) => {
  const [selectedDocument, setSelectedDocument] = useState<PaymentDocument | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && documents.length > 0) {
      setSelectedDocument(documents[0]);
    }
  }, [isOpen, documents]);

  const handleDocumentSelect = (document: PaymentDocument) => {
    setSelectedDocument(document);
  };

  const handleDownload = async (doc: PaymentDocument) => {
    if (!doc.file_url) return;
    
    try {
      setLoading(true);
      const response = await fetch(doc.file_url);
      const blob = await response.blob();
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = doc.file_name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading document:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith('image/')) {
      return <Image className="h-4 w-4" />;
    } else if (contentType === 'application/pdf') {
      return <FileText className="h-4 w-4" />;
    }
    return <FileText className="h-4 w-4" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderDocumentPreview = (document: PaymentDocument) => {
    if (!document.file_url) {
      return (
        <div className="flex items-center justify-center h-full bg-gray-100 rounded-lg">
          <div className="text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">Preview not available</p>
          </div>
        </div>
      );
    }

    if (document.content_type.startsWith('image/')) {
      return (
        <div className="flex items-center justify-center h-full bg-gray-100 rounded-lg">
          <img
            src={document.file_url}
            alt={document.file_name}
            className="max-w-full max-h-full object-contain rounded-lg"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              target.parentElement!.innerHTML = `
                <div class="text-center">
                  <div class="h-12 w-12 text-gray-400 mx-auto mb-2">📷</div>
                  <p class="text-gray-500">Image preview not available</p>
                </div>
              `;
            }}
          />
        </div>
      );
    } else if (document.content_type === 'application/pdf') {
      return (
        <div className="h-full bg-gray-100 rounded-lg">
          <iframe
            src={document.file_url}
            className="w-full h-full rounded-lg"
            title={document.file_name}
            onError={() => {
              console.error('PDF preview failed');
            }}
          />
        </div>
      );
    }

    return (
      <div className="flex items-center justify-center h-full bg-gray-100 rounded-lg">
        <div className="text-center">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-500">Preview not available for this file type</p>
          <button
            onClick={() => handleDownload(document)}
            disabled={loading}
            className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
          >
            {loading ? 'Downloading...' : 'Download to View'}
          </button>
        </div>
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onClick={onClose}></div>
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all w-full max-w-6xl sm:my-8 sm:align-middle sm:w-full">
          {/* Header */}
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                {title} ({documents.length})
              </h3>
              <button
                type="button"
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          {documents.length === 0 ? (
            <div className="px-4 py-8 sm:px-6">
              <div className="text-center">
                <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Documents Found</h3>
                <p className="text-gray-500">There are no documents attached to this payment.</p>
              </div>
            </div>
          ) : (
            <div className="flex h-96 sm:h-[600px]">
              {/* Document List Sidebar */}
              <div className="w-1/3 border-r border-gray-200 bg-gray-50 overflow-y-auto">
                <div className="p-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Documents</h4>
                  <div className="space-y-2">
                    {documents.map((document) => (
                      <button
                        key={document.id}
                        onClick={() => handleDocumentSelect(document)}
                        className={`w-full text-left p-3 rounded-lg border transition-colors ${
                          selectedDocument?.id === document.id
                            ? 'bg-blue-50 border-blue-200 text-blue-900'
                            : 'bg-white border-gray-200 hover:bg-gray-50'
                        }`}
                      >
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-1">
                            {getFileIcon(document.content_type)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">
                              {document.display_name || document.file_name}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              {formatFileSize(document.file_size)}
                            </p>
                            {document.uploader && (
                              <p className="text-xs text-gray-400 mt-1">
                                By {document.uploader.first_name}
                              </p>
                            )}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Document Preview */}
              <div className="flex-1 flex flex-col">
                {selectedDocument && (
                  <>
                    {/* Document Header */}
                    <div className="px-4 py-3 border-b border-gray-200 bg-white">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          {getFileIcon(selectedDocument.content_type)}
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">
                              {selectedDocument.display_name || selectedDocument.file_name}
                            </h4>
                            <p className="text-xs text-gray-500">
                              {formatFileSize(selectedDocument.file_size)} • {selectedDocument.content_type}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {selectedDocument.file_url && (
                            <button
                              onClick={() => window.open(selectedDocument.file_url, '_blank')}
                              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                              title="Open in new tab"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                          )}
                          <button
                            onClick={() => handleDownload(selectedDocument)}
                            disabled={loading}
                            className="p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:text-gray-300"
                            title="Download"
                          >
                            <Download className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Document Content */}
                    <div className="flex-1 p-4">
                      {renderDocumentPreview(selectedDocument)}
                    </div>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              onClick={onClose}
              className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentViewerModal;
