import React from 'react';
import { Alert<PERSON>riangle, Clock, CheckCircle, Info, X } from 'lucide-react';
import { AlertPanelProps } from '../../types/dashboard.types';

const AlertPanel: React.FC<AlertPanelProps> = ({
  alerts,
  maxAlerts = 5,
  onAlertClick,
  loading = false
}) => {
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'medium':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'low':
        return <Info className="h-5 w-5 text-blue-500" />;
      default:
        return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
  };

  const getSeverityStyles = (severity: string) => {
    switch (severity) {
      case 'high':
        return {
          bg: 'bg-red-50',
          border: 'border-l-red-400',
          text: 'text-red-800'
        };
      case 'medium':
        return {
          bg: 'bg-yellow-50',
          border: 'border-l-yellow-400',
          text: 'text-yellow-800'
        };
      case 'low':
        return {
          bg: 'bg-blue-50',
          border: 'border-l-blue-400',
          text: 'text-blue-800'
        };
      default:
        return {
          bg: 'bg-green-50',
          border: 'border-l-green-400',
          text: 'text-green-800'
        };
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  if (loading) {
    return (
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
          <div className="h-5 w-5 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg animate-pulse">
              <div className="h-5 w-5 bg-gray-200 rounded mt-0.5"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-1/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const displayAlerts = alerts.slice(0, maxAlerts);

  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Critical Alerts</h2>
        <div className="flex items-center space-x-2">
          {alerts.length > 0 && (
            <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
              {alerts.length}
            </span>
          )}
        </div>
      </div>

      {alerts.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-8 text-gray-500">
          <CheckCircle className="h-12 w-12 text-green-500 mb-3" />
          <p className="text-lg font-medium text-gray-600">All Clear!</p>
          <p className="text-sm text-gray-500">No critical alerts at this time</p>
        </div>
      ) : (
        <div className="space-y-4">
          {displayAlerts.map((alert, index) => {
            const styles = getSeverityStyles(alert.severity);
            return (
              <div
                key={index}
                className={`flex items-start space-x-3 p-4 ${styles.bg} rounded-lg border-l-4 ${styles.border} cursor-pointer hover:shadow-sm transition-shadow duration-200`}
                onClick={() => onAlertClick?.(alert)}
              >
                <div className="mt-0.5">
                  {getSeverityIcon(alert.severity)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <p className={`font-medium ${styles.text}`}>
                        {alert.title}
                      </p>
                      <p className={`text-sm ${styles.text} opacity-80 mt-1`}>
                        {alert.message}
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        {formatTimestamp(alert.timestamp)}
                      </p>
                    </div>
                    {onAlertClick && (
                      <button
                        className="ml-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle dismiss alert if needed
                        }}
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            );
          })}

          {alerts.length > maxAlerts && (
            <div className="text-center pt-4 border-t border-gray-100">
              <button className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200">
                View {alerts.length - maxAlerts} more alert{alerts.length - maxAlerts > 1 ? 's' : ''}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AlertPanel;
