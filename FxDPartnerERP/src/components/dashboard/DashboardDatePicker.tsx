import React, { useState, useEffect } from 'react';
import ReactDatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Calendar, X } from 'lucide-react';

interface DashboardDatePickerProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const DashboardDatePicker: React.FC<DashboardDatePickerProps> = ({
  value,
  onChange,
  placeholder = 'Select date',
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Convert string value to Date object
  const dateValue = value ? new Date(value) : null;

  // Handle date change
  const handleChange = (date: Date | null) => {
    if (date) {
      const selectedDate = new Date(date);
      selectedDate.setHours(5, 30, 0, 0);
      onChange(selectedDate.toISOString());
    } else {
      onChange('');
    }
    
    // Close modal after selection on mobile
    if (isMobile) {
      setIsOpen(false);
    }
  };

  // Handle input click
  const handleInputClick = () => {
    if (isMobile) {
      setIsOpen(true);
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsOpen(false);
  };

  // Format date for display
  const displayValue = dateValue ? dateValue.toLocaleDateString('en-IN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }) : '';

  if (isMobile) {
    return (
      <>
        {/* Mobile Input */}
        <div className={`relative ${className}`}>
          <input
            type="text"
            value={displayValue}
            placeholder={placeholder}
            onClick={handleInputClick}
            readOnly
            className="block w-full border border-gray-300 rounded-lg shadow-sm py-2 px-3 pr-10 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 text-xs cursor-pointer text-gray-900 bg-white"
          />
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <Calendar className="h-4 w-4 text-gray-400" />
          </div>
        </div>

        {/* Mobile Modal */}
        {isOpen && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white rounded-lg shadow-xl max-w-sm w-full mx-4">
              {/* Modal Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Select Date</h3>
                <button
                  onClick={handleModalClose}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>
              
              {/* Modal Body */}
              <div className="p-4">
                <ReactDatePicker
                  selected={dateValue}
                  onChange={handleChange}
                  dateFormat="dd/MM/yyyy"
                  inline
                  calendarClassName="dashboard-mobile-calendar"
                  showPopperArrow={false}
                />
              </div>
              
              {/* Modal Footer */}
              <div className="p-4 border-t border-gray-200">
                <button
                  onClick={handleModalClose}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors duration-200"
                >
                  Done
                </button>
              </div>
            </div>
          </div>
        )}
      </>
    );
  }

  // Desktop DatePicker
  return (
    <div className={`relative ${className}`}>
      <ReactDatePicker
        selected={dateValue}
        onChange={handleChange}
        dateFormat="dd/MM/yyyy"
        placeholderText={placeholder}
        className="block w-full border border-gray-300 rounded-lg shadow-sm py-2 px-3 pr-10 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200 text-xs text-gray-900 bg-white"
        wrapperClassName="w-full"
        popperClassName="react-datepicker-popper dashboard-desktop-popper"
        showPopperArrow={false}
        autoComplete="off"
        calendarClassName="dashboard-desktop-calendar"
        popperPlacement="bottom-start"
        popperProps={{
          strategy: 'absolute',
        }}
      />
      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
        <Calendar className="h-4 w-4 text-gray-400" />
      </div>
    </div>
  );
};

export default DashboardDatePicker;
