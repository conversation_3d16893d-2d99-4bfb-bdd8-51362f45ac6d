import React from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { KPICardProps } from '../../types/dashboard.types';

const KPICard: React.FC<KPICardProps> = ({ 
  title, 
  value, 
  change, 
  trend = 'neutral', 
  icon, 
  subtitle = "vs last period",
  loading = false,
  color = 'blue'
}) => {
  const colorClasses = {
    green: {
      icon: 'bg-green-50 text-green-600',
      trend: 'text-green-600',
      border: 'border-green-200'
    },
    blue: {
      icon: 'bg-blue-50 text-blue-600',
      trend: 'text-blue-600',
      border: 'border-blue-200'
    },
    orange: {
      icon: 'bg-orange-50 text-orange-600',
      trend: 'text-orange-600',
      border: 'border-orange-200'
    },
    purple: {
      icon: 'bg-purple-50 text-purple-600',
      trend: 'text-purple-600',
      border: 'border-purple-200'
    },
    red: {
      icon: 'bg-red-50 text-red-600',
      trend: 'text-red-600',
      border: 'border-red-200'
    }
  };

  const trendColors = {
    up: 'text-green-600',
    down: 'text-red-600',
    neutral: 'text-gray-500'
  };

  const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : Minus;

  if (loading) {
    return (
      <div className="bg-white p-3 rounded-lg shadow-sm border border-gray-100 animate-pulse">
        <div className="flex justify-between items-start mb-2">
          <div className="flex-1">
            <div className="h-3 bg-gray-200 rounded w-20 mb-1"></div>
            <div className="h-6 bg-gray-200 rounded w-24"></div>
          </div>
          <div className="h-8 w-8 bg-gray-200 rounded-lg"></div>
        </div>
        <div className="flex items-center justify-between">
          <div className="h-3 bg-gray-200 rounded w-12"></div>
          <div className="h-2 bg-gray-200 rounded w-16"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white p-3 rounded-lg shadow-sm border transition-all duration-300 hover:shadow-md hover:${colorClasses[color].border} ${colorClasses[color].border}`}>
      <div className="flex justify-between items-start mb-2">
        <div className="flex-1">
          <p className="text-xs font-medium text-gray-500 mb-1">{title}</p>
          <p className="text-xl font-bold text-gray-800">
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
        </div>
        <div className={`h-8 w-8 flex items-center justify-center rounded-lg ${colorClasses[color].icon}`}>
          {React.cloneElement(icon as React.ReactElement, { className: 'h-4 w-4' })}
        </div>
      </div>
      
      {change && (
        <div className="flex items-center">
          <TrendIcon className={`h-3 w-3 mr-1 ${trendColors[trend]}`} />
          <span className={`text-xs font-semibold ${trendColors[trend]}`}>
            {change}
          </span>
        </div>
      )}
    </div>
  );
};

export default KPICard;
