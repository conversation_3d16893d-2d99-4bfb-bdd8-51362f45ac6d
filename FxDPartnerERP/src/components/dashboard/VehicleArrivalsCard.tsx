import React from 'react';
import { ArrivalView } from '../../types/dashboard.types';

interface VehicleArrivalsCardProps {
  data: ArrivalView | null;
  loading?: boolean;
}

const VehicleArrivalsCard: React.FC<VehicleArrivalsCardProps> = ({ data, loading = false }) => {
  if (loading) {
    return (
      <div className="bg-white p-3 rounded-lg shadow-sm border border-gray-100 animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
        <div className="grid grid-cols-3 gap-1 sm:gap-2">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="border border-gray-200 rounded p-1 sm:p-2 h-14 sm:h-16 flex flex-col justify-center items-center space-y-1">
              <div className="h-2 sm:h-3 bg-gray-200 rounded w-12 sm:w-16"></div>
              <div className="h-3 sm:h-4 bg-gray-200 rounded w-8 sm:w-12"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
      {/* Header */}
      <h2 className="text-sm font-semibold text-gray-800 mb-2">Vehicle Overview</h2>
      
      {/* Grid Layout - 3 columns, 2 rows (consistent across all devices) */}
      <div className="grid grid-cols-3 gap-1 sm:gap-2 h-auto">
        
        {/* Top Left: Total Vehicles */}
        <div className="border border-gray-200 rounded p-1 sm:p-2 flex flex-col justify-center items-center bg-gray-50 min-h-[60px] sm:min-h-[70px]">
          <div className="text-[10px] sm:text-xs font-medium text-gray-500 mb-1 text-center leading-tight">
            Total<br />Vehicles
          </div>
          <div className="text-lg sm:text-xl font-bold text-gray-800">
            {data?.totalArrivals || 0}
          </div>
        </div>

        {/* Top Middle: Unloading Pending */}
        <div className="border border-gray-200 rounded p-1 sm:p-2 flex flex-col justify-center items-center min-h-[60px] sm:min-h-[70px]">
          <div className="text-[10px] sm:text-xs font-medium text-gray-500 mb-1 text-center leading-tight">
            Unloading<br />Pending
          </div>
          <div className="text-sm sm:text-lg font-bold text-orange-600">
            {data?.unloadingPending || 0}/{data?.totalArrivals || 0}
          </div>
        </div>

        {/* Top Right: Unloading Complete */}
        <div className="border border-gray-200 rounded p-1 sm:p-2 flex flex-col justify-center items-center min-h-[60px] sm:min-h-[70px]">
          <div className="text-[10px] sm:text-xs font-medium text-gray-500 mb-1 text-center leading-tight">
            Unloading<br />Complete
          </div>
          <div className="text-sm sm:text-lg font-bold text-green-600">
            {data?.unloadingComplete || 0}/{data?.totalArrivals || 0}
          </div>
        </div>

        {/* Bottom Left: Patti Pending */}
        <div className="border border-gray-200 rounded p-1 sm:p-2 flex flex-col justify-center items-center min-h-[60px] sm:min-h-[70px]">
          <div className="text-[10px] sm:text-xs font-medium text-gray-500 mb-1 text-center leading-tight">
            Patti<br />Pending
          </div>
          <div className="text-sm sm:text-lg font-bold text-red-600">
            {data?.pattiPending || 0}/{data?.totalArrivals || 0}
          </div>
        </div>

        {/* Bottom Middle: Partial Patti */}
        <div className="border border-gray-200 rounded p-1 sm:p-2 flex flex-col justify-center items-center min-h-[60px] sm:min-h-[70px]">
          <div className="text-[10px] sm:text-xs font-medium text-gray-500 mb-1 text-center leading-tight">
            Partial<br />Patti
          </div>
          <div className="text-sm sm:text-lg font-bold text-yellow-600">
            {data?.partialPatti || 0}/{data?.totalArrivals || 0}
          </div>
        </div>

        {/* Bottom Right: Full Patti */}
        <div className="border border-gray-200 rounded p-1 sm:p-2 flex flex-col justify-center items-center min-h-[60px] sm:min-h-[70px]">
          <div className="text-[10px] sm:text-xs font-medium text-gray-500 mb-1 text-center leading-tight">
            Full<br />Patti
          </div>
          <div className="text-sm sm:text-lg font-bold text-green-600">
            {data?.fullPatti || 0}/{data?.totalArrivals || 0}
          </div>
        </div>

      </div>
    </div>
  );
};

export default VehicleArrivalsCard;
