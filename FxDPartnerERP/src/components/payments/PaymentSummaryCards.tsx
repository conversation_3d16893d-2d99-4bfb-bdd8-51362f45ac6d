import React from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Receipt, 
  DollarSign, 
  CreditCard,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import { PaymentSummary } from '../../types/payment.types';

interface PaymentSummaryCardsProps {
  summary: PaymentSummary;
  loading?: boolean;
}

const PaymentSummaryCards: React.FC<PaymentSummaryCardsProps> = ({ 
  summary, 
  loading = false 
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatGrowth = (growth: number) => {
    const isPositive = growth >= 0;
    return (
      <div className={`flex items-center text-sm font-medium ${
        isPositive ? 'text-green-600' : 'text-red-600'
      }`}>
        {isPositive ? (
          <ArrowUpRight className="h-4 w-4 mr-1" />
        ) : (
          <ArrowDownRight className="h-4 w-4 mr-1" />
        )}
        {Math.abs(growth).toFixed(1)}%
      </div>
    );
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="animate-pulse">
              <div className="flex items-center justify-between mb-4">
                <div className="h-4 bg-gray-200 rounded w-20"></div>
                <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
              </div>
              <div className="h-8 bg-gray-200 rounded w-24 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-16"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  const cards = [
    {
      title: 'Total Received',
      value: summary.totalReceived,
      icon: TrendingUp,
      color: 'green',
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600',
      borderColor: 'border-green-200',
      growth: summary.monthlyGrowth > 0 ? summary.monthlyGrowth : 0
    },
    {
      title: 'Total Paid',
      value: summary.totalPaid,
      icon: TrendingDown,
      color: 'blue',
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600',
      borderColor: 'border-blue-200',
      growth: 0
    },
    {
      title: 'Total Expenses',
      value: summary.totalExpenses,
      icon: Receipt,
      color: 'red',
      bgColor: 'bg-red-50',
      iconColor: 'text-red-600',
      borderColor: 'border-red-200',
      growth: 0
    },
    {
      title: 'Net Cash Flow',
      value: summary.netCashFlow,
      icon: summary.netCashFlow >= 0 ? DollarSign : CreditCard,
      color: summary.netCashFlow >= 0 ? 'green' : 'red',
      bgColor: summary.netCashFlow >= 0 ? 'bg-green-50' : 'bg-red-50',
      iconColor: summary.netCashFlow >= 0 ? 'text-green-600' : 'text-red-600',
      borderColor: summary.netCashFlow >= 0 ? 'border-green-200' : 'border-red-200',
      growth: summary.monthlyGrowth
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
      {cards.map((card, index) => {
        const Icon = card.icon;
        
        return (
          <div
            key={index}
            className={`bg-white rounded-xl shadow-sm border ${card.borderColor} p-4 sm:p-6 hover:shadow-md transition-shadow duration-200`}
          >
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <div className="text-xs sm:text-sm font-medium text-gray-600">
                {card.title}
              </div>
              <div className={`h-8 w-8 sm:h-10 sm:w-10 ${card.bgColor} rounded-full flex items-center justify-center`}>
                <Icon className={`h-4 w-4 sm:h-5 sm:w-5 ${card.iconColor}`} />
              </div>
            </div>
            
            <div className="space-y-1 sm:space-y-2">
              <div className="text-xl sm:text-2xl font-bold text-gray-900">
                {formatCurrency(card.value)}
              </div>
              
              {card.growth !== 0 && (
                <div className="flex items-center justify-between">
                  {formatGrowth(card.growth)}
                  <span className="text-xs text-gray-500 hidden sm:inline">vs last month</span>
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PaymentSummaryCards;
