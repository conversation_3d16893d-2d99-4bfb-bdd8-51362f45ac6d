import React, { useState, useEffect, useRef } from 'react';
import { Search, Plus, ChevronDown, Check, Package } from 'lucide-react';
import { skuService } from '../../services/api/productService';
import { toast } from 'react-hot-toast';

interface SKU {
  id: string;
  code: string;
  unit_type: 'box' | 'loose';
  unit_weight?: number;
  status: string;
  product?: {
    id: string;
    name: string;
    category: string;
  };
}

interface SKUSelectorProps {
  productId?: string;
  availableSKUs?: SKU[];
  value?: SKU | null;
  onChange: (sku: SKU | null) => void;
  onCreateNew: () => void;
  placeholder?: string;
  disabled?: boolean;
  showCreateOption?: boolean;
}

const SKUSelector: React.FC<SKUSelectorProps> = ({
  productId,
  availableSKUs = [],
  value,
  onChange,
  onCreateNew,
  placeholder = "Select or search for a SKU...",
  disabled = false,
  showCreateOption = true
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [skus, setSKUs] = useState<SKU[]>([]);
  const [loading, setLoading] = useState(false);
  
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Load SKUs when productId changes or use availableSKUs
  useEffect(() => {
    if (productId) {
      loadProductSKUs(productId);
    } else if (availableSKUs.length > 0) {
      setSKUs(availableSKUs);
    } else {
      setSKUs([]);
    }
  }, [productId, availableSKUs]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const loadProductSKUs = async (productId: string) => {
    setLoading(true);
    try {
      const data = await skuService.getByProduct(productId);
      setSKUs(data || []);
    } catch (error) {
      console.error('Error loading SKUs:', error);
      toast.error('Failed to load SKUs');
    } finally {
      setLoading(false);
    }
  };

  const handleInputFocus = () => {
    setIsOpen(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setIsOpen(true);
  };

  const handleSKUSelect = (sku: SKU) => {
    onChange(sku);
    setSearchQuery(sku.code);
    setIsOpen(false);
  };

  const handleCreateNew = () => {
    onCreateNew();
    setIsOpen(false);
  };

  const handleClear = () => {
    onChange(null);
    setSearchQuery('');
    inputRef.current?.focus();
  };

  // Filter SKUs based on search query
  const filteredSKUs = skus.filter(sku =>
    sku.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (sku.product?.name && sku.product.name.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const getUnitTypeDisplay = (unitType: string) => {
    return unitType === 'box' ? 'Box/Crate' : 'Loose';
  };

  const getUnitTypeIcon = (unitType: string) => {
    return unitType === 'box' ? '📦' : '⚖️';
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={value ? value.code : searchQuery}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          placeholder={placeholder}
          disabled={disabled || (!productId && availableSKUs.length === 0)}
          className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
        />
        
        <Package className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        
        {value ? (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        ) : (
          <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        )}
      </div>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
          {loading && (
            <div className="px-4 py-3 text-sm text-gray-500 text-center">
              Loading SKUs...
            </div>
          )}

          {!loading && (!productId && availableSKUs.length === 0) && (
            <div className="px-4 py-3 text-sm text-gray-500 text-center">
              Please select a product first
            </div>
          )}

          {!loading && filteredSKUs.length > 0 && (
            <>
              <div className="px-4 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b">
                Available SKUs
              </div>
              {filteredSKUs.map((sku) => (
                <button
                  key={sku.id}
                  type="button"
                  onClick={() => handleSKUSelect(sku)}
                  className="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <span className="mr-2">{getUnitTypeIcon(sku.unit_type)}</span>
                        <div>
                          <div className="font-medium text-gray-900">{sku.code}</div>
                          <div className="text-sm text-gray-500">
                            {getUnitTypeDisplay(sku.unit_type)}
                            {sku.unit_weight && ` • ${sku.unit_weight} kg per unit`}
                          </div>
                          {sku.product && (
                            <div className="text-xs text-gray-400 mt-1">
                              {sku.product.name}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    {value?.id === sku.id && (
                      <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                    )}
                  </div>
                </button>
              ))}
            </>
          )}

          {!loading && filteredSKUs.length === 0 && searchQuery && skus.length > 0 && (
            <div className="px-4 py-3 text-sm text-gray-500 text-center">
              No SKUs found for "{searchQuery}"
            </div>
          )}

          {!loading && skus.length === 0 && (productId || availableSKUs.length === 0) && (
            <div className="px-4 py-3 text-sm text-gray-500 text-center">
              No SKUs available
            </div>
          )}

          {showCreateOption && (productId || availableSKUs.length > 0) && (
            <button
              type="button"
              onClick={handleCreateNew}
              className="w-full px-4 py-3 text-left hover:bg-green-50 focus:bg-green-50 focus:outline-none border-t border-gray-200 text-green-600 font-medium"
            >
              <div className="flex items-center">
                <Plus className="h-4 w-4 mr-2" />
                Create New SKU
                {searchQuery && ` "${searchQuery}"`}
              </div>
            </button>
          )}
        </div>
      )}

      {value && (
        <div className="mt-2 p-2 bg-gray-50 rounded-md border">
          <div className="flex items-center text-sm">
            <span className="mr-2">{getUnitTypeIcon(value.unit_type)}</span>
            <div>
              <span className="font-medium">{value.code}</span>
              <span className="text-gray-500 ml-2">
                • {getUnitTypeDisplay(value.unit_type)}
                {value.unit_weight && ` • ${value.unit_weight} kg per unit`}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SKUSelector;
