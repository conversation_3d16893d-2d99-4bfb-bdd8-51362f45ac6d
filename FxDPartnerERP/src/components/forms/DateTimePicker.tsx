import React from 'react';
import ReactDatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Calendar } from 'lucide-react';

interface DatePickerProps {
  id?: string;
  name?: string;
  value: string;
  onChange: (value: string) => void;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  minDate?: Date;
  maxDate?: Date;
  className?: string;
}

const DatePicker: React.FC<DatePickerProps> = ({
  id,
  name,
  value,
  onChange,
  label,
  required = false,
  disabled = false,
  placeholder = 'Select date',
  minDate,
  maxDate,
  className = ''
}) => {
  // Convert string value to Date object
  const dateValue = value ? new Date(value) : null;

  // Handle date change and convert back to string with fixed 5:30 AM IST time
  const handleChange = (date: Date | null) => {
    if (date) {
      // Create a new date object to avoid mutating the original
      const selectedDate = new Date(date);
      
      // Set time to 5:30 AM IST (00:00 UTC)
      // Since we want 5:30 AM IST, and IST is UTC+5:30, 
      // we set the time to 5:30 in local time and convert to ISO
      selectedDate.setHours(5, 30, 0, 0);
      
      // Convert to ISO string for consistent storage
      onChange(selectedDate.toISOString());
    } else {
      onChange('');
    }
  };

  return (
    <div className={className}>
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <div className="relative">
        <ReactDatePicker
          id={id}
          name={name}
          selected={dateValue}
          onChange={handleChange}
          dateFormat="dd/MM/yyyy"
          placeholderText={placeholder}
          disabled={disabled}
          minDate={minDate}
          maxDate={maxDate}
          required={required}
          className="block w-full border border-gray-300 rounded-lg shadow-sm py-2.5 px-3 pr-10 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200"
          wrapperClassName="w-full"
          popperClassName="react-datepicker-popper"
          showPopperArrow={false}
          autoComplete="off"
          calendarClassName="modern-calendar"
        />
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <Calendar className="h-5 w-5 text-gray-400" />
        </div>
      </div>
    </div>
  );
};

export default DatePicker;
