import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Search, ChevronDown, Package } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { skuService } from '../../services/api/productService';

interface SKU {
  id: string;
  product_id: string;
  code: string;
  unit_type: 'box' | 'loose';
  unit_weight?: number;
  status: string;
}

interface SKUSearchInputProps {
  productId: string;
  value: string; // This will be the SKU code, not ID
  onChange: (skuCode: string, selectedSKU?: SKU) => void;
  placeholder?: string;
  className?: string;
}

const SKUSearchInput: React.FC<SKUSearchInputProps> = ({
  productId,
  value,
  onChange,
  placeholder = "Search SKUs...",
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [skus, setSkus] = useState<SKU[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Update input value when value prop changes
  useEffect(() => {
    setInputValue(value || '');
  }, [value]);

  // Load SKUs when product ID changes or when search query changes
  useEffect(() => {
    if (!productId) return;

    const loadSKUs = async () => {
      try {
        setLoading(true);
        const data = await skuService.search(productId, searchQuery);
        setSkus(data || []);
      } catch (error) {
        console.error('Error loading SKUs:', error);
        toast.error('Failed to load SKUs');
      } finally {
        setLoading(false);
      }
    };

    loadSKUs();
  }, [productId, searchQuery]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setSearchQuery(newValue);
    setIsOpen(true);
    setSelectedIndex(-1);

    // Always pass the typed value back to parent to allow free text input
    onChange(newValue, undefined);
  };

  // Handle SKU selection
  const handleSelectSKU = (sku: SKU) => {
    console.log('🎯 SKUSearchInput - Processing selected SKU:', {
      originalSKU: {
        id: sku.id,
        product_id: sku.product_id,
        code: sku.code,
        unit_type: sku.unit_type
      }
    });

    // Pass the selected SKU to the parent component
    onChange(sku.code, sku);
    setInputValue(sku.code);

    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.blur();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'ArrowDown' || e.key === 'Enter') {
        e.preventDefault();
        setIsOpen(true);
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < skus.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : 0);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && skus[selectedIndex]) {
          handleSelectSKU(skus[selectedIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle focus/blur
  const handleFocus = () => {
    setIsOpen(true);
  };

  const handleBlur = (e: React.FocusEvent) => {
    // Don't close if clicking inside dropdown
    if (dropdownRef.current?.contains(e.relatedTarget as Node)) {
      return;
    }

    // Use timeout to allow click events to process
    setTimeout(() => {
      setIsOpen(false);
      // Don't restore value on blur - allow free text input
    }, 200);
  };

  // Create portal root if it doesn't exist
  useEffect(() => {
    if (!document.getElementById('portal-root')) {
      const portalRoot = document.createElement('div');
      portalRoot.id = 'portal-root';
      document.body.appendChild(portalRoot);
    }
  }, []);

  // Get portal root
  const portalRoot = document.getElementById('portal-root');

  // Calculate dropdown position
  const dropdownPosition = React.useMemo(() => {
    if (!isOpen || !inputRef.current) return null;

    const rect = inputRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;
    
    // Show dropdown above if there's more space above and not enough below
    const showAbove = spaceBelow < 200 && spaceAbove > spaceBelow;
    
    return {
      top: showAbove ? rect.top + window.scrollY - 200 : rect.bottom + window.scrollY,
      left: rect.left + window.scrollX,
      width: rect.width,
      maxHeight: showAbove ? Math.min(spaceAbove - 10, 300) : Math.min(spaceBelow - 10, 300)
    };
  }, [isOpen]);

  // Render dropdown
  const renderDropdown = () => {
    if (!isOpen || !portalRoot || !dropdownPosition) return null;

    return createPortal(
      <div
        ref={dropdownRef}
        style={{
          position: 'fixed',
          top: dropdownPosition.top,
          left: dropdownPosition.left,
          width: dropdownPosition.width,
          maxHeight: dropdownPosition.maxHeight,
          zIndex: 9999
        }}
        className="bg-white border border-gray-300 rounded-md shadow-lg overflow-auto"
      >
        {loading ? (
          <div className="px-3 py-2 text-sm text-gray-500 text-center">
            Loading SKUs...
          </div>
        ) : skus.length > 0 ? (
          <ul className="py-1">
            {skus.map((sku, index) => (
              <li
                key={sku.id}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log('🔍 DROPDOWN ITEM CLICKED:', {
                    index,
                    clickedSKU: {
                      id: sku.id,
                      product_id: sku.product_id,
                      code: sku.code,
                      unit_type: sku.unit_type
                    }
                  });
                  handleSelectSKU(sku);
                }}
                className={`px-3 py-2 cursor-pointer transition-colors duration-150 ${
                  index === selectedIndex
                    ? 'bg-green-50 text-green-900'
                    : 'hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Package className="h-4 w-4 text-gray-400 mr-2" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {sku.code}
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <div className="px-3 py-2 text-sm text-gray-500 text-center">
            {inputValue.trim() === '' ? 'Start typing to search SKUs...' : 'No SKUs found'}
          </div>
        )}
      </div>,
      portalRoot
    );
  };

  return (
    <div className="relative">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${className}`}
          autoComplete="off"
        />
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <ChevronDown className="h-4 w-4 text-gray-400" />
        </div>
      </div>
      {renderDropdown()}
    </div>
  );
};

export default SKUSearchInput;
