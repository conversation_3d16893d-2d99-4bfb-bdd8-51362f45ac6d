import React, { useState, useEffect } from 'react';
import { DollarSign, Smartphone, Building, FileText } from 'lucide-react';

export interface PaymentCollection {
  cash: number;
  upi: number;
  bank_transfer: number;
  cheque: number;
}

interface PaymentCollectionSectionProps {
  orderTotal: number;
  paymentCollection: PaymentCollection;
  onPaymentCollectionChange: (collection: PaymentCollection) => void;
}

const PaymentCollectionSection: React.FC<PaymentCollectionSectionProps> = ({
  orderTotal,
  paymentCollection,
  onPaymentCollectionChange
}) => {
  const [localPayments, setLocalPayments] = useState<PaymentCollection>(paymentCollection);

  useEffect(() => {
    setLocalPayments(paymentCollection);
  }, [paymentCollection]);

  const handlePaymentChange = (type: keyof PaymentCollection, value: string) => {
    const numValue = parseFloat(value) || 0;
    const updatedPayments = {
      ...localPayments,
      [type]: numValue
    };
    setLocalPayments(updatedPayments);
    onPaymentCollectionChange(updatedPayments);
  };

  const getTotalCollected = () => {
    return localPayments.cash + localPayments.upi + localPayments.bank_transfer + localPayments.cheque;
  };

  const getRemainingAmount = () => {
    return orderTotal - getTotalCollected();
  };

  const getPaymentIcon = (type: keyof PaymentCollection) => {
    switch (type) {
      case 'cash':
        return <DollarSign className="h-5 w-5 text-green-600" />;
      case 'upi':
        return <Smartphone className="h-5 w-5 text-blue-600" />;
      case 'bank_transfer':
        return <Building className="h-5 w-5 text-purple-600" />;
      case 'cheque':
        return <FileText className="h-5 w-5 text-orange-600" />;
      default:
        return <DollarSign className="h-5 w-5 text-gray-600" />;
    }
  };

  const getPaymentLabel = (type: keyof PaymentCollection) => {
    switch (type) {
      case 'cash':
        return 'Cash Payment';
      case 'upi':
        return 'UPI Payment';
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'cheque':
        return 'Cheque Payment';
      default:
        return type;
    }
  };

  return (
    <div className="border-t pt-6">
      <h2 className="text-lg font-medium text-gray-900 mb-4">Payment Collection</h2>
      
      {/* Payment Input Boxes */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {(Object.keys(localPayments) as Array<keyof PaymentCollection>).map((paymentType) => (
          <div key={paymentType} className="space-y-2">
            <label className="flex items-center text-sm font-medium text-gray-700">
              {getPaymentIcon(paymentType)}
              <span className="ml-2">{getPaymentLabel(paymentType)}</span>
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">₹</span>
              <input
                type="number"
                value={localPayments[paymentType] || ''}
                onChange={(e) => handlePaymentChange(paymentType, e.target.value)}
                min="0"
                step="0.01"
                className="block w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                placeholder="0.00"
              />
            </div>
          </div>
        ))}
      </div>

      {/* Payment Summary */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Payment Summary</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Order Total:</span>
            <span className="font-medium">₹{orderTotal.toFixed(2)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Total Collected:</span>
            <span className="font-medium">₹{getTotalCollected().toFixed(2)}</span>
          </div>
          <div className="border-t pt-2 flex justify-between">
            <span className="text-gray-600">Remaining Amount:</span>
            <span className={`font-medium ${getRemainingAmount() === 0 ? 'text-green-600' : getRemainingAmount() > 0 ? 'text-orange-600' : 'text-red-600'}`}>
              ₹{getRemainingAmount().toFixed(2)}
            </span>
          </div>
        </div>

        {/* Status Messages */}
        {getRemainingAmount() < 0 && (
          <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-800">
            <strong>Overpayment:</strong> Total collected exceeds order amount by ₹{Math.abs(getRemainingAmount()).toFixed(2)}.
          </div>
        )}
        
        {getRemainingAmount() === 0 && getTotalCollected() > 0 && (
          <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded text-xs text-green-800">
            <strong>Full Payment:</strong> Order amount fully collected.
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentCollectionSection;
