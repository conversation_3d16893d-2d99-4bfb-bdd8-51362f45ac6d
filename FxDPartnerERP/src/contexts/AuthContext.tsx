import React, { createContext, useContext, useState, useEffect } from 'react';
import { apiRequest, API_CONFIG } from '../services/api/config';

interface Organization {
  id: string;
  name: string;
  type: string;
  status: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  roles: string[];
  permissions: string[];
  pages: string[];
  organization_id: string;
  organization?: Organization;
}

interface AuthContextType {
  user: User | null;
  organization: Organization | null;
  userOrganizations: Organization[];
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  switchOrganization: (organizationId: string) => Promise<void>;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasRole: (role: string) => boolean;
  canAccessPage: (page: string) => boolean;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  organization: null,
  userOrganizations: [],
  loading: true,
  login: async () => {},
  logout: () => {},
  isAuthenticated: false,
  switchOrganization: async () => {},
  hasPermission: () => false,
  hasAnyPermission: () => false,
  hasRole: () => false,
  canAccessPage: () => false,
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [userOrganizations, setUserOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);

  // Check if user is logged in on initial load
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check localStorage for stored auth data
        const storedAuth = localStorage.getItem('auth_token');
        const storedUser = localStorage.getItem('user');
        const storedOrganization = localStorage.getItem('organization');
        
        if (storedAuth && storedUser) {
          const userData = JSON.parse(storedUser);
          setUser(userData);
          
          if (storedOrganization) {
            const orgData = JSON.parse(storedOrganization);
            setOrganization(orgData);
            // Update API config with organization ID
            API_CONFIG.ORGANIZATION_ID = orgData.id;
          }

          // Fetch user organizations if we have a valid token
          await fetchUserOrganizations();
        }
      } catch (error) {
        console.error('Authentication error:', error);
        // Clear invalid auth data
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user');
        localStorage.removeItem('organization');
      } finally {
        setLoading(false);
      }
    };
    
    checkAuth();
  }, []);

  const fetchUserOrganizations = async () => {
    try {
      const response = await apiRequest('/auth/me');
      const { user: userData } = response;
      
      if (userData && userData.organizations) {
        const organizations = userData.organizations.map((org: any) => ({
          id: org.id,
          name: org.name,
          type: org.type || 'business',
          status: org.status
        }));
        setUserOrganizations(organizations);
      }
    } catch (error) {
      console.error('Error fetching user organizations:', error);
      // Set mock data for now
      setUserOrganizations([
        {
          id: 'default-org-id',
          name: 'FXD Fruit Shop',
          type: 'retail',
          status: 'active'
        },
        {
          id: 'org-2',
          name: 'FXD Wholesale',
          type: 'wholesale',
          status: 'active'
        }
      ]);
    }
  };

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      console.log('Attempting login with:', { email, apiUrl: API_CONFIG.BASE_URL });
      
      // Call the backend authentication API
      const response = await apiRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ email, password })
      });

      console.log('Login response received:', response);

      // The apiRequest function extracts the data part, so response is { token, user }
      const { token, user: userData } = response;

      if (token && userData) {
        console.log('Login successful, storing auth data');
        
        // Store auth data
        localStorage.setItem('auth_token', token);
        localStorage.setItem('user', JSON.stringify(userData));
        if (userData.organization) {
          localStorage.setItem('organization', JSON.stringify(userData.organization));
        }

        // Update state
        setUser(userData);
        setOrganization(userData.organization);
        
        // Update API config with organization ID
        if (userData.organization_id) {
          API_CONFIG.ORGANIZATION_ID = userData.organization_id;
        }

        // Fetch user organizations after successful login
        await fetchUserOrganizations();
      } else {
        console.error('Invalid response structure:', response);
        throw new Error('Invalid response from server - missing token or user data');
      }
    } catch (error) {
      console.error('Login error:', error);
      // Re-throw with more specific error message
      if (error instanceof Error) {
        throw new Error(`Login failed: ${error.message}`);
      } else {
        throw new Error('Login failed: Unknown error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      // In a real implementation, you would call the backend logout endpoint
      // await apiRequest('/auth/logout', { method: 'POST' });
      
      // Clear local storage
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      localStorage.removeItem('organization');
      
      // Reset state
      setUser(null);
      setOrganization(null);
      setUserOrganizations([]);
      
      // Reset API config
      API_CONFIG.ORGANIZATION_ID = 'default-org-id';
    } catch (error) {
      console.error('Logout error:', error);
      // Even if logout fails, clear local state
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      localStorage.removeItem('organization');
      setUser(null);
      setOrganization(null);
      setUserOrganizations([]);
    }
  };

  const switchOrganization = async (organizationId: string) => {
    try {
      setLoading(true);
      
      // Call the backend API to switch organization
      const response = await apiRequest(`/auth/switch-organization/${organizationId}`, {
        method: 'POST'
      });

      const { token, user: userData } = response;

      if (token && userData) {
        // Update stored auth data
        localStorage.setItem('auth_token', token);
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem('organization', JSON.stringify(userData.organization));

        // Update state
        setUser(userData);
        setOrganization(userData.organization);
        
        // Update API config
        API_CONFIG.ORGANIZATION_ID = organizationId;
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('Error switching organization:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Permission checking functions
  const hasPermission = (permission: string): boolean => {
    if (!user || !user.permissions) return false;
    return user.permissions.includes(permission);
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    if (!user || !user.permissions) return false;
    return permissions.some(permission => user.permissions.includes(permission));
  };

  const hasRole = (role: string): boolean => {
    if (!user || !user.roles) return false;
    return user.roles.includes(role);
  };

  const canAccessPage = (page: string): boolean => {
    if (!user || !user.pages) return false;
    return user.pages.includes(page);
  };

  return (
    <AuthContext.Provider 
      value={{ 
        user, 
        organization,
        userOrganizations,
        loading, 
        login, 
        logout, 
        isAuthenticated: !!user,
        switchOrganization,
        hasPermission,
        hasAnyPermission,
        hasRole,
        canAccessPage
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
