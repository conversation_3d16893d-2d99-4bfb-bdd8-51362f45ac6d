import { useState, useCallback, useMemo, useEffect } from 'react';
import { Payment, PaymentFilters } from '../types/payment.types';

const initialFilters: PaymentFilters = {
  search: '',
  type: 'all',
  status: 'all',
  mode: 'all',
  dateRange: {
    from: '',
    to: ''
  },
  partyType: 'all',
  amountRange: {
    min: null,
    max: null
  },
  quickDateFilter: 'today'
};

export const usePaymentFilters = (payments: Payment[]) => {
  const [filters, setFilters] = useState<PaymentFilters>(initialFilters);
  const [initialized, setInitialized] = useState(false);

  const updateFilter = useCallback(<K extends keyof PaymentFilters>(
    key: K,
    value: PaymentFilters[K]
  ) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const updateDateRange = useCallback((from: string, to: string) => {
    setFilters(prev => ({
      ...prev,
      dateRange: { from, to }
    }));
  }, []);

  const updateAmountRange = useCallback((min: number | null, max: number | null) => {
    setFilters(prev => ({
      ...prev,
      amountRange: { min, max }
    }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(initialFilters);
  }, []);

  const clearDateRange = useCallback(() => {
    setFilters(prev => ({
      ...prev,
      dateRange: { from: '', to: '' }
    }));
  }, []);

  const setQuickDateFilter = useCallback((period: 'today' | 'yesterday' | 'week' | 'month' | 'quarter' | 'year' | 'custom') => {
    setFilters(prev => ({ ...prev, quickDateFilter: period }));
    
    if (period === 'custom') {
      // Don't set date range for custom, let user set it manually
      return;
    }
    
    const now = new Date();
    let from = new Date();
    let to = new Date();
    
    switch (period) {
      case 'today':
        // Set to start of today in local timezone
        from = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        // Set to end of today in local timezone
        to = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);
        break;
      case 'yesterday':
        const yesterday = new Date(now);
        yesterday.setDate(yesterday.getDate() - 1);
        // Set to start of yesterday in local timezone
        from = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
        // Set to end of yesterday in local timezone
        to = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59, 999);
        break;
      case 'week':
        // Last 7 days
        from = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        to = now;
        break;
      case 'month':
        // Last 30 days
        from = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        to = now;
        break;
      case 'quarter':
        // Last 90 days
        from = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        to = now;
        break;
      case 'year':
        from = new Date(now.getFullYear(), 0, 1);
        to = now;
        break;
    }

    // For today and yesterday, we need to use date-only format for comparison
    // For other periods, we can use the full date range
    if (period === 'today' || period === 'yesterday') {
      const fromDate = new Date(from.getFullYear(), from.getMonth(), from.getDate());
      const toDate = new Date(to.getFullYear(), to.getMonth(), to.getDate());
      updateDateRange(
        fromDate.toISOString().split('T')[0],
        toDate.toISOString().split('T')[0]
      );
    } else {
      updateDateRange(
        from.toISOString().split('T')[0],
        to.toISOString().split('T')[0]
      );
    }
  }, [updateDateRange]);

  // Initialize with today's date range on first load
  useEffect(() => {
    if (!initialized) {
      setQuickDateFilter('today');
      setInitialized(true);
    }
  }, [initialized, setQuickDateFilter]);

  const filteredPayments = useMemo(() => {
    return payments.filter(payment => {
      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesSearch = 
          payment.party_name?.toLowerCase().includes(searchLower) ||
          payment.reference_number?.toLowerCase().includes(searchLower) ||
          payment.notes?.toLowerCase().includes(searchLower) ||
          payment.amount.toString().includes(searchLower);
        
        if (!matchesSearch) return false;
      }

      // Type filter
      if (filters.type !== 'all' && payment.type !== filters.type) {
        return false;
      }


      // Mode filter
      if (filters.mode !== 'all' && payment.mode !== filters.mode) {
        return false;
      }

      // Party type filter
      if (filters.partyType !== 'all' && payment.party_type !== filters.partyType) {
        return false;
      }

      // Date range filter
      if (filters.dateRange.from && filters.dateRange.to) {
        // Convert payment date to local date string for proper comparison
        const paymentDate = new Date(payment.payment_date);
        const localPaymentDateString = new Date(
          paymentDate.getFullYear(),
          paymentDate.getMonth(),
          paymentDate.getDate()
        ).toISOString().split('T')[0];
        
        if (localPaymentDateString < filters.dateRange.from || localPaymentDateString > filters.dateRange.to) {
          return false;
        }
      }

      // Amount range filter
      if (filters.amountRange.min !== null && payment.amount < filters.amountRange.min) {
        return false;
      }
      if (filters.amountRange.max !== null && payment.amount > filters.amountRange.max) {
        return false;
      }

      return true;
    });
  }, [payments, filters]);

  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (filters.search) count++;
    if (filters.type !== 'all') count++;
    if (filters.mode !== 'all') count++;
    if (filters.partyType !== 'all') count++;
    if (filters.dateRange.from && filters.dateRange.to) count++;
    if (filters.amountRange.min !== null || filters.amountRange.max !== null) count++;
    return count;
  }, [filters]);

  const hasActiveFilters = useMemo(() => {
    return activeFiltersCount > 0;
  }, [activeFiltersCount]);

  // Get unique values for filter options
  const filterOptions = useMemo(() => {
    const modes = [...new Set(payments.map(p => p.mode))];
    const partyTypes = [...new Set(payments.map(p => p.party_type).filter(Boolean))] as string[];
    const statuses = [...new Set(payments.map(p => p.status))];
    
    return {
      modes,
      partyTypes,
      statuses
    };
  }, [payments]);

  return {
    filters,
    filteredPayments,
    activeFiltersCount,
    hasActiveFilters,
    filterOptions,
    updateFilter,
    updateDateRange,
    updateAmountRange,
    clearFilters,
    clearDateRange,
    setQuickDateFilter
  };
};
