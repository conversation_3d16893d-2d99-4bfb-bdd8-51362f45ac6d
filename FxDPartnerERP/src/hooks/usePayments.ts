import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { paymentService } from '../services/api/paymentService';
import { Payment, PaymentSummary } from '../types/payment.types';

export const usePayments = () => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPayments, setSelectedPayments] = useState<string[]>([]);

  const loadPayments = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await paymentService.getAll();
      setPayments(data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load payments';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const createPayment = useCallback(async (paymentData: Record<string, unknown>) => {
    try {
      console.log('Creating payment with data:', paymentData);
      const result = await paymentService.create(paymentData);
      console.log('Payment creation result:', result);
      toast.success('Payment recorded successfully!');
      await loadPayments(); // Reload payments
      return true;
    } catch (err) {
      console.error('Error in createPayment:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create payment';
      toast.error(errorMessage);
      return false;
    }
  }, [loadPayments]);

  const updatePayment = useCallback(async (id: string, paymentData: Record<string, unknown>) => {
    try {
      await paymentService.update(id, paymentData);
      toast.success('Payment updated successfully!');
      await loadPayments(); // Reload payments
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update payment';
      toast.error(errorMessage);
      return false;
    }
  }, [loadPayments]);

  const deletePayment = useCallback(async (id: string) => {
    try {
      await paymentService.delete(id);
      setPayments(prev => prev.filter(payment => payment.id !== id));
      toast.success('Payment deleted successfully!');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete payment';
      toast.error(errorMessage);
      return false;
    }
  }, []);

  const deleteMultiplePayments = useCallback(async (ids: string[]) => {
    try {
      await Promise.all(ids.map(id => paymentService.delete(id)));
      setPayments(prev => prev.filter(payment => !ids.includes(payment.id)));
      setSelectedPayments([]);
      toast.success(`${ids.length} payments deleted successfully!`);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete payments';
      toast.error(errorMessage);
      return false;
    }
  }, []);

  const togglePaymentSelection = useCallback((paymentId: string) => {
    setSelectedPayments(prev => 
      prev.includes(paymentId) 
        ? prev.filter(id => id !== paymentId)
        : [...prev, paymentId]
    );
  }, []);

  const selectAllPayments = useCallback((paymentIds: string[]) => {
    setSelectedPayments(paymentIds);
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedPayments([]);
  }, []);

  const getPaymentSummary = useCallback((filteredPayments: Payment[]): PaymentSummary => {
    // Include both 'completed' and 'confirmed' payments as processed payments
    const processedPayments = filteredPayments.filter(p => 
      p.status === 'completed' || p.status === 'confirmed'
    );
    
    const totalReceived = processedPayments
      .filter(p => p.type === 'received')
      .reduce((sum, p) => sum + Number(p.amount), 0);

    // Handle both 'made' and 'paid' types for backward compatibility
    const totalPaid = processedPayments
      .filter(p => p.type === 'made' || p.type === 'paid')
      .reduce((sum, p) => sum + Number(p.amount), 0);

    const totalExpenses = processedPayments
      .filter(p => p.type === 'expense')
      .reduce((sum, p) => sum + Number(p.amount), 0);

    const netCashFlow = totalReceived - totalPaid - totalExpenses;

    // Calculate monthly growth (simplified)
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    const currentMonthPayments = processedPayments.filter(p => {
      const paymentDate = new Date(p.payment_date);
      return paymentDate.getMonth() === currentMonth && paymentDate.getFullYear() === currentYear;
    });
    const currentMonthReceived = currentMonthPayments
      .filter(p => p.type === 'received')
      .reduce((sum, p) => sum + Number(p.amount), 0);
    
    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
    
    const lastMonthPayments = processedPayments.filter(p => {
      const paymentDate = new Date(p.payment_date);
      return paymentDate.getMonth() === lastMonth && paymentDate.getFullYear() === lastMonthYear;
    });
    const lastMonthReceived = lastMonthPayments
      .filter(p => p.type === 'received')
      .reduce((sum, p) => sum + Number(p.amount), 0);
    
    const monthlyGrowth = lastMonthReceived > 0 
      ? ((currentMonthReceived - lastMonthReceived) / lastMonthReceived) * 100 
      : currentMonthReceived > 0 ? 100 : 0;

    return {
      totalReceived,
      totalPaid,
      totalExpenses,
      netCashFlow,
      monthlyGrowth
    };
  }, []);

  useEffect(() => {
    loadPayments();
  }, [loadPayments]);

  return {
    payments,
    loading,
    error,
    selectedPayments,
    loadPayments,
    createPayment,
    updatePayment,
    deletePayment,
    deleteMultiplePayments,
    togglePaymentSelection,
    selectAllPayments,
    clearSelection,
    getPaymentSummary,
    refetch: loadPayments
  };
};
