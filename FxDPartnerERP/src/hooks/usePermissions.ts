import { useAuth } from '../contexts/AuthContext';

export const usePermissions = () => {
  const { hasPermission, hasAnyPermission, hasRole, canAccessPage, user } = useAuth();

  return {
    hasPermission,
    hasAnyPermission,
    hasRole,
    canAccessPage,
    permissions: user?.permissions || [],
    roles: user?.roles || [],
    pages: user?.pages || [],
    
    // Convenience methods for common permissions
    canCreateCustomer: () => hasPermission('customer:create'),
    canReadCustomer: () => hasPermission('customer:read'),
    canUpdateCustomer: () => hasPermission('customer:update'),
    canDeleteCustomer: () => hasPermission('customer:delete'),
    
    canCreateSupplier: () => hasPermission('supplier:create'),
    canReadSupplier: () => hasPermission('supplier:read'),
    canUpdateSupplier: () => hasPermission('supplier:update'),
    canDeleteSupplier: () => hasPermission('supplier:delete'),
    
    canCreateProduct: () => hasPermission('product:create'),
    canReadProduct: () => hasPermission('product:read'),
    canUpdateProduct: () => hasPermission('product:update'),
    canDeleteProduct: () => hasPermission('product:delete'),
    
    canCreateInventory: () => hasPermission('inventory:create'),
    canReadInventory: () => hasPermission('inventory:read'),
    canUpdateInventory: () => hasPermission('inventory:update'),
    canDeleteInventory: () => hasPermission('inventory:delete'),
    canAdjustInventory: () => hasPermission('inventory:adjust'),
    
    canCreateSales: () => hasPermission('sales:create'),
    canReadSales: () => hasPermission('sales:read'),
    canUpdateSales: () => hasPermission('sales:update'),
    canDeleteSales: () => hasPermission('sales:delete'),
    canApproveSales: () => hasPermission('sales:approve'),
    
    canCreatePurchase: () => hasPermission('purchase:create'),
    canReadPurchase: () => hasPermission('purchase:read'),
    canUpdatePurchase: () => hasPermission('purchase:update'),
    canDeletePurchase: () => hasPermission('purchase:delete'),
    canApprovePurchase: () => hasPermission('purchase:approve'),
    
    canCreatePayment: () => hasPermission('payment:create'),
    canReadPayment: () => hasPermission('payment:read'),
    canUpdatePayment: () => hasPermission('payment:update'),
    canDeletePayment: () => hasPermission('payment:delete'),
    
    canViewReports: () => hasPermission('reports:view'),
    canExportReports: () => hasPermission('reports:export'),
    
    canViewSettings: () => hasPermission('settings:view'),
    canUpdateSettings: () => hasPermission('settings:update'),
    
    canManageOrganization: () => hasPermission('organization:manage'),
    
    canCreateRole: () => hasPermission('role:create'),
    canReadRole: () => hasPermission('role:read'),
    canUpdateRole: () => hasPermission('role:update'),
    canDeleteRole: () => hasPermission('role:delete'),
    
    canCreateUser: () => hasPermission('user:create'),
    canReadUser: () => hasPermission('user:read'),
    canUpdateUser: () => hasPermission('user:update'),
    canDeleteUser: () => hasPermission('user:delete'),
    
    // Role checks
    isAdmin: () => hasRole('admin'),
    isManager: () => hasRole('manager'),
    isSalesExecutive: () => hasRole('sales_executive'),
    isPurchaseExecutive: () => hasRole('purchase_executive'),
    isInventoryManager: () => hasRole('inventory_manager'),
    isViewer: () => hasRole('viewer'),
    
    // Page access checks
    canAccessDashboard: () => canAccessPage('dashboard'),
    canAccessVehicleArrival: () => canAccessPage('vehicle_arrival'),
    canAccessRecordPurchase: () => canAccessPage('record_purchase'),
    canAccessInventory: () => canAccessPage('inventory'),
    canAccessSales: () => canAccessPage('sales'),
    canAccessDispatch: () => canAccessPage('dispatch'),
    canAccessPendingApprovals: () => canAccessPage('pending_approvals'),
    canAccessCustomers: () => canAccessPage('customers'),
    canAccessSuppliers: () => canAccessPage('suppliers'),
    canAccessLedger: () => canAccessPage('ledger'),
    canAccessPayments: () => canAccessPage('payments'),
    canAccessSettings: () => canAccessPage('settings'),
  };
};

export default usePermissions;
