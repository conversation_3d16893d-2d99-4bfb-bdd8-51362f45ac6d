import { apiRequest } from './config';

export const purchaseRecordService = {
  // Get all purchase records
  getAll: async (filters?: { startDate?: string; endDate?: string }, showGlobalError: boolean = true) => {
    const queryParams = new URLSearchParams();
    if (filters?.startDate) queryParams.append('startDate', filters.startDate);
    if (filters?.endDate) queryParams.append('endDate', filters.endDate);
    
    const queryString = queryParams.toString();
    const url = queryString ? `/api/purchase-records?${queryString}` : '/api/purchase-records';
    
    return await apiRequest(url, {}, showGlobalError);
  },

  // Get single purchase record
  getById: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/purchase-records/${id}`, {}, showGlobalError);
  },

  // Create purchase record
  create: async (recordData: any, itemsData: any[], additionalCostsData: any[], showGlobalError: boolean = true) => {
    return await apiRequest('/api/purchase-records', {
      method: 'POST',
      body: JSON.stringify({
        recordData,
        itemsData,
        additionalCostsData
      }),
    }, showGlobalError);
  },

  // Update purchase record
  update: async (id: string, recordData: any, itemsData?: any[], additionalCostsData?: any[], showGlobalError: boolean = true) => {
    return await apiRequest(`/api/purchase-records/${id}`, {
      method: 'PUT',
      body: JSON.stringify({
        recordData,
        itemsData,
        additionalCostsData
      }),
    }, showGlobalError);
  },

  // Update status
  updateStatus: async (id: string, status: string, closureNotes?: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/purchase-records/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status, closureNotes }),
    }, showGlobalError);
  },

  // Delete purchase record
  delete: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/purchase-records/${id}`, {
      method: 'DELETE',
    }, showGlobalError);
  },

  // Update closure status
  updateClosureStatus: async (id: string, status: string, notes?: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/purchase-records/${id}/closure-status`, {
      method: 'PUT',
      body: JSON.stringify({ status, notes }),
    }, showGlobalError);
  },
};
