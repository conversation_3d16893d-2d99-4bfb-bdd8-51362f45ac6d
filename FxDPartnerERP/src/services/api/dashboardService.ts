import { apiRequest } from './config';
import { 
  CustomerSupplierView,
  ArrivalView,
  InventoryView,
  SalesPaymentView,
  PurchasePaymentView,
  FinancialMetrics, 
  OperationalMetrics, 
  CustomerInsights, 
  Alert,
  DashboardFilters 
} from '../../types/dashboard.types';

export class DashboardService {
  private static baseUrl = '/api/dashboard';

  // Simple Dashboard Views
  static async getCustomerSupplierView(filters?: { startDate?: string; endDate?: string }, showGlobalError: boolean = true): Promise<CustomerSupplierView> {
    const params = new URLSearchParams();
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);
    
    const queryString = params.toString();
    const endpoint = `${this.baseUrl}/customer-supplier${queryString ? `?${queryString}` : ''}`;
    
    return await apiRequest(endpoint, { method: 'GET' }, showGlobalError);
  }

  static async getArrivalView(filters?: { startDate?: string; endDate?: string }, showGlobalError: boolean = true): Promise<ArrivalView> {
    const params = new URLSearchParams();
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);
    
    const queryString = params.toString();
    const endpoint = `${this.baseUrl}/arrivals${queryString ? `?${queryString}` : ''}`;
    
    return await apiRequest(endpoint, { method: 'GET' }, showGlobalError);
  }

  static async getInventoryView(filters?: { startDate?: string; endDate?: string }, showGlobalError: boolean = true): Promise<InventoryView> {
    const params = new URLSearchParams();
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);
    
    const queryString = params.toString();
    const endpoint = `${this.baseUrl}/inventory${queryString ? `?${queryString}` : ''}`;
    
    return await apiRequest(endpoint, { method: 'GET' }, showGlobalError);
  }

  static async getSalesPaymentView(filters?: { startDate?: string; endDate?: string }, showGlobalError: boolean = true): Promise<SalesPaymentView> {
    const params = new URLSearchParams();
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);
    
    const queryString = params.toString();
    const endpoint = `${this.baseUrl}/sales-payment${queryString ? `?${queryString}` : ''}`;
    
    return await apiRequest(endpoint, { method: 'GET' }, showGlobalError);
  }

  static async getPurchasePaymentView(filters?: { startDate?: string; endDate?: string }, showGlobalError: boolean = true): Promise<PurchasePaymentView> {
    const params = new URLSearchParams();
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);
    
    const queryString = params.toString();
    const endpoint = `${this.baseUrl}/purchase-payment${queryString ? `?${queryString}` : ''}`;
    
    return await apiRequest(endpoint, { method: 'GET' }, showGlobalError);
  }

  // Legacy methods for backward compatibility
  static async getFinancialMetrics(filters?: Partial<DashboardFilters>, showGlobalError: boolean = true): Promise<FinancialMetrics> {
    const params = new URLSearchParams();
    if (filters?.period) params.append('period', filters.period);
    
    const queryString = params.toString();
    const endpoint = `${this.baseUrl}/financial-metrics${queryString ? `?${queryString}` : ''}`;
    
    return await apiRequest(endpoint, { method: 'GET' }, showGlobalError);
  }

  static async getOperationalMetrics(filters?: Partial<DashboardFilters>, showGlobalError: boolean = true): Promise<OperationalMetrics> {
    const params = new URLSearchParams();
    if (filters?.period) params.append('period', filters.period);
    
    const queryString = params.toString();
    const endpoint = `${this.baseUrl}/operational-metrics${queryString ? `?${queryString}` : ''}`;
    
    return await apiRequest(endpoint, { method: 'GET' }, showGlobalError);
  }

  static async getCustomerInsights(filters?: Partial<DashboardFilters>, showGlobalError: boolean = true): Promise<CustomerInsights> {
    const params = new URLSearchParams();
    if (filters?.period) params.append('period', filters.period);
    
    const queryString = params.toString();
    const endpoint = `${this.baseUrl}/customer-insights${queryString ? `?${queryString}` : ''}`;
    
    return await apiRequest(endpoint, { method: 'GET' }, showGlobalError);
  }

  static async getCriticalAlerts(showGlobalError: boolean = true): Promise<Alert[]> {
    const data = await apiRequest(`${this.baseUrl}/alerts`, { method: 'GET' }, showGlobalError);
    return data.map((alert: any) => ({
      ...alert,
      timestamp: new Date(alert.timestamp)
    }));
  }

  // Utility method to get all simple dashboard data at once
  static async getAllSimpleDashboardData(filters?: { startDate?: string; endDate?: string }) {
    try {
      const [
        customerSupplierView,
        arrivalView,
        inventoryView,
        salesPaymentView,
        purchasePaymentView
      ] = await Promise.all([
        this.getCustomerSupplierView(filters),
        this.getArrivalView(filters),
        this.getInventoryView(filters),
        this.getSalesPaymentView(filters),
        this.getPurchasePaymentView(filters)
      ]);

      return {
        customerSupplierView,
        arrivalView,
        inventoryView,
        salesPaymentView,
        purchasePaymentView
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error;
    }
  }
}
