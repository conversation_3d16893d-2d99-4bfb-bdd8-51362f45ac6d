import { apiRequest } from './config';

export const paymentService = {
  // Get all payments
  getAll: async (showGlobalError: boolean = true) => {
    return await apiRequest('/api/payments', {}, showGlobalError);
  },

  // Get single payment
  getById: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/payments/${id}`, {}, showGlobalError);
  },

  // Create payment
  create: async (paymentData: Record<string, unknown>, proofFile?: File, showGlobalError: boolean = true) => {
    console.log('=== PAYMENT SERVICE DEBUG ===');
    console.log('Payment data received:', paymentData);
    console.log('Proof file:', proofFile);
    
    // Since we're now handling file uploads separately in the frontend,
    // we can send the payment data as JSON directly
    console.log('Sending payment data as JSON:', JSON.stringify(paymentData));
    console.log('=== END PAYMENT SERVICE DEBUG ===');

    return await apiRequest('/api/payments', {
      method: 'POST',
      body: JSON.stringify(paymentData),
      headers: {
        'Content-Type': 'application/json',
      },
    }, showGlobalError);
  },

  // Update payment
  update: async (id: string, paymentData: Record<string, unknown>, proofFile?: File, showGlobalError: boolean = true) => {
    const formData = new FormData();
    formData.append('data', JSON.stringify(paymentData));
    
    if (proofFile) {
      formData.append('proof', proofFile);
    }

    return await apiRequest(`/api/payments/${id}`, {
      method: 'PUT',
      body: formData,
      headers: {},
    }, showGlobalError);
  },

  // Delete payment
  delete: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/payments/${id}`, {
      method: 'DELETE',
    }, showGlobalError);
  },

  // Get payments by customer
  getByCustomer: async (customerId: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/payments/party/${customerId}`, {}, showGlobalError);
  },

  // Get payments by supplier
  getBySupplier: async (supplierId: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/payments/party/${supplierId}`, {}, showGlobalError);
  },

  // Get payments by party (unified method for both customers and suppliers)
  getByParty: async (partyId: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/payments/party/${partyId}`, {}, showGlobalError);
  },

  // Get payments by reference (e.g., sales order)
  getByReference: async (referenceId: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/payments/reference/${referenceId}`, {}, showGlobalError);
  },

  // Get payment documents
  getDocuments: async (paymentId: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/payments/${paymentId}/documents`, {}, showGlobalError);
  },
};
