import { apiRequest } from './config';

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface UpdateProfileRequest {
  name: string;
}

export interface UserProfile {
  id: string;
  email: string;
  name: string;
  first_name: string;
  phone?: string;
  status: string;
  role?: string;
  roles: string[];
  permissions: string[];
  pages: string[];
  organization_id?: string;
  organization?: {
    id: string;
    name: string;
    code?: string;
    status: string;
  };
  last_login_at?: string;
  created_at: string;
}

export const userService = {
  // Change user password
  changePassword: async (data: ChangePasswordRequest): Promise<void> => {
    await apiRequest('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  },

  // Update user profile
  updateProfile: async (data: UpdateProfileRequest): Promise<void> => {
    await apiRequest('/auth/update-profile', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  },

  // Get current user profile
  getCurrentUser: async (): Promise<UserProfile> => {
    const response = await apiRequest('/auth/me');
    return response.user;
  }
};
