import { apiRequest } from './config';

export const customerService = {
  // Get all customers
  getAll: async (showGlobalError: boolean = true) => {
    return await apiRequest('/api/customers', {}, showGlobalError);
  },

  // Get single customer
  getById: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/customers/${id}`, {}, showGlobalError);
  },

  // Create customer
  create: async (customerData: any, showGlobalError: boolean = true) => {
    return await apiRequest('/api/customers', {
      method: 'POST',
      body: JSON.stringify(customerData),
    }, showGlobalError);
  },

  // Update customer
  update: async (id: string, customerData: any, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/customers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(customerData),
    }, showGlobalError);
  },

  // Delete customer
  delete: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/customers/${id}`, {
      method: 'DELETE',
    }, showGlobalError);
  },
};
