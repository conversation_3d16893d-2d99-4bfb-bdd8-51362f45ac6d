import { apiRequest } from './config';

export const supplierService = {
  // Get all suppliers
  getAll: async (showGlobalError: boolean = true) => {
    return await apiRequest('/api/suppliers', {}, showGlobalError);
  },

  // Get single supplier
  getById: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/suppliers/${id}`, {}, showGlobalError);
  },

  // Create supplier
  create: async (data: any, showGlobalError: boolean = true) => {
    return await apiRequest('/api/suppliers', {
      method: 'POST',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Update supplier
  update: async (id: string, data: any, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/suppliers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Delete supplier
  delete: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/suppliers/${id}`, {
      method: 'DELETE',
    }, showGlobalError);
  },

  // Get purchase records for a supplier
  getPurchaseRecords: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/suppliers/${id}/purchase-records`, {}, showGlobalError);
  },

  // Get payments for a supplier
  getPayments: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/suppliers/${id}/payments`, {}, showGlobalError);
  },

  // Update supplier balance
  updateBalance: async (id: string, data: { amount: number; operation: 'add' | 'subtract' }, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/suppliers/${id}/balance`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Get suppliers with statistics
  getWithStats: async (showGlobalError: boolean = true) => {
    return await apiRequest('/api/suppliers/stats', {}, showGlobalError);
  },

  // Search suppliers
  search: async (query: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/suppliers/search?q=${encodeURIComponent(query)}`, {}, showGlobalError);
  },
};
