import { apiRequest } from './config';

export const productService = {
  // Get all products
  getAll: async (showGlobalError: boolean = true) => {
    return await apiRequest('/api/products', {}, showGlobalError);
  },

  // Get all products with SKUs
  getAllWithSKUs: async (showGlobalError: boolean = true) => {
    return await apiRequest('/api/products/all-with-skus', {}, showGlobalError);
  },

  // Search products
  search: async (query: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/products/search?q=${encodeURIComponent(query)}`, {}, showGlobalError);
  },

  // Search products with detailed SKU information
  searchWithSKUs: async (query: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/products/search-with-skus?q=${encodeURIComponent(query)}`, {}, showGlobalError);
  },

  // Get recent products
  getRecent: async (showGlobalError: boolean = true) => {
    return await apiRequest('/api/products/recent', {}, showGlobalError);
  },

  // Get single product
  getById: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/products/${id}`, {}, showGlobalError);
  },

  // Create product
  create: async (data: any, showGlobalError: boolean = true) => {
    return await apiRequest('/api/products', {
      method: 'POST',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Update product
  update: async (id: string, data: any, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Delete product
  delete: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/products/${id}`, {
      method: 'DELETE',
    }, showGlobalError);
  },
};

export const skuService = {
  // Get all SKUs
  getAll: async (showGlobalError: boolean = true) => {
    return await apiRequest('/api/products/skus', {}, showGlobalError);
  },

  // Get SKUs by product
  getByProduct: async (productId: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/products/${productId}/skus`, {}, showGlobalError);
  },

  // Search SKUs by product ID and query
  search: async (productId: string, query?: string, showGlobalError: boolean = true) => {
    const queryParams = new URLSearchParams();
    queryParams.append('productId', productId);
    if (query) {
      queryParams.append('q', query);
    }
    return await apiRequest(`/api/products/skus/search?${queryParams.toString()}`, {}, showGlobalError);
  },

  // Create SKU
  create: async (data: any, showGlobalError: boolean = true) => {
    return await apiRequest('/api/products/skus', {
      method: 'POST',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Update SKU
  update: async (id: string, data: any, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/products/skus/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Delete SKU
  delete: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/products/skus/${id}`, {
      method: 'DELETE',
    }, showGlobalError);
  },
};
