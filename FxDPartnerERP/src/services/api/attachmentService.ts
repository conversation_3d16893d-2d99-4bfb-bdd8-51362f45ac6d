import { apiRequest } from './config';

export const attachmentService = {
  // Upload attachment
  upload: async (file: File, entityType: string, entityId?: string, showGlobalError: boolean = true) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('entityType', entityType);
    if (entityId) {
      formData.append('entityId', entityId);
    }

    return await apiRequest('/api/attachments/upload', {
      method: 'POST',
      body: formData,
      // Don't set Content-Type header for FormData - browser will set it with boundary
      headers: {},
    }, showGlobalError);
  },

  // Get attachments by entity
  getByEntity: async (entityType: string, entityId: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/attachments/${entityType}/${entityId}`, {}, showGlobalError);
  },

  // Delete attachment
  delete: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/attachments/${id}`, {
      method: 'DELETE',
    }, showGlobalError);
  },
};
