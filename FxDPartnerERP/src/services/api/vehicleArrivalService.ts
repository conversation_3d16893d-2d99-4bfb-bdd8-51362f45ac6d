import { apiRequest } from './config';

export const vehicleArrivalService = {
  // Get all vehicle arrivals
  getAll: async (showGlobalError: boolean = true) => {
    return await apiRequest('/api/procurement/vehicle-arrivals', {}, showGlobalError);
  },

  // Get single vehicle arrival
  getById: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/procurement/vehicle-arrivals/${id}`, {}, showGlobalError);
  },

  // Create vehicle arrival
  create: async (data: any, showGlobalError: boolean = true) => {
    return await apiRequest('/api/procurement/vehicle-arrivals', {
      method: 'POST',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Update vehicle arrival
  update: async (id: string, data: any, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/procurement/vehicle-arrivals/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Update status
  updateStatus: async (id: string, status: string, notes?: string, finalQuantities?: any[], showGlobalError: boolean = true) => {
    return await apiRequest(`/api/procurement/vehicle-arrivals/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ 
        status, 
        notes, 
        final_quantities: finalQuantities 
      }),
    }, showGlobalError);
  },

  // Delete vehicle arrival
  delete: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/procurement/vehicle-arrivals/${id}`, {
      method: 'DELETE',
    }, showGlobalError);
  },
};
