import { apiRequest } from './config';

export interface FileUploadResult {
  fileKey: string;
  fileUrl: string;
  fileName: string;
  fileSize: number;
  contentType: string;
}

export interface DocumentData {
  id: string;
  file_name: string;
  file_key: string;
  file_url?: string;
  file_size: number;
  content_type: string;
  entity_type: string;
  entity_id: string;
  display_name?: string;
  organization_id: string;
  uploaded_by: string;
  created_at: string;
  updated_at: string;
  uploader?: {
    id: string;
    first_name: string;
    email: string;
  };
}

export interface AttachDocumentData {
  fileKey: string;
  fileName: string;
  fileSize: number;
  contentType: string;
  displayName?: string;
}

export class DocumentService {
  /**
   * Upload a single file
   */
  static async uploadFile(file: File, showGlobalError: boolean = true): Promise<FileUploadResult> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiRequest('/api/attachments/upload', {
      method: 'POST',
      body: formData,
    }, showGlobalError);

    return response.data;
  }

  /**
   * Upload file and attach to entity in one step (like the curl command)
   */
  static async uploadAndAttachDirect(
    file: File,
    entityType: string,
    entityId?: string,
    displayName?: string,
    showGlobalError: boolean = true
  ): Promise<{ upload: FileUploadResult; document?: DocumentData }> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('entityType', entityType);
    
    if (entityId) {
      formData.append('entityId', entityId);
    }
    
    if (displayName) {
      formData.append('displayName', displayName);
    }

    const response = await apiRequest('/api/attachments/upload', {
      method: 'POST',
      body: formData,
    }, showGlobalError);

    return response.data;
  }

  /**
   * Upload multiple files
   */
  static async uploadMultipleFiles(files: File[], showGlobalError: boolean = true): Promise<FileUploadResult[]> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });

    const response = await apiRequest('/api/attachments/upload/multiple', {
      method: 'POST',
      body: formData,
    }, showGlobalError);

    return response.data.files;
  }

  /**
   * Attach a document to an entity
   */
  static async attachDocument(
    entityType: string,
    entityId: string,
    documentData: AttachDocumentData,
    showGlobalError: boolean = true
  ): Promise<DocumentData> {
    const response = await apiRequest(`/api/attachments/${entityType}/${entityId}`, {
      method: 'POST',
      body: JSON.stringify(documentData),
    }, showGlobalError);

    return response.data;
  }

  /**
   * Get documents for an entity
   */
  static async getEntityDocuments(entityType: string, entityId: string, showGlobalError: boolean = true): Promise<DocumentData[]> {
    const response = await apiRequest(`/api/attachments/${entityType}/${entityId}`, {}, showGlobalError);
    return response.data;
  }

  /**
   * Delete a document
   */
  static async deleteDocument(documentId: string, showGlobalError: boolean = true): Promise<void> {
    await apiRequest(`/api/attachments/${documentId}`, {
      method: 'DELETE',
    }, showGlobalError);
  }

  /**
   * Search documents
   */
  static async searchDocuments(searchTerm: string, showGlobalError: boolean = true): Promise<DocumentData[]> {
    const response = await apiRequest(`/api/attachments/search?q=${encodeURIComponent(searchTerm)}`, {}, showGlobalError);
    return response.data;
  }

  /**
   * Get organization documents
   */
  static async getOrganizationDocuments(limit?: number, showGlobalError: boolean = true): Promise<DocumentData[]> {
    const queryParams = limit ? `?limit=${limit}` : '';
    const response = await apiRequest(`/api/attachments/organization/all${queryParams}`, {}, showGlobalError);
    return response.data;
  }

  /**
   * Upload file and attach to entity in one step
   */
  static async uploadAndAttach(
    file: File,
    entityType: string,
    entityId: string,
    displayName?: string,
    showGlobalError: boolean = true
  ): Promise<DocumentData> {
    // First upload the file
    const uploadResult = await this.uploadFile(file, showGlobalError);

    // Then attach it to the entity
    const attachData: AttachDocumentData = {
      fileKey: uploadResult.fileKey,
      fileName: uploadResult.fileName,
      fileSize: uploadResult.fileSize,
      contentType: uploadResult.contentType,
      displayName: displayName || uploadResult.fileName
    };

    return await this.attachDocument(entityType, entityId, attachData, showGlobalError);
  }

  /**
   * Upload multiple files and attach to entity
   */
  static async uploadMultipleAndAttach(
    files: File[],
    entityType: string,
    entityId: string,
    showGlobalError: boolean = true
  ): Promise<DocumentData[]> {
    // First upload all files
    const uploadResults = await this.uploadMultipleFiles(files, showGlobalError);

    // Then attach each to the entity
    const attachPromises = uploadResults.map(result => {
      const attachData: AttachDocumentData = {
        fileKey: result.fileKey,
        fileName: result.fileName,
        fileSize: result.fileSize,
        contentType: result.contentType,
        displayName: result.fileName
      };
      return this.attachDocument(entityType, entityId, attachData, showGlobalError);
    });

    return await Promise.all(attachPromises);
  }

  /**
   * Check if file type is supported
   */
  static isSupportedFileType(file: File): boolean {
    const supportedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/csv'
    ];

    return supportedTypes.includes(file.type);
  }

  /**
   * Format file size for display
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get file icon based on content type
   */
  static getFileIcon(contentType: string): string {
    if (contentType.startsWith('image/')) {
      return '🖼️';
    } else if (contentType === 'application/pdf') {
      return '📄';
    } else if (contentType.includes('excel') || contentType.includes('spreadsheet')) {
      return '📊';
    } else if (contentType.includes('word') || contentType.includes('document')) {
      return '📝';
    } else if (contentType.startsWith('text/')) {
      return '📄';
    } else {
      return '📎';
    }
  }
}

export default DocumentService;
