/* Custom styles for dropdown components to ensure proper visibility */

/* Ensure dropdowns appear above other elements */
.dropdown-container {
  position: relative;
  z-index: 10;
}

/* Product dropdown specific styles */
.product-dropdown-list {
  position: absolute;
  z-index: 9999 !important;
  background-color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Ensure dropdown is not cut off by parent containers */
.overflow-visible-important {
  overflow: visible !important;
}

/* Fix for table cells that might clip dropdowns */
td {
  position: relative;
  overflow: visible !important;
}

/* Ensure table doesn't clip dropdowns */
.table-container {
  overflow: visible !important;
}

/* Fix for any parent containers that might have overflow hidden */
.sales-form-container {
  overflow: visible !important;
}

/* Ensure dropdown appears above modals if needed */
.dropdown-above-modal {
  z-index: 10000 !important;
}

/* Mobile specific fixes */
@media (max-width: 768px) {
  .product-dropdown-list {
    position: fixed !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 90vw !important;
    max-width: 400px !important;
  }
}
