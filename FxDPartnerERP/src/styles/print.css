/* Invoice Print Styles */
@media print {
  /* Hide navigation and non-essential elements */
  .no-print,
  nav,
  .sidebar,
  button:not(.print-keep),
  .print-hide {
    display: none !important;
  }

  /* Reset page margins and background */
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  body {
    background: white !important;
    color: black !important;
    font-family: Arial, sans-serif !important;
    font-size: 11pt !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Page setup */
  @page {
    margin: 0.5in;
    size: A4;
  }

  /* Hide everything except invoice content */
  .space-y-4,
  .space-y-6 {
    display: none !important;
  }

  /* Show only invoice content */
  .invoice-print-content {
    display: block !important;
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    background: white !important;
  }

  /* Invoice Header */
  .invoice-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    margin-bottom: 30pt !important;
    padding-bottom: 15pt !important;
    border-bottom: 2px solid #000 !important;
  }

  .invoice-company-info {
    flex: 1 !important;
  }

  .invoice-company-name {
    font-size: 20pt !important;
    font-weight: bold !important;
    color: #000 !important;
    margin-bottom: 5pt !important;
  }

  .invoice-company-details {
    font-size: 10pt !important;
    color: #333 !important;
    line-height: 1.3 !important;
  }

  .invoice-title-section {
    text-align: right !important;
  }

  .invoice-title {
    font-size: 24pt !important;
    font-weight: bold !important;
    color: #000 !important;
    margin-bottom: 5pt !important;
  }

  .invoice-number {
    font-size: 12pt !important;
    color: #333 !important;
  }

  /* Invoice Details Section */
  .invoice-details {
    display: flex !important;
    justify-content: space-between !important;
    margin-bottom: 25pt !important;
  }

  .invoice-to,
  .invoice-info {
    flex: 1 !important;
  }

  .invoice-to {
    margin-right: 30pt !important;
  }

  .invoice-section-title {
    font-size: 12pt !important;
    font-weight: bold !important;
    color: #000 !important;
    margin-bottom: 8pt !important;
    border-bottom: 1px solid #ccc !important;
    padding-bottom: 3pt !important;
  }

  .invoice-customer-name {
    font-size: 11pt !important;
    font-weight: bold !important;
    color: #000 !important;
    margin-bottom: 3pt !important;
  }

  .invoice-customer-details {
    font-size: 10pt !important;
    color: #333 !important;
    line-height: 1.3 !important;
  }

  .invoice-info-row {
    display: flex !important;
    justify-content: space-between !important;
    margin-bottom: 3pt !important;
    font-size: 10pt !important;
  }

  .invoice-info-label {
    font-weight: bold !important;
    color: #000 !important;
  }

  .invoice-info-value {
    color: #333 !important;
  }

  /* Items Table */
  .invoice-items-table {
    width: 100% !important;
    border-collapse: collapse !important;
    margin-bottom: 20pt !important;
  }

  .invoice-items-table th {
    background-color: #f0f0f0 !important;
    border: 1px solid #000 !important;
    padding: 8pt !important;
    text-align: center !important;
    font-size: 10pt !important;
    font-weight: bold !important;
    color: #000 !important;
  }

  .invoice-items-table td {
    border: 1px solid #000 !important;
    padding: 6pt !important;
    font-size: 9pt !important;
    color: #000 !important;
    vertical-align: top !important;
  }

  .invoice-items-table .item-name {
    text-align: left !important;
    font-weight: bold !important;
  }

  .invoice-items-table .item-sku {
    text-align: left !important;
    font-size: 8pt !important;
    color: #666 !important;
  }

  .invoice-items-table .item-qty,
  .invoice-items-table .item-price,
  .invoice-items-table .item-total {
    text-align: right !important;
  }

  /* Invoice Summary */
  .invoice-summary {
    float: right !important;
    width: 250pt !important;
    margin-top: 15pt !important;
  }

  .invoice-summary-table {
    width: 100% !important;
    border-collapse: collapse !important;
  }

  .invoice-summary-table td {
    padding: 5pt 8pt !important;
    font-size: 10pt !important;
    border-bottom: 1px solid #ccc !important;
  }

  .invoice-summary-label {
    text-align: left !important;
    font-weight: bold !important;
    color: #000 !important;
  }

  .invoice-summary-value {
    text-align: right !important;
    color: #333 !important;
  }

  .invoice-total-row td {
    border-top: 2px solid #000 !important;
    border-bottom: 2px solid #000 !important;
    font-size: 12pt !important;
    font-weight: bold !important;
    color: #000 !important;
    padding: 8pt !important;
  }

  /* Payment Information */
  .invoice-payment-info {
    clear: both !important;
    margin-top: 25pt !important;
    padding-top: 15pt !important;
    border-top: 1px solid #ccc !important;
  }

  .invoice-payment-title {
    font-size: 11pt !important;
    font-weight: bold !important;
    color: #000 !important;
    margin-bottom: 8pt !important;
  }

  .invoice-payment-details {
    font-size: 10pt !important;
    color: #333 !important;
  }

  /* Notes Section */
  .invoice-notes {
    margin-top: 20pt !important;
    padding-top: 15pt !important;
    border-top: 1px solid #ccc !important;
  }

  .invoice-notes-title {
    font-size: 11pt !important;
    font-weight: bold !important;
    color: #000 !important;
    margin-bottom: 8pt !important;
  }

  .invoice-notes-content {
    font-size: 10pt !important;
    color: #333 !important;
    line-height: 1.4 !important;
  }

  /* Footer */
  .invoice-footer {
    position: fixed !important;
    bottom: 0.5in !important;
    left: 0.5in !important;
    right: 0.5in !important;
    text-align: center !important;
    font-size: 9pt !important;
    color: #666 !important;
    border-top: 1px solid #ccc !important;
    padding-top: 5pt !important;
  }

  /* Utility classes */
  .print-break-before {
    page-break-before: always !important;
  }

  .print-break-after {
    page-break-after: always !important;
  }

  .print-no-break {
    page-break-inside: avoid !important;
  }

  /* Status badges for print */
  .invoice-status {
    display: inline-block !important;
    padding: 3pt 8pt !important;
    border: 1px solid #000 !important;
    background: #f0f0f0 !important;
    font-size: 9pt !important;
    font-weight: bold !important;
    color: #000 !important;
  }

  /* Dispatch info for print */
  .invoice-dispatch-info {
    margin-top: 15pt !important;
    padding: 8pt !important;
    border: 1px solid #ccc !important;
    background: #f9f9f9 !important;
  }

  .invoice-dispatch-title {
    font-size: 10pt !important;
    font-weight: bold !important;
    color: #000 !important;
    margin-bottom: 5pt !important;
  }

  .invoice-dispatch-details {
    font-size: 9pt !important;
    color: #333 !important;
  }

  /* Returns/PDD section for print */
  .invoice-returns {
    margin-top: 15pt !important;
    padding: 8pt !important;
    border: 1px solid #cc0000 !important;
    background: #fff5f5 !important;
  }

  .invoice-returns-title {
    font-size: 10pt !important;
    font-weight: bold !important;
    color: #cc0000 !important;
    margin-bottom: 5pt !important;
  }

  .invoice-returns-table {
    width: 100% !important;
    border-collapse: collapse !important;
    margin-top: 5pt !important;
  }

  .invoice-returns-table th,
  .invoice-returns-table td {
    border: 1px solid #cc0000 !important;
    padding: 4pt !important;
    font-size: 8pt !important;
    text-align: center !important;
  }

  .invoice-returns-table th {
    background: #ffe6e6 !important;
    font-weight: bold !important;
    color: #cc0000 !important;
  }

  .invoice-returns-table td {
    color: #333 !important;
  }
}
