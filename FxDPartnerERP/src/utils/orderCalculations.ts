/**
 * Utility functions for order calculations
 * Handles proper conversion of decimal values from database to numbers
 */

/**
 * Safely parse a decimal value to number
 * <PERSON>les string decimals from database, null/undefined values
 */
export const parseDecimal = (value: any): number => {
  if (value === null || value === undefined) return 0;
  if (typeof value === 'number') return value;
  
  const parsed = parseFloat(value.toString());
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Calculate subtotal from order items
 */
export const calculateSubtotal = (items: any[]): number => {
  if (!items || items.length === 0) return 0;
  
  return items.reduce((sum, item) => {
    const quantity = parseDecimal(item.quantity);
    const unitPrice = parseDecimal(item.unit_price);
    return sum + (quantity * unitPrice);
  }, 0);
};

/**
 * Calculate total amount from subtotal, tax, and discount
 */
export const calculateTotal = (subtotal: any, taxAmount: any = 0, discountAmount: any = 0): number => {
  const sub = parseDecimal(subtotal);
  const tax = parseDecimal(taxAmount);
  const discount = parseDecimal(discountAmount);
  
  return Math.max(0, sub + tax - discount);
};

/**
 * Calculate order total with fallback to item calculation
 */
export const getOrderTotal = (order: any): number => {
  // First try to use the stored total_amount
  const storedTotal = parseDecimal(order.total_amount);
  
  // If stored total exists and is valid, use it
  if (storedTotal > 0) {
    return storedTotal;
  }
  
  // Fallback: calculate from items if available
  if (order.items || order.sales_order_items) {
    const items = order.items || order.sales_order_items;
    const subtotal = calculateSubtotal(items);
    return calculateTotal(subtotal, order.tax_amount, order.discount_amount);
  }
  
  // Last fallback: try to calculate from stored subtotal
  return calculateTotal(order.subtotal, order.tax_amount, order.discount_amount);
};

/**
 * Format currency value for display
 */
export const formatCurrency = (value: any, currency: string = '₹'): string => {
  const numValue = parseDecimal(value);
  return `${currency}${numValue.toLocaleString('en-IN', { 
    minimumFractionDigits: 2, 
    maximumFractionDigits: 2 
  })}`;
};

/**
 * Calculate available credit for a customer
 */
export const calculateAvailableCredit = (creditLimit: any, currentBalance: any): number => {
  const limit = parseDecimal(creditLimit);
  const balance = parseDecimal(currentBalance);
  return Math.max(0, limit - balance);
};

/**
 * Calculate credit shortage for an order
 */
export const calculateCreditShortage = (orderTotal: any, availableCredit: any): number => {
  const total = parseDecimal(orderTotal);
  const available = parseDecimal(availableCredit);
  return Math.max(0, total - available);
};
