import React, { useState, useEffect } from 'react';
import { Users, Search, Filter, Eye, Pencil, Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { supplierService, type Supplier } from '../../services/api';

const Suppliers: React.FC = () => {
  const navigate = useNavigate();
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  
  useEffect(() => {
    loadSuppliers();
  }, []);

  const loadSuppliers = async () => {
    try {
      const data = await supplierService.getAll();
      setSuppliers(data);
    } catch (error) {
      console.error('Error loading suppliers:', error);
      // Global error handler will show the backend error message
    } finally {
      setLoading(false);
    }
  };
  
  const filteredSuppliers = suppliers.filter(supplier => {
    const matchesSearch = 
      supplier.company_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.contact_person.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.phone.includes(searchTerm);
      
    const matchesStatus = selectedStatus === 'all' || supplier.status === selectedStatus;
    
    return matchesSearch && matchesStatus;
  });

  const handleStatusChange = async (id: string, newStatus: 'active' | 'inactive') => {
    try {
      await supplierService.update(id, { status: newStatus });

      setSuppliers(prev => prev.map(supplier => 
        supplier.id === id ? { ...supplier, status: newStatus } : supplier
      ));
      
      toast.success(`Supplier ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      console.error('Error updating supplier status:', error);
      toast.error('Failed to update supplier status');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading suppliers...</div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 px-4 sm:px-0">
      {/* Header - Mobile Optimized */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="flex items-center">
          <Users className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mr-2" />
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800">Supplier Management</h1>
        </div>
        <button 
          onClick={() => navigate('/suppliers/new')}
          className="mobile-form-button flex items-center justify-center touch-manipulation"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Supplier
        </button>
      </div>
      
      {/* Supplier Summary - Mobile Optimized */}
      <div className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500 mb-1">Total Suppliers</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-800">{suppliers.length}</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600 flex-shrink-0 ml-2">
              <Users className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500 mb-1">Active Suppliers</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-800">
                {suppliers.filter(s => s.status === 'active').length}
              </p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-blue-100 text-blue-600 flex-shrink-0 ml-2">
              <Users className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm xs:col-span-2 lg:col-span-1">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500 mb-1">Total Outstanding</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-800">
                ₹{suppliers.reduce((sum, s) => sum + (Number(s.current_balance) || 0), 0).toLocaleString()}
              </p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-600 flex-shrink-0 ml-2">
              <Users className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Filters and Search - Mobile Optimized */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0 sm:space-x-4">
        <div className="relative flex-1 sm:max-w-xs">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            className="mobile-form-input pl-10"
            placeholder="Search suppliers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="flex items-center space-x-2 sm:flex-shrink-0">
          <Filter className="h-4 w-4 text-gray-500" />
          <select
            className="mobile-form-input min-w-0 flex-1 sm:w-auto"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
      </div>
      
      {/* Suppliers List - Responsive Design */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        {/* Desktop Table View */}
        <div className="hidden lg:block">
          <div className="mobile-table-scroll">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider table-col-md">
                    Supplier Info
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider table-col-lg">
                    Contact Details
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider table-col-lg">
                    Products
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider table-col-md">
                    Financial Info
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider table-col-sm">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider table-col-sm">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredSuppliers.map((supplier) => (
                  <tr key={supplier.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 table-col-md">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                          <Users className="h-5 w-5" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 break-words">{supplier.company_name}</div>
                          <div className="text-sm text-gray-500 break-words">{supplier.contact_person}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 table-col-lg">
                      <div className="text-sm">
                        <div className="text-gray-900">{supplier.phone}</div>
                        <div className="text-gray-500 break-words">{supplier.email}</div>
                        <div className="text-gray-500 break-words">{supplier.address}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 table-col-lg">
                      <div className="flex flex-wrap gap-1">
                        {supplier.products && supplier.products.length > 0 ? (
                          supplier.products.map((product, index) => (
                            <span 
                              key={index}
                              className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800"
                            >
                              {product}
                            </span>
                          ))
                        ) : (
                          <span className="text-sm text-gray-400">No products listed</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 table-col-md">
                      <div className="text-sm">
                        <div className="text-gray-900">Credit: ₹{supplier.credit_limit.toLocaleString()}</div>
                        <div className="text-gray-500">Balance: ₹{supplier.current_balance.toLocaleString()}</div>
                        <div className="text-gray-500">Terms: {supplier.payment_terms} days</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 table-col-sm">
                      <select
                        value={supplier.status}
                        onChange={(e) => handleStatusChange(supplier.id, e.target.value as 'active' | 'inactive')}
                        className={`text-sm rounded-full px-3 py-1 border-0 focus:ring-2 focus:ring-green-500 touch-manipulation ${
                          supplier.status === 'active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                      </select>
                    </td>
                    <td className="px-6 py-4 table-col-sm text-sm font-medium">
                      <div className="flex space-x-2">
                        <button 
                          onClick={() => navigate(`/suppliers/view/${supplier.id}`)}
                          className="text-indigo-600 hover:text-indigo-900 transition-colors duration-200 touch-manipulation p-1"
                          title="View Details"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button 
                          onClick={() => navigate(`/suppliers/edit/${supplier.id}`)}
                          className="text-gray-600 hover:text-gray-900 transition-colors duration-200 touch-manipulation p-1"
                          title="Edit Supplier"
                        >
                          <Pencil className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Mobile Card View */}
        <div className="lg:hidden">
          <div className="divide-y divide-gray-200">
            {filteredSuppliers.map((supplier) => (
              <div key={supplier.id} className="p-4 hover:bg-gray-50 transition-colors duration-200">
                {/* Header with Icon, Company Info and Actions */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-start space-x-3 flex-1 min-w-0">
                    <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                      <Users className="h-5 w-5" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-base font-medium text-gray-900 break-words">{supplier.company_name}</div>
                      <div className="text-sm text-gray-500 break-words">{supplier.contact_person}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1 ml-2">
                    <button 
                      onClick={() => navigate(`/suppliers/view/${supplier.id}`)}
                      className="text-indigo-600 hover:text-indigo-900 transition-colors duration-200 touch-manipulation p-2"
                      title="View Details"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                    <button 
                      onClick={() => navigate(`/suppliers/edit/${supplier.id}`)}
                      className="text-gray-600 hover:text-gray-900 transition-colors duration-200 touch-manipulation p-2"
                      title="Edit Supplier"
                    >
                      <Pencil className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* Contact Details */}
                <div className="mb-3">
                  <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">Contact Details</h4>
                  <div className="text-sm text-gray-900">{supplier.phone}</div>
                  <div className="text-sm text-gray-500 break-all">{supplier.email}</div>
                </div>

                {/* Products */}
                <div className="mb-3">
                  <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">Products</h4>
                  <div className="flex flex-wrap gap-1">
                    {supplier.products && supplier.products.length > 0 ? (
                      supplier.products.map((product, index) => (
                        <span 
                          key={index}
                          className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800"
                        >
                          {product}
                        </span>
                      ))
                    ) : (
                      <span className="text-sm text-gray-400">No products listed</span>
                    )}
                  </div>
                </div>

                {/* Financial Info and Status in horizontal layout */}
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">Financial Info</h4>
                    <div className="text-sm space-y-0.5">
                      <div className="text-gray-900">Credit: ₹{supplier.credit_limit.toLocaleString()}</div>
                      <div className="text-gray-500">Balance: ₹{supplier.current_balance.toLocaleString()}</div>
                      <div className="text-gray-500">Terms: {supplier.payment_terms} days</div>
                    </div>
                  </div>
                  <div className="ml-4">
                    <select
                      value={supplier.status}
                      onChange={(e) => handleStatusChange(supplier.id, e.target.value as 'active' | 'inactive')}
                      className={`text-sm rounded-full px-3 py-1 border-0 focus:ring-2 focus:ring-green-500 touch-manipulation ${
                        supplier.status === 'active' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Empty State */}
        {filteredSuppliers.length === 0 && !loading && (
          <div className="py-12 px-4 text-center text-gray-500">
            <Users className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Suppliers Found</h3>
            <p className="text-sm text-gray-500 mb-4 max-w-sm mx-auto">
              {suppliers.length === 0 
                ? "Get started by adding your first supplier."
                : "No suppliers match your current search and filter criteria."
              }
            </p>
            {suppliers.length === 0 && (
              <button
                onClick={() => navigate('/suppliers/new')}
                className="mobile-form-button"
              >
                Add First Supplier
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Suppliers;
