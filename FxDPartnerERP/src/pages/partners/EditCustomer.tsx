import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { User, ArrowLeft } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { getCustomer, updateCustomer } from '../../services/api';
import Dropdown from '../../components/ui/Dropdown';

interface CustomerFormData {
  name: string;
  customerType: string;
  contact: string;
  email: string;
  address: string;
  gstNumber: string;
  panNumber: string;
  creditLimit: number;
  paymentTerms: number;
  openingBalance: number;
  openingBalanceType: 'debit' | 'credit';
  status: 'active' | 'inactive';
  notes: string;
}

const EditCustomer: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState<CustomerFormData>({
    name: '',
    customerType: 'retailer',
    contact: '',
    email: '',
    address: '',
    gstNumber: '',
    panNumber: '',
    creditLimit: 0,
    paymentTerms: 30,
    openingBalance: 0,
    openingBalanceType: 'debit',
    status: 'active',
    notes: ''
  });

  useEffect(() => {
    if (id) {
      loadCustomerData();
    }
  }, [id]);

  const loadCustomerData = async () => {
    if (!id) return;
    
    try {
      const data = await getCustomer(id);

      // Populate form with existing data
      setFormData({
        name: data.name,
        customerType: data.customer_type,
        contact: data.contact || '',
        email: data.email || '',
        address: data.address || '',
        gstNumber: data.gst_number || '',
        panNumber: data.pan_number || '',
        creditLimit: data.credit_limit,
        paymentTerms: data.payment_terms,
        openingBalance: data.opening_balance || 0,
        openingBalanceType: data.opening_balance_type || 'debit',
        status: data.status,
        notes: data.notes || ''
      });
    } catch (error) {
      console.error('Error loading customer data:', error);
      toast.error('Failed to load customer data');
      navigate('/customers');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'paymentTerms' || name === 'creditLimit' ? Number(value) : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name.trim()) {
      toast.error('Customer name is required');
      return;
    }

    // Contact number validation
    if (!formData.contact.trim()) {
      toast.error('Contact number is required');
      return;
    }

    // Validate contact number format (exactly 10 digits)
    if (!/^[0-9]{10}$/.test(formData.contact)) {
      toast.error('Contact number must be exactly 10 digits');
      return;
    }

    if (formData.creditLimit < 0) {
      toast.error('Credit limit cannot be negative');
      return;
    }

    if (formData.paymentTerms <= 0) {
      toast.error('Payment terms must be greater than 0 days');
      return;
    }

    // Email validation only if email is provided
    if (formData.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    setIsSubmitting(true);

    try {
      await updateCustomer(id!, {
        name: formData.name.trim(),
        customer_type: formData.customerType,
        contact: formData.contact.trim(),
        email: formData.email.trim() || '',
        address: formData.address.trim() || '',
        gst_number: formData.gstNumber.trim() || null,
        pan_number: formData.panNumber.trim() || null,
        credit_limit: formData.creditLimit,
        payment_terms: formData.paymentTerms,
        opening_balance: formData.openingBalance,
        opening_balance_type: formData.openingBalanceType,
        status: formData.status,
        notes: formData.notes.trim() || null
      });

      toast.success('Customer updated successfully!');
      navigate('/customers');
    } catch (error: unknown) {
      console.error('Error updating customer:', error);
      
      // Handle specific error responses
      if (error && typeof error === 'object' && 'response' in error) {
        const apiError = error as { response?: { data?: { error?: string } } };
        const errorMessage = apiError.response?.data?.error;
        
        if (errorMessage) {
          toast.error(errorMessage);
        } else {
          toast.error('Failed to update customer. Please try again.');
        }
      } else {
        toast.error('Failed to update customer. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading customer data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/customers')}
            className="text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <ArrowLeft className="h-6 w-6" />
          </button>
          <div className="flex items-center">
            <User className="h-6 w-6 text-green-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-800">Edit Customer</h1>
          </div>
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h2>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Customer Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Customer Type
                </label>
                <div className="mt-1">
                  <Dropdown
                    options={[
                      { value: 'retailer', label: 'Retailer' },
                      { value: 'wholesaler', label: 'Wholesaler' },
                      { value: 'restaurant', label: 'Restaurant' },
                      { value: 'other', label: 'Other' }
                    ]}
                    value={formData.customerType}
                    onChange={(value) => setFormData(prev => ({ ...prev, customerType: value }))}
                    className="w-full"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Contact Number <span className="text-red-500">*</span>
                </label>
                <input
                  type="tel"
                  name="contact"
                  value={formData.contact}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700">
                  Address
                </label>
                <textarea
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  rows={3}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </div>
          </div>

          {/* Financial Information */}
          <div className="border-t pt-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Financial Information</h2>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Credit Limit (₹)
                </label>
                <input
                  type="number"
                  name="creditLimit"
                  value={formData.creditLimit}
                  onChange={handleChange}
                  min="0"
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Payment Terms (Days)
                </label>
                <input
                  type="number"
                  name="paymentTerms"
                  value={formData.paymentTerms}
                  onChange={handleChange}
                  min="1"
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">GST Number</label>
                <input
                  type="text"
                  name="gstNumber"
                  value={formData.gstNumber}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">PAN Number</label>
                <input
                  type="text"
                  name="panNumber"
                  value={formData.panNumber}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Opening Balance (₹)
                </label>
                <input
                  type="number"
                  name="openingBalance"
                  value={formData.openingBalance}
                  onChange={(e) => setFormData(prev => ({ ...prev, openingBalance: Number(e.target.value) }))}
                  min="0"
                  step="0.01"
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Opening Balance Type
                </label>
                <div className="mt-1">
                  <Dropdown
                    options={[
                      { value: 'debit', label: 'Debit (Customer owes us)' },
                      { value: 'credit', label: 'Credit (We owe customer)' }
                    ]}
                    value={formData.openingBalanceType}
                    onChange={(value) => setFormData(prev => ({ ...prev, openingBalanceType: value as 'debit' | 'credit' }))}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="border-t pt-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h2>
            <div>
              <label className="block text-sm font-medium text-gray-700">Notes</label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-green-500 focus:border-green-500"
                placeholder="Any additional notes about this customer..."
              />
            </div>
          </div>

          {/* Status */}
          <div className="border-t pt-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Status</h2>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Customer Status
              </label>
              <div className="mt-1">
                <Dropdown
                  options={[
                    { value: 'active', label: 'Active' },
                    { value: 'inactive', label: 'Inactive' }
                  ]}
                  value={formData.status}
                  onChange={(value) => setFormData(prev => ({ ...prev, status: value as 'active' | 'inactive' }))}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          <div className="border-t pt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => navigate('/customers')}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Updating Customer...' : 'Update Customer'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditCustomer;
