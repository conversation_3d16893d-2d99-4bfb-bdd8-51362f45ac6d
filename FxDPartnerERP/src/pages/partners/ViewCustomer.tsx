import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { User, ArrowLeft, Edit, Mail, Phone, MapPin, CreditCard, Building, Calendar, FileText, Eye, ShoppingCart, DollarSign, AlertTriangle, Package, TrendingUp, TrendingDown } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { getCustomer, getSalesOrdersByCustomerId, getPaymentsByPartyId, getCustomers, getSuppliers } from '../../services/api';
import { salesService } from '../../services/api/salesService';
import { purchaseRecordService } from '../../services/api/purchaseRecordService';
import { usePayments } from '../../hooks/usePayments';

interface Customer {
  id: string;
  name: string;
  customer_type: string;
  contact: string;
  email: string;
  address: string;
  delivery_addresses: any[] | null;
  gst_number: string | null;
  pan_number: string | null;
  credit_limit: number;
  current_balance: number;
  payment_terms: number;
  status: string;
  notes: string | null;
  opening_balance: number;
  opening_balance_type: 'debit' | 'credit';
  created_at: string | null;
  updated_at: string | null;
}

interface SalesOrder {
  id: string;
  order_number: string;
  order_date: string;
  delivery_date: string | null;
  payment_mode: string;
  payment_status: string;
  subtotal: number | null;
  discount_amount: number | null;
  total_amount: number | null;
  status: string;
  sales_order_items: Array<{
    product_name: string;
    sku_code: string;
    quantity: number;
    unit_type: string;
    unit_price: number;
    total_price: number;
  }>;
}

interface Payment {
  id: string;
  type: string;
  amount: number;
  payment_date: string;
  reference_number: string | null;
  reference_type: string | null;
  mode: string;
  status: string;
  notes: string | null;
}

const ViewCustomer: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { payments: allPayments } = usePayments();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [salesOrders, setSalesOrders] = useState<SalesOrder[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'orders' | 'payments'>('overview');
  const [outstandingBalance, setOutstandingBalance] = useState<number>(0);

  useEffect(() => {
    if (id) {
      loadCustomerData();
    }
  }, [id]);

  useEffect(() => {
    if (customer) {
      calculateOutstandingBalanceAsync().then((balance: number) => {
        setOutstandingBalance(balance);
      });
    }
  }, [customer, salesOrders, payments]);

  const loadCustomerData = async () => {
    if (!id) return;
    
    try {
      const [customerData, ordersData, paymentsData] = await Promise.all([
        getCustomer(id),
        getSalesOrdersByCustomerId(id),
        getPaymentsByPartyId(id)
      ]);
      
      setCustomer(customerData);
      setSalesOrders(ordersData || []);
      setPayments(paymentsData || []);
    } catch (error) {
      console.error('Error loading customer data:', error);
      toast.error('Failed to load customer data');
      navigate('/customers');
    } finally {
      setLoading(false);
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const formatCurrency = (amount: number | null | undefined): string => {
    if (amount === null || amount === undefined) {
      return '0.00';
    }
    return amount.toLocaleString('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'dispatched':
        return 'bg-blue-100 text-blue-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentModeDisplay = (mode: string) => {
    switch (mode) {
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'upi':
        return 'UPI';
      case 'cash':
        return 'Cash';
      case 'credit':
        return 'Credit';
      default:
        return mode.charAt(0).toUpperCase() + mode.slice(1);
    }
  };

  const calculateOrderStats = () => {
    // Exclude cancelled orders from all calculations
    const activeSalesOrders = salesOrders.filter(order => order.status !== 'cancelled');
    
    const totalOrders = activeSalesOrders.length;
    const totalValue = activeSalesOrders.reduce((sum, order) => sum + (Number(order.total_amount) || 0), 0);
    const pendingOrders = activeSalesOrders.filter(order => 
      order.status === 'draft' || order.status === 'processing'
    ).length;
    const completedOrders = activeSalesOrders.filter(order => 
      order.status === 'delivered'
    ).length;

    return { totalOrders, totalValue, pendingOrders, completedOrders };
  };

  const calculatePaymentStats = () => {
    const totalPayments = payments.length;
    // Match Ledger calculation: all payments of type 'received' (since all payments are completed)
    const totalReceived = payments
      .filter(p => p.type === 'received')
      .reduce((sum, p) => sum + (Number(p.amount) || 0), 0);
    const pendingPayments = payments.filter(p => p.status === 'pending').length;

    return { totalPayments, totalReceived, pendingPayments };
  };

  const getAvailableCredit = () => {
    if (!customer) return 0;
    return (Number(customer.credit_limit) || 0) - (Number(customer.current_balance) || 0);
  };

  const getCreditUtilization = () => {
    if (!customer || (Number(customer.credit_limit) || 0) === 0) return 0;
    return ((Number(customer.current_balance) || 0) / (Number(customer.credit_limit) || 1)) * 100;
  };

  const formatOpeningBalance = (amount: number, type: 'debit' | 'credit') => {
    if (!amount || amount === 0) return { text: '₹0', color: 'text-gray-600' };
    const sign = type === 'debit' ? '-' : '+';
    const color = type === 'debit' ? 'text-red-600' : 'text-green-600';
    const formattedAmount = formatCurrency(amount);
    return { text: `${sign}₹${formattedAmount}`, color };
  };

  const calculateOutstandingBalanceAsync = async () => {
    if (!customer || !allPayments) return 0;
    
    try {
      // Fetch ALL historical data (same as Ledger page)
      const [allSalesOrders, allPurchaseRecords, allCustomers, allSuppliers] = await Promise.all([
        salesService.getAll({}),
        purchaseRecordService.getAll({}),
        getCustomers(),
        getSuppliers()
      ]);
      
      // Create ALL ledger entries (same logic as Ledger page)
      const ledgerEntries: any[] = [];
      
      // Add ALL sales transaction entries - EXCLUDE CANCELLED ORDERS
      allSalesOrders.forEach((order: any) => {
        // Skip cancelled orders - they should not affect balance
        if (order.status === 'cancelled') {
          return;
        }
        
        const customerName = order.customer?.name || order.customer_name || 'Unknown Customer';
        const amount = parseFloat(order.total_amount) || 0;
        
        ledgerEntries.push({
          id: `SO-${order.id}`,
          date: order.order_date,
          type: 'debit',
          amount: amount,
          partyName: customerName,
          partyType: 'customer',
          partyBalance: 0
        });
      });
      
      // Add ALL purchase transaction entries
      allPurchaseRecords.forEach((record: any) => {
        const supplierName = record.supplier || 'Unknown Supplier';
        const amount = parseFloat(record.total_amount) || 0;
        
        ledgerEntries.push({
          id: `PR-${record.id}`,
          date: record.record_date,
          type: 'credit',
          amount: amount,
          partyName: supplierName,
          partyType: 'supplier',
          partyBalance: 0
        });
      });
      
      // Add ALL payment entries
      if (allPayments) {
        allPayments.forEach((payment: any) => {
          let partyName: string;
          let partyType: 'customer' | 'supplier' | 'expense';
          let entryType: 'debit' | 'credit';

          if (payment.type === 'expense') {
            partyName = payment.party_name || 'General Expense';
            partyType = 'expense';
            entryType = 'debit';
          } else if (payment.type === 'received') {
            partyName = payment.party_name || 'Unknown Customer';
            partyType = 'customer';
            entryType = 'credit';
          } else {
            partyName = payment.party_name || 'Unknown Supplier';
            partyType = 'supplier';
            entryType = 'debit';
          }

          const amount = parseFloat(payment.amount.toString()) || 0;

          ledgerEntries.push({
            id: `PAY-${payment.id}`,
            date: payment.payment_date,
            type: entryType,
            amount: amount,
            partyName,
            partyType,
            partyBalance: 0
          });
        });
      }
      
      // Sort entries by date (oldest first) for balance calculation
      ledgerEntries.sort((a, b) => {
        const dateA = new Date(a.date).getTime();
        const dateB = new Date(b.date).getTime();
        if (dateA !== dateB) {
          return dateA - dateB;
        }
        return a.id.localeCompare(b.id);
      });
      
      // Calculate running balances for each party (same logic as Ledger page)
      const partyBalances: { [key: string]: number } = {};
      
      // Initialize opening balances for customers
      allCustomers.forEach((cust: any) => {
        const partyKey = `customer-${cust.name}`;
        const openingBalance = cust.opening_balance || 0;
        const openingBalanceType = cust.opening_balance_type || 'debit';
        
        // For customers: debit means they owe us (negative), credit means we owe them (positive)
        partyBalances[partyKey] = openingBalanceType === 'debit' ? -openingBalance : openingBalance;
      });
      
      // Initialize opening balances for suppliers
      allSuppliers.forEach((supplier: any) => {
        const partyKey = `supplier-${supplier.company_name}`;
        const openingBalance = supplier.opening_balance || 0;
        const openingBalanceType = supplier.opening_balance_type || 'debit';
        
        // For suppliers: debit means we owe them (negative), credit means they owe us (positive)
        partyBalances[partyKey] = openingBalanceType === 'debit' ? -openingBalance : openingBalance;
      });
      
      // Process all entries to calculate running balances
      ledgerEntries.forEach(entry => {
        const partyKey = `${entry.partyType}-${entry.partyName}`;
        
        if (!partyBalances[partyKey]) {
          partyBalances[partyKey] = 0;
        }
        
        // Update running balance: Previous Balance - Debit + Credit
        if (entry.type === 'debit') {
          partyBalances[partyKey] = partyBalances[partyKey] - entry.amount;
        } else {
          partyBalances[partyKey] = partyBalances[partyKey] + entry.amount;
        }
        
        entry.partyBalance = partyBalances[partyKey];
      });
      
      // Return the final balance for this specific customer
      const customerKey = `customer-${customer.name}`;
      return partyBalances[customerKey] || 0;
      
    } catch (error) {
      console.error('Error calculating customer outstanding balance:', error);
      // Fallback to current calculation
      const orderStats = calculateOrderStats();
      const paymentStats = calculatePaymentStats();
      const openingBalance = customer.opening_balance || 0;
      const openingBalanceWithSign = customer.opening_balance_type === 'debit' ? -openingBalance : openingBalance;
      return openingBalanceWithSign - orderStats.totalValue + paymentStats.totalReceived;
    }
  };

  const calculateOutstandingBalance = () => {
    if (!customer) return 0;
    
    const orderStats = calculateOrderStats();
    const paymentStats = calculatePaymentStats();
    
    // Get opening balance with proper sign
    const openingBalance = customer.opening_balance || 0;
    const openingBalanceWithSign = customer.opening_balance_type === 'debit' ? -openingBalance : openingBalance;
    
    // For customers: Outstanding = Opening Balance - Sales Orders + Payments Received
    // Negative means customer owes us, positive means we owe customer
    const outstanding = openingBalanceWithSign - orderStats.totalValue + paymentStats.totalReceived;
    
    return outstanding;
  };

  const formatOutstandingBalance = (amount: number) => {
    if (amount === 0) return { text: '₹0.00', color: 'text-gray-600' };
    
    const absAmount = Math.abs(amount);
    const formattedAmount = formatCurrency(absAmount);
    
    if (amount < 0) {
      // Customer owes us
      return { text: `₹${formattedAmount}`, color: 'text-red-600' };
    } else {
      // We owe customer
      return { text: `₹${formattedAmount}`, color: 'text-green-600' };
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading customer details...</div>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Customer not found</div>
      </div>
    );
  }

  const orderStats = calculateOrderStats();
  const paymentStats = calculatePaymentStats();
  const availableCredit = getAvailableCredit();
  const creditUtilization = getCreditUtilization();

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/customers')}
            className="text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <ArrowLeft className="h-6 w-6" />
          </button>
          <div className="flex items-center">
            <User className="h-6 w-6 text-green-600 mr-2" />
            <h1 className="text-xl sm:text-2xl font-bold text-gray-800">Customer Details</h1>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <button
            onClick={() => navigate(`/customers/edit/${id}`)}
            className="bg-blue-600 text-white rounded-md px-3 sm:px-4 py-2 text-sm font-medium hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center"
          >
            <Edit className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">Edit Customer</span>
            <span className="sm:hidden">Edit</span>
          </button>
          <button
            onClick={() => navigate('/sales/new')}
            className="bg-green-600 text-white rounded-md px-3 sm:px-4 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center justify-center"
          >
            <ShoppingCart className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">New Sales Order</span>
            <span className="sm:hidden">New Order</span>
          </button>
          <button
            onClick={() => window.print()}
            className="bg-gray-600 text-white rounded-md px-3 sm:px-4 py-2 text-sm font-medium hover:bg-gray-700 transition-colors duration-200 flex items-center justify-center"
          >
            <FileText className="h-4 w-4 mr-1" />
            Print
          </button>
        </div>
      </div>

      <div className="space-y-6">
        {/* Main Content */}
        <div className="space-y-6">
          {/* Customer Information */}
          <div className="bg-white shadow-sm rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900 flex items-center">
                <User className="h-5 w-5 text-green-600 mr-2" />
                Customer Information
              </h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Customer Name</label>
                    <p className="text-sm text-gray-900">{customer.name}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Customer Type</label>
                    <p className="text-sm text-gray-900 capitalize">{customer.customer_type}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Phone</label>
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 text-gray-400 mr-2" />
                      <p className="text-sm text-gray-900">{customer.contact}</p>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Email</label>
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 text-gray-400 mr-2" />
                      <p className="text-sm text-gray-900">{customer.email}</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Address</label>
                    <div className="flex items-start">
                      <MapPin className="h-4 w-4 text-gray-400 mr-2 mt-0.5" />
                      <p className="text-sm text-gray-900">{customer.address}</p>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">GST Number</label>
                    <p className="text-sm text-gray-900">{customer.gst_number || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">PAN Number</label>
                    <p className="text-sm text-gray-900">{customer.pan_number || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Status</label>
                    <span className={`inline-flex px-3 py-1 text-xs leading-5 font-semibold rounded-full ${getStatusColor(customer.status)}`}>
                      {customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Delivery Addresses */}
              {customer.delivery_addresses && customer.delivery_addresses.length > 0 && (
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Delivery Addresses</h3>
                  <div className="space-y-3">
                    {customer.delivery_addresses.map((addr: any, index: number) => (
                      <div key={index} className="bg-white rounded-lg p-3 border">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start">
                            <MapPin className="h-4 w-4 text-gray-400 mr-2 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">
                                {addr.label}
                                {addr.is_default && (
                                  <span className="ml-2 inline-flex px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                    Default
                                  </span>
                                )}
                              </p>
                              <p className="text-sm text-gray-600 mt-1">{addr.address}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Notes */}
              {customer.notes && (
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Notes</h3>
                  <p className="text-sm text-gray-600 whitespace-pre-wrap">{customer.notes}</p>
                </div>
              )}
            </div>
          </div>

          {/* Tabs */}
          <div className="bg-white shadow-sm rounded-lg">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex flex-col sm:flex-row sm:space-x-8 px-6">
                <button
                  onClick={() => setActiveTab('overview')}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'overview'
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="hidden sm:inline">Financial Overview</span>
                  <span className="sm:hidden">Overview</span>
                </button>
                <button
                  onClick={() => setActiveTab('orders')}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'orders'
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="hidden sm:inline">Sales Orders ({orderStats.totalOrders})</span>
                  <span className="sm:hidden">Orders ({orderStats.totalOrders})</span>
                </button>
                <button
                  onClick={() => setActiveTab('payments')}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'payments'
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="hidden sm:inline">Payment History ({paymentStats.totalPayments})</span>
                  <span className="sm:hidden">Payments ({paymentStats.totalPayments})</span>
                </button>
              </nav>
            </div>

            <div className="p-6">
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  {/* Summary Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="bg-blue-50 rounded-lg p-4">
                      <div className="flex items-center">
                        <ShoppingCart className="h-8 w-8 text-blue-600" />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-blue-600">Total Orders</p>
                          <p className="text-2xl font-bold text-blue-900">{orderStats.totalOrders}</p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-green-50 rounded-lg p-4">
                      <div className="flex items-center">
                        <TrendingUp className="h-8 w-8 text-green-600" />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-green-600">Completed</p>
                          <p className="text-2xl font-bold text-green-900">{orderStats.completedOrders}</p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-yellow-50 rounded-lg p-4">
                      <div className="flex items-center">
                        <Package className="h-8 w-8 text-yellow-600" />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-yellow-600">Pending</p>
                          <p className="text-2xl font-bold text-yellow-900">{orderStats.pendingOrders}</p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-purple-50 rounded-lg p-4">
                      <div className="flex items-center">
                        <DollarSign className="h-8 w-8 text-purple-600" />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-purple-600">Total Value</p>
                          <p className="text-lg font-bold text-purple-900">₹{formatCurrency(orderStats.totalValue)}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">Financial Summary</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Opening Balance:</span>
                          <span className={`text-sm font-medium ${formatOpeningBalance(customer.opening_balance || 0, customer.opening_balance_type || 'debit').color}`}>
                            {formatOpeningBalance(customer.opening_balance || 0, customer.opening_balance_type || 'debit').text}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Total Orders Value:</span>
                          <span className="text-sm font-medium text-gray-900">₹{formatCurrency(orderStats.totalValue)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Total Payments Received:</span>
                          <span className="text-sm font-medium text-gray-900">₹{formatCurrency(paymentStats.totalReceived)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Outstanding Balance:</span>
                          <span className={`text-sm font-medium ${formatOutstandingBalance(outstandingBalance).color}`}>
                            {formatOutstandingBalance(outstandingBalance).text}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">Credit Information</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Credit Limit:</span>
                          <span className="text-sm font-medium text-gray-900">₹{formatCurrency(customer.credit_limit)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Available Credit:</span>
                          <span className="text-sm font-medium text-green-600">₹{formatCurrency(availableCredit)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Credit Utilization:</span>
                          <span className={`text-sm font-medium ${creditUtilization > 80 ? 'text-red-600' : creditUtilization > 60 ? 'text-yellow-600' : 'text-green-600'}`}>
                            {creditUtilization.toFixed(1)}%
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Payment Terms:</span>
                          <span className="text-sm font-medium text-gray-900">{customer.payment_terms} days</span>
                        </div>
                      </div>
                      
                      {/* Credit Utilization Bar */}
                      <div className="mt-3">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${creditUtilization > 80 ? 'bg-red-500' : creditUtilization > 60 ? 'bg-yellow-500' : 'bg-green-500'}`}
                            style={{ width: `${Math.min(creditUtilization, 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {creditUtilization > 80 && (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center">
                        <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
                        <span className="text-sm font-medium text-red-800">
                          High credit utilization - consider reviewing credit terms
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'orders' && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-gray-900">Sales Orders</h3>
                    <span className="text-sm text-gray-500">
                      Total: ₹{formatCurrency(orderStats.totalValue)}
                    </span>
                  </div>
                  
                  {salesOrders.length > 0 ? (
                    <>
                      {/* Desktop Table View */}
                      <div className="hidden md:block overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Order Details
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Items
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {salesOrders.slice(0, 10).map((order) => (
                              <tr key={order.id} className="hover:bg-gray-50">
                                <td className="px-6 py-4 max-w-[300px]">
                                  <div className="flex items-center">
                                    <ShoppingCart className="h-4 w-4 text-gray-400 mr-2" />
                                    <div>
                                      <div className="text-sm font-medium text-gray-900">{order.order_number}</div>
                                      <div className="text-sm text-gray-500">{formatDateTime(order.order_date)}</div>
                                    </div>
                                  </div>
                                </td>
                                <td className="px-6 py-4">
                                  <div className="text-sm text-gray-900">
                                    {order.sales_order_items?.slice(0, 2).map((item, index) => (
                                      <div key={index}>
                                        {item.product_name} - {item.quantity} {item.unit_type === 'box' ? 'boxes' : 'kg'}
                                      </div>
                                    )) || <div className="text-gray-500">No items</div>}
                                    {(order.sales_order_items?.length || 0) > 2 && (
                                      <div className="text-sm text-gray-500">
                                        +{(order.sales_order_items?.length || 0) - 2} more items
                                      </div>
                                    )}
                                  </div>
                                </td>
                                <td className="px-6 py-4 max-w-[300px] text-sm text-gray-900">
                                  ₹{formatCurrency(order.total_amount)}
                                </td>
                                <td className="px-6 py-4 max-w-[300px]">
                                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getOrderStatusColor(order.status)}`}>
                                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                  </span>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>

                      {/* Mobile Card View */}
                      <div className="md:hidden space-y-4">
                        {salesOrders.slice(0, 10).map((order) => (
                          <div key={order.id} className="bg-white border rounded-lg p-4 shadow-sm">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex items-center">
                                <ShoppingCart className="h-5 w-5 text-gray-400 mr-2" />
                                <div>
                                  <p className="text-sm font-medium text-gray-900">{order.order_number}</p>
                                  <p className="text-xs text-gray-500">{formatDate(order.order_date)}</p>
                                </div>
                              </div>
                              <span className={`px-2 py-1 text-xs leading-4 font-semibold rounded-full ${getOrderStatusColor(order.status)}`}>
                                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                              </span>
                            </div>
                            
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-500">Amount:</span>
                                <span className="text-sm font-medium text-gray-900">₹{formatCurrency(order.total_amount)}</span>
                              </div>
                              
                              {order.sales_order_items && order.sales_order_items.length > 0 && (
                                <div>
                                  <span className="text-sm text-gray-500">Items:</span>
                                  <div className="mt-1 text-sm text-gray-900">
                                    {order.sales_order_items.slice(0, 2).map((item, index) => (
                                      <div key={index}>
                                        {item.product_name} - {item.quantity} {item.unit_type === 'box' ? 'boxes' : 'kg'}
                                      </div>
                                    ))}
                                    {order.sales_order_items.length > 2 && (
                                      <div className="text-sm text-gray-500">
                                        +{order.sales_order_items.length - 2} more items
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <ShoppingCart className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                      <p>No sales orders found for this customer.</p>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'payments' && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-gray-900">Payment History</h3>
                    <span className="text-sm text-gray-500">
                      Total Received: ₹{formatCurrency(paymentStats.totalReceived)}
                    </span>
                  </div>
                  
                  {payments.length > 0 ? (
                    <>
                      {/* Desktop Table View */}
                      <div className="hidden md:block overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Payment Date
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount & Type
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Mode
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {payments.slice(0, 10).map((payment) => (
                              <tr key={payment.id} className="hover:bg-gray-50">
                                <td className="px-6 py-4 max-w-[300px]">
                                  <div className="flex items-center">
                                    <DollarSign className="h-4 w-4 text-gray-400 mr-2" />
                                    <div>
                                      <div className="text-sm font-medium text-gray-900">
                                        {formatDateTime(payment.payment_date)}
                                      </div>
                                    </div>
                                  </div>
                                </td>
                                <td className="px-6 py-4 max-w-[300px]">
                                  <div className={`text-sm font-medium ${
                                    payment.type === 'received' ? 'text-green-600' : 'text-red-600'
                                  }`}>
                                    {payment.type === 'received' ? '+' : '-'}₹{formatCurrency(payment.amount)}
                                  </div>
                                  <div className="text-sm text-gray-500 capitalize">{payment.type}</div>
                                </td>
                                <td className="px-6 py-4 max-w-[300px] text-sm text-gray-900">
                                  {getPaymentModeDisplay(payment.mode)}
                                </td>
                                <td className="px-6 py-4 max-w-[300px]">
                                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPaymentStatusColor(payment.status)}`}>
                                    {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                                  </span>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>

                      {/* Mobile Card View */}
                      <div className="md:hidden space-y-4">
                        {payments.slice(0, 10).map((payment) => (
                          <div key={payment.id} className="bg-white border rounded-lg p-4 shadow-sm">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex items-center">
                                <DollarSign className="h-5 w-5 text-gray-400 mr-2" />
                                <div>
                                  <p className="text-sm font-medium text-gray-900">{formatDate(payment.payment_date)}</p>
                                  <p className="text-xs text-gray-500">{payment.reference_number || 'No reference'}</p>
                                </div>
                              </div>
                              <span className={`px-2 py-1 text-xs leading-4 font-semibold rounded-full ${getPaymentStatusColor(payment.status)}`}>
                                {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                              </span>
                            </div>
                            
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-500">Amount:</span>
                                <span className={`text-sm font-medium ${
                                  payment.type === 'received' ? 'text-green-600' : 'text-red-600'
                                }`}>
                                  {payment.type === 'received' ? '+' : '-'}₹{formatCurrency(payment.amount)}
                                </span>
                              </div>
                              
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-500">Type:</span>
                                <span className="text-sm font-medium text-gray-900 capitalize">{payment.type}</span>
                              </div>
                              
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-500">Mode:</span>
                                <span className="text-sm font-medium text-gray-900">{getPaymentModeDisplay(payment.mode)}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <DollarSign className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                      <p>No payment records found for this customer.</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

      </div>
    </div>
  );
};

export default ViewCustomer;
