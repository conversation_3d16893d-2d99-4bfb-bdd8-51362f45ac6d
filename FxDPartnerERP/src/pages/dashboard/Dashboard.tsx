import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Truck, 
  Package, 
  DollarSign,
  CreditCard,
  Calendar,
  Clock,
  Filter
} from 'lucide-react';

// Import dashboard components
import KPICard from '../../components/dashboard/KPICard';
import VehicleArrivalsCard from '../../components/dashboard/VehicleArrivalsCard';
import Dropdown from '../../components/ui/Dropdown';
import DashboardDatePicker from '../../components/dashboard/DashboardDatePicker';

// Import dashboard service and types
import { DashboardService } from '../../services/api/dashboardService';
import { 
  CustomerSupplierView,
  ArrivalView,
  InventoryView,
  SalesPaymentView,
  PurchasePaymentView
} from '../../types/dashboard.types';

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  });
  const [quickFilter, setQuickFilter] = useState('today');
  
  const [dashboardData, setDashboardData] = useState<{
    customerSupplierView: CustomerSupplierView | null;
    arrivalView: ArrivalView | null;
    inventoryView: InventoryView | null;
    salesPaymentView: SalesPaymentView | null;
    purchasePaymentView: PurchasePaymentView | null;
  }>({
    customerSupplierView: null,
    arrivalView: null,
    inventoryView: null,
    salesPaymentView: null,
    purchasePaymentView: null
  });

  const currentDate = new Date().toLocaleDateString('en-IN', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const currentTime = new Date().toLocaleTimeString('en-IN', {
    hour: '2-digit',
    minute: '2-digit'
  });

  useEffect(() => {
    loadDashboardData();
  }, [dateRange, quickFilter]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const filters = getDateFilters();
      const data = await DashboardService.getAllSimpleDashboardData(filters);
      setDashboardData(data);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDateFilters = () => {
    const today = new Date();
    const filters: { startDate?: string; endDate?: string } = {};

    switch (quickFilter) {
      case 'today':
        filters.startDate = today.toISOString().split('T')[0];
        filters.endDate = today.toISOString().split('T')[0];
        break;
      case 'yesterday': {
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        filters.startDate = yesterday.toISOString().split('T')[0];
        filters.endDate = yesterday.toISOString().split('T')[0];
        break;
      }
      case 'week': {
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - 7);
        filters.startDate = weekStart.toISOString().split('T')[0];
        filters.endDate = today.toISOString().split('T')[0];
        break;
      }
      case 'month': {
        const monthStart = new Date(today);
        monthStart.setDate(today.getDate() - 30);
        filters.startDate = monthStart.toISOString().split('T')[0];
        filters.endDate = today.toISOString().split('T')[0];
        break;
      }
      case 'custom':
        if (dateRange.startDate && dateRange.endDate) {
          filters.startDate = dateRange.startDate;
          filters.endDate = dateRange.endDate;
        }
        break;
    }

    return filters;
  };

  const handleQuickFilterChange = (filter: string) => {
    setQuickFilter(filter);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="space-y-3 h-screen overflow-auto">
      {/* Compact Header with Filters */}
      <div className="bg-gradient-to-r from-green-600 to-green-700 rounded-lg p-3 text-white">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div className="mb-2 lg:mb-0">
            <h1 className="text-lg lg:text-xl font-bold mb-1">Business Dashboard</h1>
            <div className="flex items-center space-x-3 text-green-100 text-xs">
              <div className="flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                <span>{currentDate}</span>
              </div>
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                <span>{currentTime}</span>
              </div>
            </div>
          </div>
          
          {/* Compact Filters */}
          <div className="flex flex-wrap gap-2">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-white" />
              <div className="bg-white rounded-md">
                <Dropdown
                  options={[
                    { value: 'today', label: 'Today' },
                    { value: 'yesterday', label: 'Yesterday' },
                    { value: 'week', label: 'Last Week' },
                    { value: 'month', label: 'Last Month' },
                    { value: 'custom', label: 'Custom Range' }
                  ]}
                  value={quickFilter}
                  onChange={handleQuickFilterChange}
                  placeholder="Select period"
                  className="text-sm min-w-[180px] border-0 text-green-600"
                />
              </div>
            </div>
            
            {quickFilter === 'custom' && (
              <div className="flex gap-2 flex-wrap">
                <div className="min-w-[140px] dashboard-date-input">
                  <DashboardDatePicker
                    value={dateRange.startDate}
                    onChange={(value: string) => setDateRange(prev => ({ ...prev, startDate: value.split('T')[0] }))}
                    placeholder="Start date"
                    className="text-xs"
                  />
                </div>
                <div className="min-w-[140px] dashboard-date-input">
                  <DashboardDatePicker
                    value={dateRange.endDate}
                    onChange={(value: string) => setDateRange(prev => ({ ...prev, endDate: value.split('T')[0] }))}
                    placeholder="End date"
                    className="text-xs"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* First Row: Sales & Payments and Purchase & Payments */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-3">
        {/* 1. Sales & Payments */}
        <div className="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
          <h2 className="text-sm font-semibold text-gray-800 mb-2">Sales & Payments</h2>
          <div className="grid grid-cols-2 gap-2">
            <KPICard 
              title="Total Sales" 
              value={formatCurrency(dashboardData.salesPaymentView?.totalSales || 0)}
              subtitle="revenue"
              icon={<DollarSign />}
              loading={loading}
              color="green"
            />
            <KPICard 
              title="Collected" 
              value={formatCurrency(dashboardData.salesPaymentView?.totalPaymentsCollected || 0)}
              subtitle="received"
              icon={<CreditCard />}
              loading={loading}
              color="blue"
            />
            <KPICard 
              title="Pending" 
              value={formatCurrency(dashboardData.salesPaymentView?.pendingPayments || 0)}
              subtitle="outstanding"
              icon={<CreditCard />}
              loading={loading}
              color="red"
            />
            <KPICard 
              title="Collection Rate" 
              value={`${dashboardData.salesPaymentView?.collectionRate || 0}%`}
              subtitle="efficiency"
              icon={<DollarSign />}
              loading={loading}
              color="purple"
            />
          </div>
        </div>

        {/* 2. Purchase & Payments */}
        <div className="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
          <h2 className="text-sm font-semibold text-gray-800 mb-2">Purchase & Payments</h2>
          <div className="grid grid-cols-2 gap-2">
            <KPICard 
              title="Total Purchases" 
              value={formatCurrency(dashboardData.purchasePaymentView?.totalPurchases || 0)}
              subtitle="spent"
              icon={<DollarSign />}
              loading={loading}
              color="red"
            />
            <KPICard 
              title="Paid" 
              value={formatCurrency(dashboardData.purchasePaymentView?.totalPaymentsMade || 0)}
              subtitle="amount paid"
              icon={<CreditCard />}
              loading={loading}
              color="blue"
            />
            <KPICard 
              title="Pending" 
              value={formatCurrency(dashboardData.purchasePaymentView?.pendingPayments || 0)}
              subtitle="outstanding"
              icon={<CreditCard />}
              loading={loading}
              color="orange"
            />
            <KPICard 
              title="Payment Rate" 
              value={`${dashboardData.purchasePaymentView?.paymentRate || 0}%`}
              subtitle="efficiency"
              icon={<DollarSign />}
              loading={loading}
              color="green"
            />
          </div>
        </div>
      </div>

      {/* Second Row: Vehicle Arrivals and Inventory Overview */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-3">
        {/* 3. Vehicle Arrivals - New Enhanced Card */}
        <VehicleArrivalsCard 
          data={dashboardData.arrivalView}
          loading={loading}
        />

        {/* 4. Inventory Overview */}
        <div className="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
          <h2 className="text-sm font-semibold text-gray-800 mb-2">Inventory Overview</h2>
          <div className="grid grid-cols-2 gap-2">
            <KPICard 
              title="Opening Stock" 
              value={dashboardData.inventoryView?.openingStock || 0}
              subtitle="units start"
              icon={<Package />}
              loading={loading}
              color="blue"
            />
            <KPICard 
              title="New Arrivals" 
              value={dashboardData.inventoryView?.newArrivals || 0}
              subtitle="received"
              icon={<Truck />}
              loading={loading}
              color="green"
            />
            <KPICard 
              title="Dispatched" 
              value={dashboardData.inventoryView?.totalDispatched || 0}
              subtitle="sold"
              icon={<Package />}
              loading={loading}
              color="orange"
            />
            <KPICard 
              title="Current Stock" 
              value={dashboardData.inventoryView?.currentInventory || 0}
              subtitle="in stock"
              icon={<Package />}
              loading={loading}
              color="purple"
            />
          </div>
        </div>
      </div>

      {/* Customer and Supplier Section - Full Width at Bottom */}
      <div className="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
        <h2 className="text-sm font-semibold text-gray-800 mb-2">Customer & Supplier Overview</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          <KPICard 
            title="Total Customers" 
            value={dashboardData.customerSupplierView?.customers.total || 0}
            subtitle={`${dashboardData.customerSupplierView?.customers.active || 0} active`}
            icon={<Users />}
            loading={loading}
            color="blue"
          />
          <KPICard 
            title="New Customers" 
            value={dashboardData.customerSupplierView?.customers.new || 0}
            subtitle="in period"
            icon={<Users />}
            loading={loading}
            color="green"
          />
          <KPICard 
            title="Total Suppliers" 
            value={dashboardData.customerSupplierView?.suppliers.total || 0}
            subtitle={`${dashboardData.customerSupplierView?.suppliers.active || 0} active`}
            icon={<Truck />}
            loading={loading}
            color="orange"
          />
          <KPICard 
            title="New Suppliers" 
            value={dashboardData.customerSupplierView?.suppliers.new || 0}
            subtitle="in period"
            icon={<Truck />}
            loading={loading}
            color="purple"
          />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
