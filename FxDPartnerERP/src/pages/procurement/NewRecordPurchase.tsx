import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { FileText, ArrowLeft, Plus, Trash2, Package } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { vehicleArrivalService } from '../../services/api/vehicleArrivalService';
import { purchaseRecordService } from '../../services/api/purchaseRecordService';
import { supplierService } from '../../services/api/supplierService';
import { organizationService, Organization } from '../../services/api/organizationService';
import EditableDropdown from '../../components/ui/EditableDropdown';
import Dropdown from '../../components/ui/Dropdown';
import DatePicker from '../../components/forms/DateTimePicker';
import PurchaseRecordCreationModal from '../../components/modals/PurchaseRecordCreationModal';

interface PurchaseRecordItem {
  id: string;
  productId: string;
  productName: string;
  skuId: string;
  skuCode: string;
  category: string;
  quantity: number;
  unitType: string;
  totalWeight: number;
  marketPrice: number;
  unitPrice: number;
  total: number;
}

interface AdditionalCost {
  id: string;
  name: string;
  amount: number;
  type: 'fixed' | 'percentage' | 'per_box';
  calculatedAmount: number;
}

interface VehicleArrival {
  id: string;
  supplier: string;
  arrival_time: string;
  vehicle_number: string | null;
  reference_number: string | null;
  items: Array<{
    id: string;
    product: {
      id: string;
      name: string;
      category: string;
    };
    sku: {
      id: string;
      code: string;
    };
    quantity: number;
    unit_type: string;
    total_weight: number;
    final_quantity?: number;
    final_total_weight?: number;
  }>;
}

interface Supplier {
  id: string;
  company_name: string;
  payment_terms: number;
}

const NewRecordPurchase: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const vehicleIdFromUrl = searchParams.get('vehicleId');
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [vehicleArrivals, setVehicleArrivals] = useState<VehicleArrival[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [organizationData, setOrganizationData] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);

  const [formData, setFormData] = useState({
    vehicleArrivalId: '',
    vehicleArrival: '',
    supplierId: '',
    supplier: '',
    recordDate: new Date().toISOString().slice(0, 16),
    arrivalTimestamp: '',
    pricingModel: 'fixed',
    notes: ''
  });

  const [items, setItems] = useState<PurchaseRecordItem[]>([]);
  const [additionalCosts, setAdditionalCosts] = useState<AdditionalCost[]>([]);
  const [showCreationModal, setShowCreationModal] = useState(false);

  useEffect(() => {
    loadInitialData();
  }, []);

  // Auto-populate from URL parameter
  useEffect(() => {
    if (vehicleIdFromUrl && vehicleArrivals.length > 0) {
      const matchingVehicle = vehicleArrivals.find(arrival => arrival.id === vehicleIdFromUrl);
      if (matchingVehicle) {
        const displayValue = generateVehicleArrivalDisplayValue(matchingVehicle);
        handleVehicleArrivalChange(displayValue);
      }
    }
  }, [vehicleIdFromUrl, vehicleArrivals]);

  // Auto-populate costs when pricing model changes
  useEffect(() => {
    if (organizationData && formData.pricingModel) {
      loadPredefinedCosts(formData.pricingModel);
    }
  }, [formData.pricingModel, organizationData]);

  const loadInitialData = async () => {
    try {
      const [arrivalsData, suppliersData, organizationData] = await Promise.all([
        vehicleArrivalService.getAll(),
        supplierService.getAll(),
        organizationService.getCurrentOrganization()
      ]);
      
      // Filter completed arrivals that don't have purchase records yet
      interface ArrivalWithStatus extends VehicleArrival {
        status?: string;
      }
      const completedArrivals = arrivalsData?.filter((arrival: ArrivalWithStatus) => 
        arrival.status === 'completed'
      ) || [];
      
      setVehicleArrivals(completedArrivals);
      setSuppliers(suppliersData || []);
      setOrganizationData(organizationData);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load required data');
    } finally {
      setLoading(false);
    }
  };

  const generateVehicleArrivalDisplayValue = (arrival: VehicleArrival) => {
    const date = new Date(arrival.arrival_time).toLocaleDateString();
    const refNumber = arrival.reference_number || 'No Ref';
    const itemCount = arrival.items?.length || 0;
    const shortId = arrival.id.slice(-6);
    
    return `${arrival.supplier} - ${date} (REF: ${refNumber}) [${itemCount} items] - VA#${shortId}`;
  };

  const loadPredefinedCosts = (pricingModel: string) => {
    if (!organizationData) return;

    let predefinedCosts: Array<{
      name?: string;
      amount?: number;
      type?: 'fixed' | 'percentage' | 'per_box';
    }> = [];
    
    if (pricingModel === 'fixed' && organizationData.fixed_price_costs) {
      predefinedCosts = organizationData.fixed_price_costs;
    } else if (pricingModel === 'commission' && organizationData.commission_costs) {
      predefinedCosts = organizationData.commission_costs;
    }

    if (predefinedCosts.length > 0) {
      const newCosts: AdditionalCost[] = predefinedCosts.map((cost, index) => ({
        id: `predefined_${Date.now()}_${index}`,
        name: cost.name || '',
        amount: cost.amount || 0,
        type: cost.type || 'fixed',
        calculatedAmount: 0
      }));

      setAdditionalCosts(newCosts);
      
      // Recalculate amounts for the new costs
      setTimeout(() => {
        newCosts.forEach(cost => {
          const itemsSubtotal = calculateItemsSubtotal();
          const totalBoxes = items.reduce((sum, item) => sum + (item.unitType === 'box' ? item.quantity : 0), 0);
          
          let calculatedAmount = 0;
          switch (cost.type) {
            case 'fixed':
              calculatedAmount = Number(cost.amount) || 0;
              break;
            case 'percentage':
              calculatedAmount = (itemsSubtotal * (Number(cost.amount) || 0)) / 100;
              break;
            case 'per_box':
              calculatedAmount = (Number(cost.amount) || 0) * totalBoxes;
              break;
          }
          
          handleCostChange(cost.id, 'calculatedAmount', calculatedAmount);
        });
      }, 100);
    } else {
      // Clear costs if no predefined costs for this model
      setAdditionalCosts([]);
    }
  };

  const handleVehicleArrivalChange = (value: string) => {
    if (value === '') {
      // Clear selection
      setFormData(prev => ({
        ...prev,
        vehicleArrivalId: '',
        vehicleArrival: '',
        arrivalTimestamp: ''
      }));
      setItems([]);
      return;
    }

    // Find arrival by matching the display value
    const arrival = vehicleArrivals.find(a => {
      const displayValue = generateVehicleArrivalDisplayValue(a);
      return displayValue === value;
    });

    if (arrival) {
      // Find supplier by name
      const supplier = suppliers.find(s => s.company_name === arrival.supplier);
      
      setFormData(prev => ({
        ...prev,
        vehicleArrivalId: arrival.id,
        vehicleArrival: value,
        supplierId: supplier?.id || '',
        supplier: arrival.supplier,
        arrivalTimestamp: arrival.arrival_time
      }));

      // Populate items from vehicle arrival using final_quantity
      const arrivalItems = (arrival.items || []).map(item => ({
        id: `item_${Date.now()}_${Math.random()}`,
        productId: item.product?.id || '',
        productName: item.product?.name || '',
        skuId: item.sku?.id || '',
        skuCode: item.sku?.code || '',
        category: item.product?.category || '',
        quantity: item.final_quantity || item.quantity || 0,
        unitType: item.unit_type || '',
        totalWeight: item.final_total_weight || item.total_weight || 0,
        marketPrice: 0,
        unitPrice: 0,
        total: 0
      }));

      setItems(arrivalItems);
    }
  };

  const handleSupplierChange = (value: string) => {
    // Check if the value is a supplier ID or a supplier name
    let supplier = suppliers.find(s => s.id === value);
    
    // If not found by ID, try to find by name (for custom values or when set from vehicle arrival)
    if (!supplier) {
      supplier = suppliers.find(s => s.company_name === value);
    }
    
    if (supplier) {
      setFormData(prev => ({
        ...prev,
        supplierId: supplier!.id,
        supplier: supplier!.company_name
      }));
    } else {
      // If it's a custom supplier name (not in the list)
      setFormData(prev => ({
        ...prev,
        supplierId: '',
        supplier: value
      }));
    }
  };

  const handleItemChange = (id: string, field: keyof PurchaseRecordItem, value: string | number) => {
    setItems(prev => prev.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };
        
        // Calculate total based on quantity and unit price
        updatedItem.total = updatedItem.quantity * updatedItem.unitPrice;
        
        return updatedItem;
      }
      return item;
    }));
  };

  const handleAddCost = () => {
    const newCost: AdditionalCost = {
      id: `cost_${Date.now()}`,
      name: '',
      amount: 0,
      type: 'fixed',
      calculatedAmount: 0
    };
    setAdditionalCosts(prev => [...prev, newCost]);
  };

  const handleRemoveCost = (id: string) => {
    setAdditionalCosts(prev => prev.filter(cost => cost.id !== id));
  };

  const handleCostChange = (id: string, field: keyof AdditionalCost, value: string | number) => {
    setAdditionalCosts(prev => prev.map(cost => {
      if (cost.id === id) {
        const updatedCost = { ...cost, [field]: value };
        
        // Calculate the actual cost amount
        const itemsSubtotal = calculateItemsSubtotal();
        const totalBoxes = items.reduce((sum, item) => sum + (item.unitType === 'box' ? item.quantity : 0), 0);
        
        switch (updatedCost.type) {
          case 'fixed':
            updatedCost.calculatedAmount = Number(updatedCost.amount) || 0;
            break;
          case 'percentage':
            updatedCost.calculatedAmount = (itemsSubtotal * (Number(updatedCost.amount) || 0)) / 100;
            break;
          case 'per_box':
            updatedCost.calculatedAmount = (Number(updatedCost.amount) || 0) * totalBoxes;
            break;
        }
        
        return updatedCost;
      }
      return cost;
    }));
  };

  const calculateItemsSubtotal = () => {
    return items.reduce((sum, item) => {
      const itemTotal = Number(item.total) || 0;
      return sum + itemTotal;
    }, 0);
  };

  const calculateAdditionalCostsTotal = () => {
    return additionalCosts.reduce((sum, cost) => {
      const costAmount = Number(cost.calculatedAmount) || 0;
      return sum + costAmount;
    }, 0);
  };

  const calculateTotal = () => {
    const itemsSubtotal = calculateItemsSubtotal();
    const costsTotal = calculateAdditionalCostsTotal();
    return itemsSubtotal - costsTotal;
  };

  const generateRecordNumber = () => {
    const timestamp = Date.now();
    return `PR-${new Date().getFullYear()}-${timestamp.toString().slice(-6)}`;
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.vehicleArrivalId) {
      toast.error('Please select a vehicle arrival');
      return;
    }

    if (!formData.supplierId) {
      toast.error('Please select a supplier');
      return;
    }

    if (items.length === 0) {
      toast.error('Please add at least one item');
      return;
    }

    // Validate items
    for (const item of items) {
      if (item.quantity <= 0 || item.unitPrice <= 0) {
        toast.error('Please complete all item details with valid values');
        return;
      }
    }

    // Show the creation modal instead of submitting directly
    setShowCreationModal(true);
  };

  const handleModalConfirm = async (closureType: 'partial_closure' | 'full_closure', closureNotes?: string) => {
    setIsSubmitting(true);

    try {
      const itemsSubtotal = calculateItemsSubtotal();
      const additionalCostsTotal = calculateAdditionalCostsTotal();
      const totalAmount = calculateTotal();

      // Get default values from supplier or organization
      const selectedSupplier = suppliers.find(s => s.id === formData.supplierId);
      const defaultCommission = organizationData?.default_commission_percentage || 0;
      const paymentTerms = selectedSupplier?.payment_terms || 30;

      // Create purchase record
      const recordData = {
        vehicle_arrival_id: formData.vehicleArrivalId || null,
        supplier_id: formData.supplierId,
        record_number: generateRecordNumber(),
        supplier: formData.supplier,
        record_date: formData.recordDate,
        arrival_timestamp: formData.arrivalTimestamp || formData.recordDate,
        pricing_model: formData.pricingModel,
        default_commission: defaultCommission,
        payment_terms: paymentTerms,
        items_subtotal: itemsSubtotal,
        additional_costs_total: additionalCostsTotal,
        total_amount: totalAmount,
        status: closureType,
        closure_date: closureType === 'full_closure' ? new Date().toISOString() : null,
        closure_notes: closureType === 'full_closure' ? closureNotes || null : null,
        notes: formData.notes || null
      };

      // Prepare items data
      const itemsData = items.map(item => ({
        purchase_record_id: '',
        product_id: item.productId,
        sku_id: item.skuId,
        product_name: item.productName,
        sku_code: item.skuCode,
        category: item.category,
        quantity: item.quantity,
        unit_type: item.unitType,
        total_weight: item.totalWeight,
        market_price: item.marketPrice,
        commission: 0,
        unit_price: item.unitPrice,
        total: item.total
      }));

      // Prepare costs data
      const additionalCostsData = additionalCosts.map(cost => ({
        purchase_record_id: '',
        name: cost.name,
        amount: cost.amount,
        type: cost.type,
        calculated_amount: cost.calculatedAmount
      }));

      await purchaseRecordService.create(recordData, itemsData, additionalCostsData);
      
      toast.success('Purchase record created successfully!');
      navigate('/record-purchase');
    } catch (error) {
      console.error('Error creating purchase record:', error);
      toast.error('Failed to create purchase record. Please try again.');
    } finally {
      setIsSubmitting(false);
      setShowCreationModal(false);
    }
  };

  const handleModalClose = () => {
    if (!isSubmitting) {
      setShowCreationModal(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-6">
      <div className="flex items-center space-x-3 sm:space-x-4">
        <button
          onClick={() => navigate('/record-purchase')}
          className="text-gray-600 hover:text-gray-900 transition-colors duration-200 p-1"
        >
          <ArrowLeft className="h-5 w-5 sm:h-6 sm:w-6" />
        </button>
        <div className="flex items-center">
          <FileText className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mr-2" />
          <h1 className="text-lg sm:text-2xl font-bold text-gray-800">New Purchase Record</h1>
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg">
        <form onSubmit={handleFormSubmit} className="p-4 sm:p-6 space-y-4 sm:space-y-6">
          {/* Basic Information */}
          <div>
            <h2 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Basic Information</h2>
            <div className="grid grid-cols-1 gap-4 sm:gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700">
                  Vehicle Arrival <span className="text-red-500">*</span>
                </label>
                <EditableDropdown
                  options={[
                    { value: '', label: 'Select vehicle arrival' },
                    ...vehicleArrivals.map(arrival => {
                      const displayValue = generateVehicleArrivalDisplayValue(arrival);
                      return {
                        value: displayValue,
                        label: displayValue
                      };
                    })
                  ]}
                  value={formData.vehicleArrival}
                  onChange={(value) => handleVehicleArrivalChange(value)}
                  placeholder="Select or search vehicle arrival..."
                  className="mt-1"
                  allowCustomValue={false}
                />
              </div>

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700">
                  Supplier <span className="text-red-500">*</span>
                </label>
                <EditableDropdown
                  options={[
                    { value: '', label: 'Select a supplier' },
                    ...suppliers.map(supplier => ({
                      value: supplier.company_name,
                      label: supplier.company_name
                    }))
                  ]}
                  value={formData.supplier}
                  onChange={(value) => handleSupplierChange(value)}
                  placeholder="Select or search supplier..."
                  className="mt-1"
                  allowCustomValue={true}
                />
              </div>

              <DatePicker
                label="Record Date"
                value={formData.recordDate}
                onChange={(value: string) => setFormData(prev => ({ ...prev, recordDate: value }))}
                required={true}
                placeholder="Select record date"
                className="mt-1"
              />

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700">
                  Pricing Model
                </label>
                <Dropdown
                  options={[
                    { value: 'fixed', label: 'Fixed Price' },
                    { value: 'commission', label: 'Commission Based' }
                  ]}
                  value={formData.pricingModel}
                  onChange={(value) => setFormData(prev => ({ ...prev, pricingModel: value }))}
                  placeholder="Select pricing model..."
                  className="mt-1"
                />
              </div>
            </div>
          </div>

          {/* Items Section */}
          <div className="border-t pt-4 sm:pt-6">
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Purchase Items</h2>
            </div>

            {/* Desktop Table */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Unit Price (₹)
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total (₹)
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {items.map((item) => (
                    <tr key={item.id}>
                      <td className="px-6 py-4 max-w-[300px]">
                        <div className="flex items-center">
                          <Package className="h-4 w-4 text-gray-400 mr-2" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">{item.productName}</div>
                            <div className="text-sm text-gray-500">{item.skuCode} • {item.category}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 max-w-[300px]">
                        <div className="text-sm text-gray-900">
                          {item.quantity} {item.unitType === 'box' ? 'boxes' : 'kg'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {item.totalWeight} kg total
                        </div>
                      </td>
                      <td className="px-6 py-4 max-w-[300px]">
                        <input
                          type="number"
                          value={item.unitPrice}
                          onChange={(e) => handleItemChange(item.id, 'unitPrice', Number(e.target.value))}
                          min="0"
                          step="0.01"
                          className="block w-24 border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          placeholder="Enter unit price"
                        />
                      </td>
                      <td className="px-6 py-4 max-w-[300px] text-sm text-gray-900">
                        ₹{item.total.toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile/Tablet Cards */}
            <div className="lg:hidden space-y-4">
              {items.map((item) => (
                <div key={item.id} className="bg-gray-50 rounded-lg p-4 border">
                  <div className="space-y-3">
                    {/* Product Info */}
                    <div className="flex items-start">
                      <Package className="h-4 w-4 text-gray-400 mr-2 mt-1 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <h3 className="text-sm font-medium text-gray-900 truncate">{item.productName}</h3>
                        <div className="text-xs text-gray-500 mt-1">
                          <div>{item.skuCode} • {item.category}</div>
                        </div>
                      </div>
                    </div>

                    {/* Quantity Info */}
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="text-xs text-gray-500">Quantity</div>
                        <div className="text-sm font-medium text-gray-900">
                          {item.quantity} {item.unitType === 'box' ? 'boxes' : 'kg'}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-gray-500">Total Weight</div>
                        <div className="text-sm font-medium text-gray-900">{item.totalWeight} kg</div>
                      </div>
                    </div>

                    {/* Pricing Inputs */}
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">Unit Price (₹)</label>
                        <input
                          type="number"
                          value={item.unitPrice}
                          onChange={(e) => handleItemChange(item.id, 'unitPrice', Number(e.target.value))}
                          min="0"
                          step="0.01"
                          className="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          placeholder="Enter unit price"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">Total (₹)</label>
                        <div className="text-sm font-bold text-gray-900 py-1">₹{item.total.toFixed(2)}</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {items.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <div className="text-sm">No items added yet. Select a vehicle arrival to populate items automatically.</div>
              </div>
            )}
          </div>

          {/* Additional Costs */}
          <div className="border-t pt-4 sm:pt-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4 space-y-2 sm:space-y-0">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Additional Costs</h2>
              <button
                type="button"
                onClick={handleAddCost}
                className="bg-green-600 text-white rounded-md px-3 py-2 sm:px-4 sm:py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center justify-center"
              >
                <Plus className="h-4 w-4 mr-1" />
                <span className="hidden xs:inline">Add Cost</span>
                <span className="xs:hidden">Add</span>
              </button>
            </div>

            {additionalCosts.length > 0 && (
              <>
                {/* Desktop Table */}
                <div className="hidden lg:block overflow-x-visible">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Cost Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Calculated
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {additionalCosts.map((cost) => (
                        <tr key={cost.id}>
                          <td className="px-6 py-4 max-w-[300px]">
                            <input
                              type="text"
                              value={cost.name}
                              onChange={(e) => handleCostChange(cost.id, 'name', e.target.value)}
                              className="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                              placeholder="Cost name"
                            />
                          </td>
                          <td className="px-6 py-4 max-w-[300px]">
                            <Dropdown
                              options={[
                                { value: 'fixed', label: 'Fixed Amount' },
                                { value: 'percentage', label: 'Percentage' },
                                { value: 'per_box', label: 'Per Box' }
                              ]}
                              value={cost.type}
                              onChange={(value) => handleCostChange(cost.id, 'type', value as 'fixed' | 'percentage' | 'per_box')}
                              placeholder="Select type..."
                              className="w-full"
                            />
                          </td>
                          <td className="px-6 py-4 max-w-[300px]">
                            <div className="relative">
                              <input
                                type="number"
                                value={cost.amount}
                                onChange={(e) => handleCostChange(cost.id, 'amount', Number(e.target.value))}
                                min="0"
                                step="0.01"
                                className="block w-24 border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                              />
                            </div>
                          </td>
                          <td className="px-6 py-4 max-w-[300px] text-sm text-gray-900">
                            ₹{cost.calculatedAmount.toFixed(2)}
                          </td>
                          <td className="px-6 py-4 max-w-[300px]">
                            <button
                              type="button"
                              onClick={() => handleRemoveCost(cost.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Mobile/Tablet Cards */}
                <div className="lg:hidden space-y-4">
                  {additionalCosts.map((cost) => (
                    <div key={cost.id} className="bg-gray-50 rounded-lg p-4 border">
                      <div className="space-y-3">
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">Cost Name</label>
                          <input
                            type="text"
                            value={cost.name}
                            onChange={(e) => handleCostChange(cost.id, 'name', e.target.value)}
                            className="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                            placeholder="Cost name"
                          />
                        </div>

                        <div>
                          <label className="block text-xs text-gray-500 mb-1">Type</label>
                          <Dropdown
                            options={[
                              { value: 'fixed', label: 'Fixed Amount' },
                              { value: 'percentage', label: 'Percentage' },
                              { value: 'per_box', label: 'Per Box' }
                            ]}
                            value={cost.type}
                            onChange={(value) => handleCostChange(cost.id, 'type', value as 'fixed' | 'percentage' | 'per_box')}
                            placeholder="Select type..."
                            className="w-full"
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="block text-xs text-gray-500 mb-1">Amount</label>
                            <input
                              type="number"
                              value={cost.amount}
                              onChange={(e) => handleCostChange(cost.id, 'amount', Number(e.target.value))}
                              min="0"
                              step="0.01"
                              className="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                            />
                          </div>
                          <div>
                            <label className="block text-xs text-gray-500 mb-1">Calculated</label>
                            <div className="text-sm font-bold text-gray-900 py-1">₹{cost.calculatedAmount.toFixed(2)}</div>
                          </div>
                        </div>

                        <div className="flex justify-end">
                          <button
                            type="button"
                            onClick={() => handleRemoveCost(cost.id)}
                            className="text-red-600 hover:text-red-900 p-1"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}

            {additionalCosts.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <div className="text-sm">No additional costs added yet. Click "Add Cost" to include expenses like transportation, commission, etc.</div>
              </div>
            )}
          </div>

          {/* Notes */}
          <div className="border-t pt-4 sm:pt-6">
            <div>
              <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-2">
                Additional Notes (Optional)
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                placeholder="Add any additional notes about this purchase record..."
              />
            </div>
          </div>

          {/* Summary */}
          <div className="border-t pt-4 sm:pt-6">
            <div className="bg-gray-50 rounded-lg p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Purchase Summary</h3>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Items Subtotal:</span>
                  <span className="text-gray-900 font-medium">₹{calculateItemsSubtotal().toFixed(2)}</span>
                </div>
                
                {additionalCosts.length > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Less: Additional Costs:</span>
                    <span className="text-red-600 font-medium">-₹{calculateAdditionalCostsTotal().toFixed(2)}</span>
                  </div>
                )}
                
                <div className="border-t pt-3 flex justify-between text-base sm:text-lg font-bold">
                  <span className="text-gray-900">Final Total Amount:</span>
                  <span className="text-green-600">₹{calculateTotal().toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="border-t pt-4 sm:pt-6">
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isSubmitting || items.length === 0}
                className="bg-green-600 text-white rounded-md px-4 py-2 sm:px-6 sm:py-3 text-sm font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200"
              >
                {isSubmitting ? 'Creating...' : 'Create Purchase Record'}
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Creation Modal */}
      <PurchaseRecordCreationModal
        isOpen={showCreationModal}
        onClose={handleModalClose}
        onConfirm={handleModalConfirm}
        isSubmitting={isSubmitting}
      />
    </div>
  );
};

export default NewRecordPurchase;
