import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Package2, ArrowLeft, Pencil, Settings, FileText } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { purchaseRecordService } from '../../services/api/purchaseRecordService';
import { organizationService, Organization } from '../../services/api/organizationService';
import { formatQuantityWithUnit, formatWeight } from '../../utils/weightUtils';
import PurchaseRecordClosureModal from '../../components/modals/PurchaseRecordClosureModal';
import '../../styles/print.css';

interface PurchaseRecordData {
  id: string;
  record_number: string;
  supplier: string;
  record_date: string;
  arrival_timestamp: string;
  pricing_model: string;
  default_commission: number | null;
  payment_terms: number | null;
  items_subtotal: number;
  additional_costs_total: number;
  total_amount: number;
  status: string;
  notes: string | null;
  purchase_record_items?: Array<{
    id: string;
    product_name: string;
    sku_code: string;
    category: string;
    quantity: number;
    unit_type: string;
    total_weight: number;
    market_price: number | null;
    commission: number | null;
    unit_price: number;
    total: number;
  }>;
  items?: Array<{
    id: string;
    product_name: string;
    sku_code: string;
    category: string;
    quantity: number;
    unit_type: string;
    total_weight: number;
    market_price: number | null;
    commission: number | null;
    unit_price: number;
    total: number;
  }>;
  purchase_record_costs?: Array<{
    id: string;
    name: string;
    amount: number;
    type: string;
    calculated_amount: number;
  }>;
  costs?: Array<{
    id: string;
    name: string;
    amount: number;
    type: string;
    calculated_amount: number;
  }>;
}

const ViewRecordPurchase: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [orderData, setOrderData] = useState<PurchaseRecordData | null>(null);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [closureModal, setClosureModal] = useState<{
    isOpen: boolean;
    recordId: string;
    currentStatus: string;
    recordNumber: string;
  }>({
    isOpen: false,
    recordId: '',
    currentStatus: '',
    recordNumber: ''
  });

  useEffect(() => {
    if (id) {
      loadOrderData();
      loadOrganizationData();
    }
  }, [id]);

  const loadOrderData = async () => {
    if (!id) return;
    
    try {
      const data = await purchaseRecordService.getById(id);
      setOrderData(data);
    } catch (error) {
      console.error('Error loading purchase record:', error);
      // Global error handler will show the backend error message
    } finally {
      setLoading(false);
    }
  };

  const loadOrganizationData = async () => {
    try {
      const orgData = await organizationService.getCurrentOrganization();
      setOrganization(orgData);
    } catch (error) {
      console.error('Error loading organization data:', error);
      // Don't show error toast for organization, as it's not critical for viewing
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'partial_closure':
        return 'Partial Closure';
      case 'full_closure':
        return 'Full Closure';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'partial_closure':
        return 'bg-yellow-100 text-yellow-800';
      case 'full_closure':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleOpenClosureModal = () => {
    if (orderData) {
      setClosureModal({
        isOpen: true,
        recordId: orderData.id,
        currentStatus: orderData.status,
        recordNumber: orderData.record_number
      });
    }
  };

  const handleCloseClosureModal = () => {
    setClosureModal({
      isOpen: false,
      recordId: '',
      currentStatus: '',
      recordNumber: ''
    });
  };

  const handleStatusUpdated = () => {
    loadOrderData();
  };

  const getCostTypeDisplay = (type: string) => {
    switch (type) {
      case 'fixed':
        return 'Fixed (₹)';
      case 'percentage':
        return 'Percentage (%)';
      case 'per_box':
        return 'Per Box (₹/box)';
      default:
        return type;
    }
  };

  // Helper functions to safely access items and costs
  const getItems = (data: PurchaseRecordData) => {
    return data.purchase_record_items || data.items || [];
  };

  const getCosts = (data: PurchaseRecordData) => {
    return data.purchase_record_costs || data.costs || [];
  };

  // Helper function to safely format numbers
  const safeToFixed = (value: number | string | null | undefined, decimals: number = 2): string => {
    const num = Number(value) || 0;
    return num.toFixed(decimals);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const isMobileDevice = () => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
           window.innerWidth <= 768;
  };

  const generatePurchaseRecordHTML = () => {
    if (!orderData) return '';
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Purchase Record - ${orderData.record_number}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 20px;
            font-size: 14px;
            line-height: 1.4;
          }
          .record-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
          }
          .company-name {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 5px;
          }
          .company-address {
            font-size: 14px;
            margin-bottom: 10px;
          }
          .record-title {
            font-size: 24px;
            font-weight: bold;
            margin: 15px 0;
          }
          .record-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
          }
          .record-number {
            font-size: 18px;
            font-weight: bold;
            color: #d32f2f;
          }
          .record-date {
            font-size: 14px;
          }
          .supplier-section {
            margin-bottom: 20px;
            font-size: 16px;
          }
          .section-title {
            font-weight: bold;
            margin-bottom: 5px;
            text-decoration: underline;
            font-size: 16px;
          }
          .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          .items-table th,
          .items-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
          }
          .items-table th {
            background-color: #f0f0f0;
            font-weight: bold;
          }
          .items-table .description {
            text-align: left;
          }
          .costs-section {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
          }
          .costs-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 16px;
          }
          .cost-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 5px;
            background-color: white;
            border: 1px solid #ddd;
          }
          .total-section {
            text-align: right;
            margin-top: 20px;
          }
          .total-row {
            font-size: 16px;
            font-weight: bold;
            margin-top: 10px;
          }
          .notes-section {
            margin-top: 30px;
            padding: 15px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
          }
          .notes-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 16px;
          }
          .signature-section {
            margin-top: 50px;
            text-align: right;
          }
          @media print {
            body { margin: 0; }
          }
        </style>
      </head>
      <body>
        <div class="record-header">
          <div class="company-name">${organization?.name || 'Your Company Name'}</div>
          <div class="company-address">
            ${organization?.address || ''}<br>
            ${organization?.phone ? `Phone: ${organization.phone}` : ''}<br>
            ${organization?.email ? `Email: ${organization.email}` : ''}
          </div>
          <div class="record-title">PURCHASE RECORD</div>
        </div>

        <div class="record-details">
          <div>
            <div class="record-number">No. ${orderData.record_number}</div>
          </div>
          <div>
            <div class="record-date">Date: ${formatDate(orderData.record_date)}</div>
            <div class="record-date">Arrival: ${formatDate(orderData.arrival_timestamp)}</div>
          </div>
        </div>

        <div class="supplier-section">
          <div class="section-title">Supplier Details:</div>
          <div><strong>Name:</strong> ${orderData.supplier}</div>
          <div><strong>Payment Terms:</strong> ${orderData.payment_terms} days</div>
          <div><strong>Pricing Model:</strong> ${orderData.pricing_model === 'commission' ? 'Commission Sale' : 'Fixed Price'}</div>
          ${orderData.pricing_model === 'commission' && orderData.default_commission ? 
            `<div><strong>Default Commission:</strong> ${orderData.default_commission}%</div>` : ''}
        </div>

        <table class="items-table">
          <thead>
            <tr>
              <th style="width: 30%">Product</th>
              <th style="width: 15%">Quantity</th>
              <th style="width: 15%">Weight</th>
              ${orderData.pricing_model === 'commission' ? `
                <th style="width: 10%">Market Price</th>
                <th style="width: 10%">Commission</th>
                <th style="width: 10%">Unit Price</th>
              ` : `
                <th style="width: 20%">Unit Price</th>
              `}
              <th style="width: 20%">Total</th>
            </tr>
          </thead>
          <tbody>
            ${getItems(orderData).map(item => `
              <tr>
                <td class="description">
                  <strong>${item.product_name} - ${item.sku_code}</strong>
                </td>
                <td>${formatQuantityWithUnit(item.quantity, item.unit_type)}</td>
                <td>${item.unit_type === 'loose' || item.unit_type === 'kg' 
                  ? formatWeight(item.total_weight) 
                  : item.total_weight}</td>
                ${orderData.pricing_model === 'commission' ? `
                  <td>₹${safeToFixed(item.market_price || 0)}</td>
                  <td>${safeToFixed(item.commission || 0, 1)}%</td>
                  <td>₹${safeToFixed(item.unit_price)}</td>
                ` : `
                  <td>₹${safeToFixed(item.unit_price)}</td>
                `}
                <td>₹${safeToFixed(item.total)}</td>
              </tr>
            `).join('')}
          </tbody>
          <tfoot>
            <tr style="background-color: #f0f0f0;">
              <td colspan="${orderData.pricing_model === 'commission' ? 6 : 4}" style="text-align: right; font-weight: bold;">
                Items Subtotal:
              </td>
              <td style="font-weight: bold;">₹${safeToFixed(orderData.items_subtotal)}</td>
            </tr>
          </tfoot>
        </table>

        ${getCosts(orderData).length > 0 ? `
          <div class="costs-section">
            <div class="costs-title">Additional Costs:</div>
            ${getCosts(orderData).map(cost => `
              <div class="cost-item">
                <span>${cost.name} (${cost.amount} ${getCostTypeDisplay(cost.type).split(' ')[1]})</span>
                <span>₹${safeToFixed(cost.calculated_amount)}</span>
              </div>
            `).join('')}
            <div style="border-top: 1px solid #000; margin-top: 10px; padding-top: 10px; font-weight: bold;">
              <div class="cost-item" style="background-color: #e0e0e0;">
                <span>Total Additional Costs:</span>
                <span>₹${safeToFixed(orderData.additional_costs_total)}</span>
              </div>
            </div>
          </div>
        ` : ''}

        <div class="total-section">
          <div style="margin-bottom: 8px;">
            <span>Items Subtotal: ₹${safeToFixed(orderData.items_subtotal)}</span>
          </div>
          ${getCosts(orderData).length > 0 ? `
            <div style="margin-bottom: 8px;">
              <span>Less: Additional Costs: -₹${safeToFixed(orderData.additional_costs_total)}</span>
            </div>
          ` : ''}
          <div class="total-row">
            <strong>Final Total Amount: ₹${safeToFixed(orderData.total_amount)}</strong>
          </div>
        </div>

        ${orderData.notes ? `
          <div class="notes-section">
            <div class="notes-title">Additional Notes:</div>
            <div>${orderData.notes.replace(/\n/g, '<br>')}</div>
          </div>
        ` : ''}

        <div class="signature-section">
          <div style="margin-top: 60px; border-top: 1px solid #000; width: 200px; margin-left: auto;">
            <div style="text-align: center; margin-top: 5px;">Authorized Signature</div>
          </div>
        </div>
      </body>
      </html>
    `;
  };

  const downloadPDF = async () => {
    try {
      const recordHTML = generatePurchaseRecordHTML();
      
      // Create a temporary iframe for PDF generation
      const iframe = document.createElement('iframe');
      iframe.style.position = 'absolute';
      iframe.style.left = '-9999px';
      iframe.style.width = '210mm';
      iframe.style.height = '297mm';
      document.body.appendChild(iframe);
      
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) return;
      
      iframeDoc.open();
      iframeDoc.write(recordHTML);
      iframeDoc.close();
      
      // Wait for content to load
      setTimeout(() => {
        if (iframe.contentWindow) {
          iframe.contentWindow.print();
        }
        // Clean up
        setTimeout(() => {
          document.body.removeChild(iframe);
        }, 1000);
      }, 500);
      
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Failed to generate PDF');
    }
  };

  const handleDirectPrint = () => {
    const recordHTML = generatePurchaseRecordHTML();
    
    // Create a temporary iframe for direct printing
    const iframe = document.createElement('iframe');
    iframe.style.position = 'absolute';
    iframe.style.left = '-9999px';
    iframe.style.width = '210mm';
    iframe.style.height = '297mm';
    document.body.appendChild(iframe);
    
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc) return;
    
    iframeDoc.open();
    iframeDoc.write(recordHTML);
    iframeDoc.close();
    
    // Wait for content to load then print
    setTimeout(() => {
      if (iframe.contentWindow) {
        iframe.contentWindow.focus();
        iframe.contentWindow.print();
      }
      // Clean up after printing
      setTimeout(() => {
        document.body.removeChild(iframe);
      }, 1000);
    }, 500);
  };

  const handlePrintRecord = () => {
    if (!orderData || !organization) return;

    if (isMobileDevice()) {
      // On mobile, trigger PDF download
      downloadPDF();
    } else {
      // On desktop/laptop, directly show print dialog
      handleDirectPrint();
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading purchase record...</div>
      </div>
    );
  }

  if (!orderData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Purchase record not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-3 sm:space-x-4">
          <button
            onClick={() => navigate('/record-purchase')}
            className="text-gray-600 hover:text-gray-900 p-1"
          >
            <ArrowLeft className="h-5 w-5 sm:h-6 sm:w-6" />
          </button>
          <div className="flex items-center">
            <Package2 className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mr-2" />
            <h1 className="text-lg sm:text-2xl font-bold text-gray-800">View Purchase Record</h1>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 no-print">
          {orderData.status === 'partial_closure' && (
            <button
              onClick={() => navigate(`/record-purchase/edit/${id}`)}
              className="bg-green-600 text-white rounded-md px-3 py-2 sm:px-4 sm:py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center justify-center"
            >
              <Pencil className="h-4 w-4 mr-1" />
              <span className="hidden xs:inline">Edit Record</span>
              <span className="xs:hidden">Edit</span>
            </button>
          )}
          <button
            onClick={handlePrintRecord}
            className="bg-gray-600 text-white rounded-md px-3 py-2 sm:px-4 sm:py-2 text-sm font-medium hover:bg-gray-700 transition-colors duration-200 flex items-center justify-center"
          >
            <FileText className="h-4 w-4 mr-1" />
            <span className="hidden xs:inline">Print Record</span>
            <span className="xs:hidden">Print</span>
          </button>
          {orderData.status !== 'cancelled' && orderData.status !== 'full_closure' && (
            <button
              onClick={handleOpenClosureModal}
              className="bg-blue-600 text-white rounded-md px-3 py-2 sm:px-4 sm:py-2 text-sm font-medium hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center"
            >
              <Settings className="h-4 w-4 mr-1" />
              <span className="hidden xs:inline">Manage Closure</span>
              <span className="xs:hidden">Manage</span>
            </button>
          )}
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg">
        <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
          {/* Basic Details */}
          <div>
            <h2 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Record Details</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Record Number</label>
                <p className="mt-1 text-sm sm:text-base text-gray-900 break-all">{orderData.record_number}</p>
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Supplier</label>
                <p className="mt-1 text-sm sm:text-base text-gray-900">{orderData.supplier}</p>
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Record Date</label>
                <p className="mt-1 text-sm sm:text-base text-gray-900">{formatDateTime(orderData.record_date)}</p>
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Arrival Timestamp</label>
                <p className="mt-1 text-sm sm:text-base text-gray-900">{formatDateTime(orderData.arrival_timestamp)}</p>
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Payment Terms</label>
                <p className="mt-1 text-sm sm:text-base text-gray-900">{orderData.payment_terms} days</p>
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Pricing Model</label>
                <p className="mt-1 text-sm sm:text-base text-gray-900">
                  {orderData.pricing_model === 'commission' ? 'Commission Sale' : 'Fixed Price'}
                </p>
              </div>
              {orderData.pricing_model === 'commission' && orderData.default_commission && (
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-500">Default Commission</label>
                  <p className="mt-1 text-sm sm:text-base text-gray-900">{orderData.default_commission}%</p>
                </div>
              )}
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Status</label>
                <span className={`mt-1 inline-flex px-2 py-1 text-xs leading-4 font-semibold rounded-full ${getStatusColor(orderData.status)}`}>
                  {getStatusDisplay(orderData.status)}
                </span>
              </div>
            </div>
          </div>

          {/* Items */}
          <div className="border-t pt-4 sm:pt-6">
            <h2 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Items</h2>
            
            {/* Desktop Table */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product Details
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity & Weight
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Unit Price (₹)
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total (₹)
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {getItems(orderData).map((item, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 table-col-lg">
                        <div>
                          <div className="text-sm font-medium text-gray-900 break-words">{item.product_name} - {item.sku_code}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 table-col-md">
                        <div>
                          {
                            item.unit_type === 'loose' || item.unit_type === 'kg' ? (
                              <div className="text-sm text-gray-900">{formatWeight(item.total_weight)}</div>
                            ) : (
                              <div className="text-sm text-gray-900">{formatQuantityWithUnit(item.quantity, item.unit_type)}</div>
                            )
                          }
                        </div>
                      </td>
                      <td className="px-6 py-4 table-col-sm text-sm text-gray-900">
                        ₹{safeToFixed(item.unit_price)}
                      </td>
                      <td className="px-6 py-4 table-col-sm text-sm font-medium text-gray-900">
                        ₹{safeToFixed(item.total)}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot className="bg-gray-50">
                  <tr>
                    <td colSpan={3} className="px-6 py-3 text-sm font-medium text-gray-900 text-right">
                      Items Subtotal:
                    </td>
                    <td className="px-6 py-3 text-sm font-medium text-gray-900">
                      ₹{safeToFixed(orderData.items_subtotal)}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>

            {/* Mobile/Tablet Cards */}
            <div className="lg:hidden space-y-4">
              {getItems(orderData).map((item, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-4 border">
                  <div className="space-y-3">
                    {/* Product Info */}
                    <div>
                      <h3 className="text-sm sm:text-base font-medium text-gray-900">{item.product_name} - {item.sku_code}</h3>
                    </div>

                    {/* Quantity & Weight */}
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="text-xs text-gray-500">Quantity</div>
                        <div className="text-sm font-medium text-gray-900">
                          {formatQuantityWithUnit(item.quantity, item.unit_type)}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-gray-500">Weight</div>
                        <div className="text-sm font-medium text-gray-900">
                          {item.unit_type === 'loose' || item.unit_type === 'kg' 
                            ? formatWeight(item.total_weight) 
                            : item.total_weight}
                        </div>
                      </div>
                    </div>

                    {/* Pricing Details */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="text-xs text-gray-500">Unit Price</div>
                        <div className="text-sm font-medium text-gray-900">₹{safeToFixed(item.unit_price)}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-gray-500">Total</div>
                        <div className="text-sm font-bold text-gray-900">₹{safeToFixed(item.total)}</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {/* Items Subtotal */}
              <div className="bg-gray-100 rounded-lg p-4 border-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Items Subtotal:</span>
                  <span className="text-base font-bold text-gray-900">₹{safeToFixed(orderData.items_subtotal)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Costs */}
          {getCosts(orderData).length > 0 && (
            <div className="border-t pt-4 sm:pt-6">
              <h2 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Additional Costs</h2>
              <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                <div className="space-y-3">
                  {getCosts(orderData).map((cost, index) => (
                    <div key={index} className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 bg-white rounded-md border space-y-2 sm:space-y-0">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-1 sm:space-y-0">
                        <span className="text-sm font-medium text-gray-700 sm:min-w-[120px]">{cost.name}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs sm:text-sm text-gray-600">
                            {cost.amount} {getCostTypeDisplay(cost.type).split(' ')[1]}
                          </span>
                          <span className="text-xs text-gray-500">
                            ({getCostTypeDisplay(cost.type)})
                          </span>
                        </div>
                      </div>
                      <span className="text-sm font-medium text-gray-900 self-end sm:self-auto">
                        ₹{safeToFixed(cost.calculated_amount)}
                      </span>
                    </div>
                  ))}
                  
                  <div className="border-t pt-3 mt-3">
                    <div className="flex justify-between text-sm font-medium">
                      <span className="text-gray-700">Total Additional Costs:</span>
                      <span className="text-gray-900">₹{safeToFixed(orderData.additional_costs_total)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          {orderData.notes && (
            <div className="border-t pt-4 sm:pt-6">
              <h2 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Additional Notes</h2>
              <div className="bg-gray-50 rounded-md p-3 sm:p-4">
                <p className="text-sm text-gray-600 whitespace-pre-wrap break-words">{orderData.notes}</p>
              </div>
            </div>
          )}

          {/* Total Summary */}
          <div className="border-t pt-4 sm:pt-6">
            <div className="bg-gray-50 rounded-lg p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Purchase Summary</h3>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Items Subtotal:</span>
                  <span className="text-gray-900 font-medium">₹{safeToFixed(orderData.items_subtotal)}</span>
                </div>
                
                {getCosts(orderData).length > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Less: Additional Costs:</span>
                    <span className="text-red-600 font-medium">-₹{safeToFixed(orderData.additional_costs_total)}</span>
                  </div>
                )}
                
                <div className="border-t pt-3 flex justify-between text-base sm:text-lg font-bold">
                  <span className="text-gray-900">Final Total Amount:</span>
                  <span className="text-green-600">₹{safeToFixed(orderData.total_amount)}</span>
                </div>
                
                {orderData.pricing_model === 'commission' && (
                  <div className="mt-4 pt-3 border-t">
                    <div className="text-xs text-gray-500">
                      * Commission-based pricing: Market price minus commission percentage
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Closure Modal */}
      <PurchaseRecordClosureModal
        isOpen={closureModal.isOpen}
        onClose={handleCloseClosureModal}
        recordId={closureModal.recordId}
        currentStatus={closureModal.currentStatus}
        recordNumber={closureModal.recordNumber}
        onStatusUpdated={handleStatusUpdated}
      />
    </div>
  );
};

export default ViewRecordPurchase;
