import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  BookOpen, 
  Search, 
  Download, 
  Eye, 
  RefreshCw, 
  AlertCircle,
  DollarSign,
  Receipt,
  CreditCard,
  ChevronDown,
  X,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { usePayments } from '../../hooks/usePayments';
import { Payment } from '../../types/payment.types';
import { salesService } from '../../services/api/salesService';
import { purchaseRecordService } from '../../services/api/purchaseRecordService';
import { getCustomers, getSuppliers } from '../../services/api';
import Dropdown from '../../components/ui/Dropdown';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

interface LedgerEntry {
  id: string;
  date: string;
  description: string;
  type: 'debit' | 'credit';
  amount: number;
  partyName: string;
  partyType: 'customer' | 'supplier' | 'expense';
  partyBalance: number;
  category: string;
  source: 'payment' | 'transaction';
  sourceData?: any;
  mode?: string;
  subtext?: string;
}

interface PartyOption {
  name: string;
  type: 'customer' | 'supplier' | 'expense';
  displayName: string;
}

const Ledger: React.FC = () => {
  const navigate = useNavigate();
  const { payments, loading: paymentsLoading } = usePayments();
  const dropdownRef = useRef<HTMLDivElement>(null);

  const [entries, setEntries] = useState<LedgerEntry[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filter states
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  
  // Party autocomplete states
  const [partySearchTerm, setPartySearchTerm] = useState<string>('');
  const [selectedParty, setSelectedParty] = useState<PartyOption | null>(null);
  const [showPartyDropdown, setShowPartyDropdown] = useState<boolean>(false);
  const [availableParties, setAvailableParties] = useState<PartyOption[]>([]);
  
  const [dateRange, setDateRange] = useState({
    from: new Date().toISOString().split('T')[0],
    to: new Date().toISOString().split('T')[0]
  });
  const [quickDateFilter, setQuickDateFilter] = useState<'today' | 'yesterday' | 'week' | 'month' | 'quarter' | 'year' | 'custom'>('today');

  useEffect(() => {
    loadLedgerData();
  }, [dateRange, quickDateFilter, payments]);

  // Extract unique parties from entries
  useEffect(() => {
    if (entries.length > 0) {
      const partiesMap = new Map<string, PartyOption>();
      
      entries.forEach(entry => {
        const key = `${entry.partyType}-${entry.partyName}`;
        if (!partiesMap.has(key)) {
          partiesMap.set(key, {
            name: entry.partyName,
            type: entry.partyType,
            displayName: `${entry.partyName} (${entry.partyType.charAt(0).toUpperCase() + entry.partyType.slice(1)})`
          });
        }
      });
      
      const parties = Array.from(partiesMap.values()).sort((a, b) => a.displayName.localeCompare(b.displayName));
      setAvailableParties(parties);
    }
  }, [entries]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowPartyDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const getDateFilters = () => {
    const today = new Date();
    const filters: { startDate?: string; endDate?: string } = {};

    switch (quickDateFilter) {
      case 'today':
        filters.startDate = today.toISOString().split('T')[0];
        filters.endDate = today.toISOString().split('T')[0];
        break;
      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        filters.startDate = yesterday.toISOString().split('T')[0];
        filters.endDate = yesterday.toISOString().split('T')[0];
        break;
      case 'week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - 7);
        filters.startDate = weekStart.toISOString().split('T')[0];
        filters.endDate = today.toISOString().split('T')[0];
        break;
      case 'month':
        const monthStart = new Date(today);
        monthStart.setDate(today.getDate() - 30);
        filters.startDate = monthStart.toISOString().split('T')[0];
        filters.endDate = today.toISOString().split('T')[0];
        break;
      case 'quarter':
        const quarterStart = new Date(today);
        quarterStart.setDate(today.getDate() - 90);
        filters.startDate = quarterStart.toISOString().split('T')[0];
        filters.endDate = today.toISOString().split('T')[0];
        break;
      case 'year':
        const yearStart = new Date(today.getFullYear(), 0, 1);
        filters.startDate = yearStart.toISOString().split('T')[0];
        filters.endDate = today.toISOString().split('T')[0];
        break;
      case 'custom':
        if (dateRange.from && dateRange.to) {
          filters.startDate = dateRange.from;
          filters.endDate = dateRange.to;
        }
        break;
    }

    return filters;
  };

  const handleQuickDateFilterChange = (filter: 'today' | 'yesterday' | 'week' | 'month' | 'quarter' | 'year' | 'custom') => {
    setQuickDateFilter(filter);
    if (filter !== 'custom') {
      const today = new Date();
      let from = today.toISOString().split('T')[0];
      let to = today.toISOString().split('T')[0];

      switch (filter) {
        case 'yesterday':
          const yesterday = new Date(today);
          yesterday.setDate(yesterday.getDate() - 1);
          from = yesterday.toISOString().split('T')[0];
          to = yesterday.toISOString().split('T')[0];
          break;
        case 'week':
          const weekStart = new Date(today);
          weekStart.setDate(today.getDate() - 7);
          from = weekStart.toISOString().split('T')[0];
          to = today.toISOString().split('T')[0];
          break;
        case 'month':
          const monthStart = new Date(today);
          monthStart.setDate(today.getDate() - 30);
          from = monthStart.toISOString().split('T')[0];
          to = today.toISOString().split('T')[0];
          break;
        case 'quarter':
          const quarterStart = new Date(today);
          quarterStart.setDate(today.getDate() - 90);
          from = quarterStart.toISOString().split('T')[0];
          to = today.toISOString().split('T')[0];
          break;
        case 'year':
          const yearStart = new Date(today.getFullYear(), 0, 1);
          from = yearStart.toISOString().split('T')[0];
          to = today.toISOString().split('T')[0];
          break;
      }

      setDateRange({ from, to });
    }
  };

  const updateDateRange = (from: string, to: string) => {
    setDateRange({ from, to });
  };

  const loadLedgerData = async () => {
    try {
      setError(null);
      setLoading(true);

      const filters = getDateFilters();
      
      // Fetch ALL historical data (no date filtering for balance calculation)
      const [salesOrders, purchaseRecords, customersData, suppliersData] = await Promise.all([
        salesService.getAll({}),
        purchaseRecordService.getAll({}),
        getCustomers(),
        getSuppliers()
      ]);

      // Store the data for use in calculations
      setCustomers(customersData);
      setSuppliers(suppliersData);

      // STEP 1: Create ALL ledger entries (no date filtering)
      const allLedgerEntries: LedgerEntry[] = [];

      // Add ALL sales transaction entries (no date filtering) - EXCLUDE CANCELLED ORDERS
      salesOrders.forEach((order: any) => {
        // Skip cancelled orders - they should not appear in ledger
        if (order.status === 'cancelled') {
          return;
        }
        
        const customerName = order.customer?.name || order.customer_name || 'Unknown Customer';
        const amount = parseFloat(order.total_amount) || 0;
        
        allLedgerEntries.push({
          id: `SO-${order.id}`,
          date: order.order_date,
          description: `${customerName} - Sale`,
          type: 'debit',
          amount: amount,
          partyName: customerName,
          partyType: 'customer',
          partyBalance: 0, // Will be calculated below
          category: 'Sales',
          source: 'transaction',
          sourceData: order,
          subtext: `${order.order_number || order.id}`
        });
      });

      // Add ALL purchase transaction entries (no date filtering)
      purchaseRecords.forEach((record: any) => {
        const supplierName = record.supplier || 'Unknown Supplier';
        const amount = parseFloat(record.total_amount) || 0;

        allLedgerEntries.push({
          id: `PR-${record.id}`,
          date: record.record_date,
          description: `${supplierName} - Purchase`,
          type: 'credit',
          amount: amount,
          partyName: supplierName,
          partyType: 'supplier',
          partyBalance: 0, // Will be calculated below
          category: 'Purchase',
          source: 'transaction',
          sourceData: record,
          subtext: `PR-${record.record_number || record.id}`
        });
      });

      // Add ALL payment entries (no date filtering)
      if (payments) {
        payments.forEach((payment: Payment) => {
          let partyName: string;
          let partyType: 'customer' | 'supplier' | 'expense';
          let entryType: 'debit' | 'credit';
          let description: string;

          if (payment.type === 'expense') {
            partyName = payment.party_name || 'General Expense';
            partyType = 'expense';
            entryType = 'debit';
            description = `Expense - ${partyName}`;
          } else if (payment.type === 'received') {
            partyName = payment.party_name || 'Unknown Customer';
            partyType = 'customer';
            entryType = 'credit';
            description = `${partyName} - Received`;
          } else {
            partyName = payment.party_name || 'Unknown Supplier';
            partyType = 'supplier';
            entryType = 'debit';
            description = `${partyName} - Made`;
          }

          const amount = parseFloat(payment.amount.toString()) || 0;

          allLedgerEntries.push({
            id: `PAY-${payment.id}`,
            date: payment.payment_date,
            description,
            type: entryType,
            amount: amount,
            partyName,
            partyType,
            partyBalance: 0, // Will be calculated below
            category: payment.type === 'expense' ? 'Expense' : payment.type === 'received' ? 'Payment Received' : 'Payment Made',
            source: 'payment',
            sourceData: payment,
            mode: payment.mode,
            subtext: payment.mode
          });
        });
      }

      // STEP 2: Sort ALL entries by date (oldest first) for balance calculation
      allLedgerEntries.sort((a, b) => {
        const dateA = new Date(a.date).getTime();
        const dateB = new Date(b.date).getTime();
        if (dateA !== dateB) {
          return dateA - dateB;
        }
        // For same date, sort by ID to ensure consistent order
        return a.id.localeCompare(b.id);
      });

      // STEP 3: Calculate running balances using ALL historical data
      const partyBalances: { [key: string]: number } = {};
      
      // Initialize opening balances for customers
      customersData.forEach((customer: any) => {
        const partyKey = `customer-${customer.name}`;
        const openingBalance = customer.opening_balance || 0;
        const openingBalanceType = customer.opening_balance_type || 'debit';
        
        // Convert opening balance to signed value
        // For customers: debit means they owe us (negative), credit means we owe them (positive)
        partyBalances[partyKey] = openingBalanceType === 'debit' ? -openingBalance : openingBalance;
      });
      
      // Initialize opening balances for suppliers
      suppliersData.forEach((supplier: any) => {
        const partyKey = `supplier-${supplier.company_name}`;
        const openingBalance = supplier.opening_balance || 0;
        const openingBalanceType = supplier.opening_balance_type || 'debit';
        
        // Convert opening balance to signed value
        // For suppliers: debit means we owe them (negative), credit means they owe us (positive)
        partyBalances[partyKey] = openingBalanceType === 'debit' ? -openingBalance : openingBalance;
      });
      
      // Calculate running balances for ALL entries
      allLedgerEntries.forEach(entry => {
        const partyKey = `${entry.partyType}-${entry.partyName}`;
        
        if (!partyBalances[partyKey]) {
          partyBalances[partyKey] = 0;
        }
        
        // Update running balance: Previous Balance - Debit + Credit
        if (entry.type === 'debit') {
          partyBalances[partyKey] = partyBalances[partyKey] - entry.amount;
        } else {
          partyBalances[partyKey] = partyBalances[partyKey] + entry.amount;
        }
        
        // Set the running balance for this entry (calculated from ALL history)
        entry.partyBalance = partyBalances[partyKey];
      });

      // STEP 4: Filter entries for display based on date range
      const filteredLedgerEntries = allLedgerEntries.filter(entry => {
        const entryDate = new Date(entry.date);
        const filterStart = filters.startDate ? new Date(filters.startDate) : null;
        const filterEnd = filters.endDate ? new Date(filters.endDate) : null;
        
        // Apply date filtering for display only
        if (filterStart) {
          const entryDateOnly = new Date(entryDate.getFullYear(), entryDate.getMonth(), entryDate.getDate());
          const filterStartOnly = new Date(filterStart.getFullYear(), filterStart.getMonth(), filterStart.getDate());
          if (entryDateOnly < filterStartOnly) return false;
        }
        if (filterEnd) {
          const entryDateOnly = new Date(entryDate.getFullYear(), entryDate.getMonth(), entryDate.getDate());
          const filterEndOnly = new Date(filterEnd.getFullYear(), filterEnd.getMonth(), filterEnd.getDate());
          if (entryDateOnly > filterEndOnly) return false;
        }
        
        return true;
      });

      // STEP 5: Sort filtered entries by date (newest first) for display
      filteredLedgerEntries.sort((a, b) => {
        const dateA = new Date(a.date).getTime();
        const dateB = new Date(b.date).getTime();
        if (dateA !== dateB) {
          return dateB - dateA; // Newest first
        }
        // For same date, sort by ID in reverse to maintain consistency
        return b.id.localeCompare(a.id);
      });

      setEntries(filteredLedgerEntries);
    } catch (error) {
      console.error('Error loading ledger data:', error);
      setError('Failed to load ledger data. Please check your connection and try again.');
      toast.error('Failed to load ledger data');
    } finally {
      setLoading(false);
    }
  };

  // Handle party search input change
  const handlePartySearchChange = (value: string) => {
    setPartySearchTerm(value);
    setShowPartyDropdown(value.length > 0);
  };

  // Handle party selection
  const handlePartySelect = (party: PartyOption) => {
    setSelectedParty(party);
    setPartySearchTerm(party.displayName);
    setShowPartyDropdown(false);
  };

  // Clear party selection
  const clearPartySelection = () => {
    setSelectedParty(null);
    setPartySearchTerm('');
    setShowPartyDropdown(false);
  };

  // Filter parties based on search term
  const filteredPartyOptions = availableParties.filter(party =>
    party.displayName.toLowerCase().includes(partySearchTerm.toLowerCase())
  );

  const filteredEntries = entries.filter(entry => {
    const matchesGeneralSearch = 
      entry.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (entry.mode && entry.mode.toLowerCase().includes(searchTerm.toLowerCase()));
      
    const matchesPartySearch = 
      !selectedParty || 
      (entry.partyName === selectedParty.name && entry.partyType === selectedParty.type);
      
    const matchesCategory = selectedCategory === 'all' || entry.category === selectedCategory;
    
    return matchesGeneralSearch && matchesPartySearch && matchesCategory;
  });

  const categories = Array.from(new Set(entries.map(entry => entry.category)));
  
  // Helper function to filter entries by selected party for calculations
  const getPartyFilteredEntries = (entriesArray: LedgerEntry[]) => {
    if (!selectedParty) {
      return entriesArray; // Return all entries if no party selected
    }
    return entriesArray.filter(entry => 
      entry.partyName === selectedParty.name && entry.partyType === selectedParty.type
    );
  };

  // Calculate party-aware financial metrics using entries for the selected date range
  const partyFilteredEntries = getPartyFilteredEntries(entries);
  
  // NEW: Calculate Total Credit and Total Debit with breakdowns
  const totalCredit = partyFilteredEntries
    .filter(entry => entry.type === 'credit')
    .reduce((sum, entry) => sum + entry.amount, 0);
    
  const totalDebit = partyFilteredEntries
    .filter(entry => entry.type === 'debit')
    .reduce((sum, entry) => sum + entry.amount, 0);
  
  // Calculate Credit breakdowns
  const paymentsReceived = partyFilteredEntries
    .filter(entry => entry.type === 'credit' && entry.source === 'payment')
    .reduce((sum, entry) => sum + entry.amount, 0);
    
  const purchases = partyFilteredEntries
    .filter(entry => entry.type === 'credit' && entry.source === 'transaction' && entry.category === 'Purchase')
    .reduce((sum, entry) => sum + entry.amount, 0);
  
  // Calculate Debit breakdowns
  const sales = partyFilteredEntries
    .filter(entry => entry.type === 'debit' && entry.source === 'transaction' && entry.category === 'Sales')
    .reduce((sum, entry) => sum + entry.amount, 0);
    
  const paymentsMade = partyFilteredEntries
    .filter(entry => entry.type === 'debit' && entry.source === 'payment' && entry.category === 'Payment Made')
    .reduce((sum, entry) => sum + entry.amount, 0);
    
  const expenses = partyFilteredEntries
    .filter(entry => entry.type === 'debit' && entry.source === 'payment' && entry.category === 'Expense')
    .reduce((sum, entry) => sum + entry.amount, 0);
  
  // State for opening balance and running balance
  const [periodOpeningBalance, setPeriodOpeningBalance] = useState<number>(0);
  const [organizationRunningBalance, setOrganizationRunningBalance] = useState<number>(0);
  
  // Calculate period opening balance (running balance from previous period)
  const calculatePeriodOpeningBalance = async () => {
    try {
      // Get the date range for the current period
      const filters = getDateFilters();
      const startDate = filters.startDate ? new Date(filters.startDate) : new Date();
      
      // Calculate previous period end date (day before current period start)
      const previousPeriodEnd = new Date(startDate);
      previousPeriodEnd.setDate(previousPeriodEnd.getDate() - 1);
      
      // Fetch all historical data (no date filtering)
      const [allSalesOrders, allPurchaseRecords] = await Promise.all([
        salesService.getAll({}),
        purchaseRecordService.getAll({})
      ]);
      
      const allHistoricalEntries: LedgerEntry[] = [];
      
      // Add all sales transactions up to previous period end
      allSalesOrders.forEach((order: any) => {
        const orderDate = new Date(order.order_date);
        if (orderDate <= previousPeriodEnd) {
          const amount = parseFloat(order.total_amount) || 0;
          const customerName = order.customer?.name || order.customer_name || 'Unknown Customer';
          allHistoricalEntries.push({
            id: `SO-${order.id}`,
            date: order.order_date,
            description: 'Sale',
            type: 'debit',
            amount: amount,
            partyName: customerName,
            partyType: 'customer',
            partyBalance: 0,
            category: 'Sales',
            source: 'transaction',
            sourceData: order
          });
        }
      });
      
      // Add all purchase transactions up to previous period end
      allPurchaseRecords.forEach((record: any) => {
        const recordDate = new Date(record.record_date);
        if (recordDate <= previousPeriodEnd) {
          const amount = parseFloat(record.total_amount) || 0;
          const supplierName = record.supplier || 'Unknown Supplier';
          allHistoricalEntries.push({
            id: `PR-${record.id}`,
            date: record.record_date,
            description: 'Purchase',
            type: 'credit',
            amount: amount,
            partyName: supplierName,
            partyType: 'supplier',
            partyBalance: 0,
            category: 'Purchase',
            source: 'transaction',
            sourceData: record
          });
        }
      });
      
      // Add all payment transactions up to previous period end
      if (payments) {
        payments.forEach((payment: Payment) => {
          const paymentDate = new Date(payment.payment_date);
          if (paymentDate <= previousPeriodEnd) {
            let entryType: 'debit' | 'credit';
            let partyType: 'customer' | 'supplier' | 'expense';
            let partyName: string;

            if (payment.type === 'expense') {
              entryType = 'debit';
              partyType = 'expense';
              partyName = payment.party_name || 'General Expense';
            } else if (payment.type === 'received') {
              entryType = 'credit';
              partyType = 'customer';
              partyName = payment.party_name || 'Unknown Customer';
            } else {
              entryType = 'debit';
              partyType = 'supplier';
              partyName = payment.party_name || 'Unknown Supplier';
            }

            const amount = parseFloat(payment.amount.toString()) || 0;
            allHistoricalEntries.push({
              id: `PAY-${payment.id}`,
              date: payment.payment_date,
              description: 'Payment',
              type: entryType,
              amount: amount,
              partyName,
              partyType,
              partyBalance: 0,
              category: payment.type === 'expense' ? 'Expense' : payment.type === 'received' ? 'Payment Received' : 'Payment Made',
              source: 'payment',
              sourceData: payment
            });
          }
        });
      }
      
      // Filter historical entries by selected party if any
      const partyFilteredHistoricalEntries = getPartyFilteredEntries(allHistoricalEntries);
      
      // Calculate historical inflows and outflows up to previous period end
      const historicalCredit = partyFilteredHistoricalEntries
        .filter(entry => entry.type === 'credit')
        .reduce((sum, entry) => sum + entry.amount, 0);
        
      const historicalDebit = partyFilteredHistoricalEntries
        .filter(entry => entry.type === 'debit')
        .reduce((sum, entry) => sum + entry.amount, 0);
      
      // Add static opening balances from customers and suppliers (these are before first transaction)
      let staticOpeningBalance = 0;
      
      if (!selectedParty) {
        // If no party selected, include all opening balances
        customers.forEach((customer: any) => {
          const openingBalance = Number(customer.opening_balance) || 0;
          const openingBalanceType = customer.opening_balance_type || 'debit';
          staticOpeningBalance += openingBalanceType === 'debit' ? -openingBalance : openingBalance;
        });
        
        suppliers.forEach((supplier: any) => {
          const openingBalance = Number(supplier.opening_balance) || 0;
          const openingBalanceType = supplier.opening_balance_type || 'debit';
          staticOpeningBalance += openingBalanceType === 'debit' ? -openingBalance : openingBalance;
        });
      } else {
        // If party selected, only include that party's opening balance
        if (selectedParty.type === 'customer') {
          const customer = customers.find(c => c.name === selectedParty.name);
          if (customer) {
            const openingBalance = Number(customer.opening_balance) || 0;
            const openingBalanceType = customer.opening_balance_type || 'debit';
            staticOpeningBalance = openingBalanceType === 'debit' ? -openingBalance : openingBalance;
          }
        } else if (selectedParty.type === 'supplier') {
          const supplier = suppliers.find(s => s.company_name === selectedParty.name);
          if (supplier) {
            const openingBalance = Number(supplier.opening_balance) || 0;
            const openingBalanceType = supplier.opening_balance_type || 'debit';
            staticOpeningBalance = openingBalanceType === 'debit' ? -openingBalance : openingBalance;
          }
        }
      }
      
      // Period opening balance = Static opening balance + Historical running balance up to previous period
      return staticOpeningBalance + historicalCredit - historicalDebit;
      
    } catch (error) {
      console.error('Error calculating period opening balance:', error);
      return 0;
    }
  };
  
  // Calculate organization running balance (ALL TIME transactions)
  const calculateOrganizationRunningBalance = async () => {
    try {
      // Fetch ALL historical data (no date filtering at all)
      const [allSalesOrders, allPurchaseRecords] = await Promise.all([
        salesService.getAll({}),
        purchaseRecordService.getAll({})
      ]);
      
      let totalCredit = 0;
      let totalDebit = 0;
      
      // Add all sales transactions (all time)
      allSalesOrders.forEach((order: any) => {
        const amount = parseFloat(order.total_amount) || 0;
        const customerName = order.customer?.name || order.customer_name || 'Unknown Customer';
        
        // If party is selected, only include transactions for that party
        if (!selectedParty || (selectedParty.type === 'customer' && selectedParty.name === customerName)) {
          totalDebit += amount; // Sales are debits
        }
      });
      
      // Add all purchase transactions (all time)
      allPurchaseRecords.forEach((record: any) => {
        const amount = parseFloat(record.total_amount) || 0;
        const supplierName = record.supplier || 'Unknown Supplier';
        
        // If party is selected, only include transactions for that party
        if (!selectedParty || (selectedParty.type === 'supplier' && selectedParty.name === supplierName)) {
          totalCredit += amount; // Purchases are credits
        }
      });
      
      // Add all payment transactions (all time)
      if (payments) {
        payments.forEach((payment: Payment) => {
          const amount = parseFloat(payment.amount.toString()) || 0;
          let partyName: string;
          let partyType: 'customer' | 'supplier' | 'expense';

          if (payment.type === 'expense') {
            partyName = payment.party_name || 'General Expense';
            partyType = 'expense';
          } else if (payment.type === 'received') {
            partyName = payment.party_name || 'Unknown Customer';
            partyType = 'customer';
          } else {
            partyName = payment.party_name || 'Unknown Supplier';
            partyType = 'supplier';
          }
          
          // If party is selected, only include payments for that party
          if (!selectedParty || (selectedParty.type === partyType && selectedParty.name === partyName)) {
            if (payment.type === 'expense' || payment.type === 'paid') {
              totalDebit += amount; // Expenses and payments made are debits
            } else if (payment.type === 'received') {
              totalCredit += amount; // Payments received are credits
            }
          }
        });
      }
      
      // Add static opening balances from customers and suppliers
      let staticOpeningBalance = 0;
      
      if (!selectedParty) {
        // If no party selected, include all opening balances
        customers.forEach((customer: any) => {
          const openingBalance = Number(customer.opening_balance) || 0;
          const openingBalanceType = customer.opening_balance_type || 'debit';
          staticOpeningBalance += openingBalanceType === 'debit' ? -openingBalance : openingBalance;
        });
        
        suppliers.forEach((supplier: any) => {
          const openingBalance = Number(supplier.opening_balance) || 0;
          const openingBalanceType = supplier.opening_balance_type || 'debit';
          staticOpeningBalance += openingBalanceType === 'debit' ? -openingBalance : openingBalance;
        });
      } else {
        // If party selected, only include that party's opening balance
        if (selectedParty.type === 'customer') {
          const customer = customers.find(c => c.name === selectedParty.name);
          if (customer) {
            const openingBalance = Number(customer.opening_balance) || 0;
            const openingBalanceType = customer.opening_balance_type || 'debit';
            staticOpeningBalance = openingBalanceType === 'debit' ? -openingBalance : openingBalance;
          }
        } else if (selectedParty.type === 'supplier') {
          const supplier = suppliers.find(s => s.company_name === selectedParty.name);
          if (supplier) {
            const openingBalance = Number(supplier.opening_balance) || 0;
            const openingBalanceType = supplier.opening_balance_type || 'debit';
            staticOpeningBalance = openingBalanceType === 'debit' ? -openingBalance : openingBalance;
          }
        }
      }
      
      // Organization running balance = Static opening balance + All time credits - All time debits
      return staticOpeningBalance + totalCredit - totalDebit;
      
    } catch (error) {
      console.error('Error calculating organization running balance:', error);
      return 0;
    }
  };
  
  // Calculate balances on component mount and when data changes
  useEffect(() => {
    const calculateBalances = async () => {
      const [openingBalance, runningBalance] = await Promise.all([
        calculatePeriodOpeningBalance(),
        calculateOrganizationRunningBalance()
      ]);
      
      setPeriodOpeningBalance(openingBalance);
      setOrganizationRunningBalance(runningBalance);
    };
    
    if (customers.length > 0 || suppliers.length > 0 || payments) {
      calculateBalances();
    }
  }, [customers, suppliers, payments, dateRange, quickDateFilter, selectedParty]);

  const handleViewTransaction = (entry: LedgerEntry) => {
    if (entry.source === 'transaction') {
      if (entry.id.startsWith('SO-')) {
        const salesOrderId = entry.id.replace('SO-', '');
        navigate(`/sales/view/${salesOrderId}?from=ledger`);
      } else if (entry.id.startsWith('PR-')) {
        const purchaseRecordId = entry.id.replace('PR-', '');
        navigate(`/record-purchase/view/${purchaseRecordId}`);
      }
    }
  };

  const exportLedgerAsCSV = () => {
    const headers = ['Date', 'Description', 'Category', 'Debit', 'Credit', 'Running Balance'];
    const csvData = filteredEntries.map(entry => {
      // Format description to match UI display (combine main description with subtext)
      let formattedDescription = entry.description;
      if (entry.subtext) {
        formattedDescription = `${entry.description} (${entry.subtext})`;
      }

      return [
        new Date(entry.date).toLocaleDateString(),
        formattedDescription,
        entry.category,
        entry.type === 'debit' ? entry.amount.toString() : '',
        entry.type === 'credit' ? entry.amount.toString() : '',
        entry.partyBalance.toString()
      ];
    });

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ledger-report-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    toast.success('Ledger exported as CSV successfully!');
  };

  const exportLedgerAsPDF = () => {
    const doc = new jsPDF();
    
    // Set consistent font for the entire document
    doc.setFont('helvetica');
    
    // Add title
    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.text('Ledger', 14, 22);
    
    let currentY = 32;
    
    // Add date range with generation info as subtext
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    const filters = getDateFilters();
    const startDate = filters.startDate ? new Date(filters.startDate).toLocaleDateString() : 'N/A';
    const endDate = filters.endDate ? new Date(filters.endDate).toLocaleDateString() : 'N/A';
    const dateRangeText = `Period: ${startDate} - ${endDate}`;
    doc.text(dateRangeText, 14, currentY);
    
    // Add generation info as subtext to Period
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text(`Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, 14, currentY + 8);
    doc.text(`Total Entries: ${filteredEntries.length}`, 14, currentY + 14);
    
    currentY += 24;
    
    // Add party details if party filter is applied
    if (selectedParty) {
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.text('Party Details:', 14, currentY);
      doc.setFont('helvetica', 'normal');
      
      // Get party details from customers or suppliers
      let partyDetails = null;
      if (selectedParty.type === 'customer') {
        partyDetails = customers.find(c => c.name === selectedParty.name);
      } else if (selectedParty.type === 'supplier') {
        partyDetails = suppliers.find(s => s.company_name === selectedParty.name);
      }
      
      currentY += 8;
      doc.text(`Name: ${selectedParty.name}`, 14, currentY);
      
      if (partyDetails) {
        currentY += 6;
        if (selectedParty.type === 'customer') {
          const address = partyDetails.address || 'N/A';
          doc.text(`Address: ${address}`, 14, currentY);
        } else if (selectedParty.type === 'supplier') {
          const address = partyDetails.address || 'N/A';
          doc.text(`Address: ${address}`, 14, currentY);
        }
      } else {
        currentY += 6;
        doc.text('Address: N/A', 14, currentY);
      }
      
      currentY += 12;
    }
    
    // Add summary metrics
    doc.setFontSize(10);
    doc.setFont('helvetica', 'bold');
    doc.text('Overall Summary:', 14, currentY);
    doc.setFont('helvetica', 'normal');
    doc.text(`Opening Balance: ${formatBalance(periodOpeningBalance).replace('₹', 'Rs. ')}`, 14, currentY + 6);
    doc.text(`Total Credit: ${formatCurrency(totalCredit).replace('₹', 'Rs. ')}`, 14, currentY + 12);
    doc.text(`Total Debit: ${formatCurrency(totalDebit).replace('₹', 'Rs. ')}`, 14, currentY + 18);
    doc.text(`Closing Balance: ${formatBalance(organizationRunningBalance).replace('₹', 'Rs. ')}`, 14, currentY + 24);
    
    // Prepare table data with safe text handling
    const tableData = filteredEntries.map(entry => {
      // Format description to match UI display and ensure safe text
      let formattedDescription = entry.description || '';
      if (entry.subtext) {
        formattedDescription = `${formattedDescription} (${entry.subtext})`;
      }
      
      // Ensure all text is safe for PDF generation
      const safeText = (text: string) => {
        return text.replace(/[^\x20-\x7E]/g, '?'); // Replace non-ASCII characters with ?
      };
      
      return [
        new Date(entry.date).toLocaleDateString(),
        safeText(formattedDescription.length > 40 ? formattedDescription.substring(0, 37) + '...' : formattedDescription),
        safeText(entry.category || ''),
        entry.type === 'debit' ? formatCurrency(entry.amount).replace('₹', 'Rs. ') : '-',
        entry.type === 'credit' ? formatCurrency(entry.amount).replace('₹', 'Rs. ') : '-',
        formatBalance(entry.partyBalance).replace('₹', 'Rs. ')
      ];
    });

    // Add table with consistent font settings
    autoTable(doc, {
      head: [['Date', 'Description', 'Category', 'Debit', 'Credit', 'Running Balance']],
      body: tableData,
      startY: currentY + 34,
      styles: {
        font: 'helvetica',
        fontSize: 8,
        cellPadding: 2,
        textColor: [0, 0, 0],
      },
      headStyles: {
        font: 'helvetica',
        fontStyle: 'bold',
        fontSize: 8,
        fillColor: [71, 85, 105], // Slate color
        textColor: [255, 255, 255],
      },
      bodyStyles: {
        font: 'helvetica',
        fontStyle: 'normal',
        fontSize: 8,
      },
      columnStyles: {
        0: { cellWidth: 25, font: 'helvetica' }, // Date
        1: { cellWidth: 60, font: 'helvetica' }, // Description
        2: { cellWidth: 25, font: 'helvetica' }, // Category
        3: { cellWidth: 25, halign: 'right', font: 'helvetica' }, // Debit
        4: { cellWidth: 25, halign: 'right', font: 'helvetica' }, // Credit
        5: { cellWidth: 30, halign: 'right', font: 'helvetica' }, // Running Balance
      },
      alternateRowStyles: {
        fillColor: [248, 250, 252], // Light gray
        font: 'helvetica',
      },
      margin: { top: 10, right: 14, bottom: 10, left: 14 },
    });

    // Save the PDF
    doc.save(`ledger-report-${new Date().toISOString().split('T')[0]}.pdf`);
    toast.success('Ledger exported as PDF successfully!');
  };

  const formatCurrency = (amount: number) => {
    // Handle NaN, null, undefined, or invalid numbers
    if (!amount || isNaN(amount) || !isFinite(amount)) {
      return '₹0';
    }
    
    const absAmount = Math.abs(amount);
    
    // Format with proper Indian number system
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(absAmount);
  };

  const formatBalance = (balance: number) => {
    // Handle NaN, null, undefined, or invalid numbers
    if (!balance || isNaN(balance) || !isFinite(balance)) {
      return '₹0';
    }
    
    const absBalance = Math.abs(balance);
    const sign = balance >= 0 ? '+' : '-';
    const formattedAmount = new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(absBalance);
    
    return `${sign}${formattedAmount}`;
  };

  const getBalanceColor = (balance: number) => {
    if (balance > 0) return 'text-green-600';
    if (balance < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Sales':
      case 'Payment Received':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Purchase':
      case 'Payment Made':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Expense':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get dynamic tile labels based on selected party
  const getTileLabels = () => {
    if (!selectedParty) {
      return {
        credit: 'Total Credit',
        debit: 'Total Debit',
        balance: 'Running Balance'
      };
    }
    
    return {
      credit: `Credit - ${selectedParty.name}`,
      debit: `Debit - ${selectedParty.name}`,
      balance: `Balance - ${selectedParty.name}`
    };
  };

  const tileLabels = getTileLabels();

  if (loading || paymentsLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <BookOpen className="h-6 w-6 text-green-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-800">Party-wise Ledger</h1>
          </div>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading ledger data...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <BookOpen className="h-6 w-6 text-green-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-800">Party-wise Ledger</h1>
          </div>
          <button 
            onClick={loadLedgerData}
            className="bg-green-600 text-white rounded-md px-4 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Retry
          </button>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-sm font-medium text-red-800">{error}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-0">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="flex items-center">
          <BookOpen className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mr-2" />
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800">Ledger</h1>
          <span className="ml-3 bg-gray-100 text-gray-600 text-sm font-medium px-2 py-1 rounded-full">
            {filteredEntries.length} entries
          </span>
          {selectedParty && (
            <span className="ml-2 bg-blue-100 text-blue-800 text-sm font-medium px-2 py-1 rounded-full">
              {selectedParty.displayName}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2 sm:gap-3">
          <div className="relative">
            <div className="flex">
              <button
                onClick={exportLedgerAsCSV}
                className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-50 transition-colors"
              >
                <Download className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">CSV</span>
              </button>
              <button
                onClick={exportLedgerAsPDF}
                className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border-t border-r border-b border-gray-300 rounded-r-lg hover:bg-gray-50 transition-colors border-l-0"
              >
                <Download className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">PDF</span>
              </button>
            </div>
          </div>

          <button 
            onClick={loadLedgerData}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <RefreshCw className="h-4 w-4 sm:mr-2" />
            <span className="hidden sm:inline">Refresh</span>
          </button>
        </div>
      </div>
      
      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500">{tileLabels.credit}</p>
              <p className="text-lg sm:text-2xl font-bold text-green-600 break-all">{formatCurrency(totalCredit)}</p>
              <div className="text-xs text-gray-400 space-y-1">
                <div>Payment Received: {formatCurrency(paymentsReceived)}</div>
                <div>Purchases: {formatCurrency(purchases)}</div>
              </div>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600 flex-shrink-0 ml-2">
              <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500">{tileLabels.debit}</p>
              <p className="text-lg sm:text-2xl font-bold text-red-600 break-all">{formatCurrency(totalDebit)}</p>
              <div className="text-xs text-gray-400 space-y-1">
                <div>Sales: {formatCurrency(sales)}</div>
                <div>Payment Made: {formatCurrency(paymentsMade)}</div>
                <div>Expenses: {formatCurrency(expenses)}</div>
              </div>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-red-100 text-red-600 flex-shrink-0 ml-2">
              <TrendingDown className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500">{tileLabels.balance}</p>
              <p className={`text-lg sm:text-2xl font-bold break-all ${getBalanceColor(organizationRunningBalance)}`}>
                {formatBalance(organizationRunningBalance)}
              </p>
              <div className="text-xs text-gray-400 space-y-1">
                <div>Opening: {formatBalance(periodOpeningBalance)}</div>
                <div>Credit: {formatCurrency(totalCredit)}</div>
                <div>Debit: {formatCurrency(totalDebit)}</div>
              </div>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 flex-shrink-0 ml-2">
              <Receipt className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row lg:items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
              placeholder="Search by description, mode..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="relative flex-1 max-w-md" ref={dropdownRef}>
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
              placeholder="Search by party name..."
              value={partySearchTerm}
              onChange={(e) => handlePartySearchChange(e.target.value)}
              onFocus={() => setShowPartyDropdown(partySearchTerm.length > 0)}
            />
            {selectedParty && (
              <button
                onClick={clearPartySelection}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
              </button>
            )}
            
            {/* Party Dropdown */}
            {showPartyDropdown && filteredPartyOptions.length > 0 && (
              <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                {filteredPartyOptions.map((party, index) => (
                  <button
                    key={`${party.type}-${party.name}`}
                    onClick={() => handlePartySelect(party)}
                    className="w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none text-sm border-b border-gray-100 last:border-b-0"
                  >
                    <div className="font-medium text-gray-900">{party.name}</div>
                    <div className="text-xs text-gray-500 capitalize">{party.type}</div>
                  </button>
                ))}
              </div>
            )}
          </div>
          
          <div className="flex flex-wrap gap-2 items-center">
            <div className="min-w-0">
              <Dropdown
                options={[
                  { value: 'all', label: 'All Categories' },
                  ...categories.map(category => ({ value: category, label: category }))
                ]}
                value={selectedCategory}
                onChange={setSelectedCategory}
                placeholder="Select category"
                className="w-48"
              />
            </div>

            <div className="min-w-0">
              <Dropdown
                options={[
                  { value: 'today', label: 'Today' },
                  { value: 'yesterday', label: 'Yesterday' },
                  { value: 'week', label: 'Last 7 Days' },
                  { value: 'month', label: 'Last 30 Days' },
                  { value: 'quarter', label: 'Last 90 Days' },
                  { value: 'year', label: 'This Year' },
                  { value: 'custom', label: 'Custom Range' }
                ]}
                value={quickDateFilter}
                onChange={(value) => handleQuickDateFilterChange(value as 'today' | 'yesterday' | 'week' | 'month' | 'quarter' | 'year' | 'custom')}
                placeholder="Select date range"
                className="w-48"
              />
            </div>

            {quickDateFilter === 'custom' && (
              <>
                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-gray-700">From:</label>
                  <input
                    type="date"
                    value={dateRange.from}
                    onChange={(e) => updateDateRange(e.target.value, dateRange.to)}
                    className="border border-gray-300 rounded-lg text-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-gray-700">To:</label>
                  <input
                    type="date"
                    value={dateRange.to}
                    onChange={(e) => updateDateRange(dateRange.from, e.target.value)}
                    className="border border-gray-300 rounded-lg text-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      
      {/* Ledger Table */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        {filteredEntries.length > 0 ? (
          <>
            {/* Desktop Table */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Debit
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Credit
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Running Balance
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredEntries.map((entry) => (
                    <tr key={entry.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm text-gray-500">
                        {formatDateTime(entry.date)}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8 flex items-center justify-center rounded-full bg-gray-100">
                            {entry.source === 'payment' ? (
                              entry.type === 'credit' ? (
                                <DollarSign className="h-4 w-4 text-green-600" />
                              ) : (
                                <CreditCard className="h-4 w-4 text-blue-600" />
                              )
                            ) : (
                              <BookOpen className="h-4 w-4 text-gray-600" />
                            )}
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900 truncate" title={entry.description}>
                              {entry.description.length > 50 ? `${entry.description.substring(0, 50)}...` : entry.description}
                            </div>
                            {entry.subtext && (
                              <div className="text-sm text-gray-500 truncate">
                                {entry.subtext}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-red-600 font-medium">
                        {entry.type === 'debit' ? formatCurrency(entry.amount) : '-'}
                      </td>
                      <td className="px-6 py-4 text-sm text-green-600 font-medium">
                        {entry.type === 'credit' ? formatCurrency(entry.amount) : '-'}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium">
                        <span className={getBalanceColor(entry.partyBalance)}>
                          {formatBalance(entry.partyBalance)}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button 
                            onClick={() => handleViewTransaction(entry)}
                            className="text-indigo-600 hover:text-indigo-900 p-1 rounded hover:bg-indigo-50"
                            title="View details"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Card View */}
            <div className="lg:hidden divide-y divide-gray-200">
              {filteredEntries.map((entry) => (
                <div 
                  key={entry.id} 
                  className="p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-gray-100">
                        {entry.source === 'payment' ? (
                          entry.type === 'credit' ? (
                            <DollarSign className="h-5 w-5 text-green-600" />
                          ) : (
                            <CreditCard className="h-5 w-5 text-blue-600" />
                          )
                        ) : (
                          <BookOpen className="h-5 w-5 text-gray-600" />
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <button 
                        onClick={() => handleViewTransaction(entry)}
                        className="text-indigo-600 hover:text-indigo-900 p-1 rounded hover:bg-indigo-50"
                        title="View details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  
                  <div className="ml-4 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className={`text-lg font-semibold ${
                        entry.type === 'debit' ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {entry.type === 'debit' ? '-' : '+'}{formatCurrency(entry.amount)}
                      </div>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full border ${getCategoryColor(entry.category)}`}>
                        {entry.category}
                      </span>
                    </div>
                    
                    <div className="text-sm font-medium text-gray-900">
                      {entry.description}
                    </div>
                    
                    {entry.subtext && (
                      <div className="text-sm text-gray-500">
                        {entry.subtext}
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <div>
                        <span className="font-medium">Date:</span> {formatDateTime(entry.date)}
                      </div>
                      <div className={`font-medium ${getBalanceColor(entry.partyBalance)}`}>
                        Balance: {formatBalance(entry.partyBalance)}
                      </div>
                    </div>
                    
                    <div className="text-xs text-gray-500">
                      <span className="font-medium">Source:</span> {entry.source === 'payment' ? 'Payment' : 'Transaction'}
                      {entry.mode && (
                        <span className="ml-2">
                          <span className="font-medium">Mode:</span> {entry.mode}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="py-12 text-center">
            <BookOpen className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Ledger Entries Found</h3>
            <p className="text-sm text-gray-500 mb-4">
              {entries.length === 0 
                ? "No financial entries found. Ledger entries will appear when you create payments, sales orders, or purchase records."
                : "No entries match your current search and filter criteria."
              }
            </p>
          </div>
        )}
      </div>

      {/* Information Note */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 sm:p-6">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <AlertCircle className="h-5 w-5 text-blue-600" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800 mb-2">
              Party-wise Ledger Information
            </h3>
            <div className="text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li><strong>Credit/Debit Accounting:</strong> This ledger shows financial entries organized by debit and credit transactions</li>
                <li><strong>Total Credit:</strong> Includes payments received and purchases (money coming in)</li>
                <li><strong>Total Debit:</strong> Includes sales, payments made, and expenses (money going out)</li>
                <li><strong>Running Balance:</strong> Shows the cumulative balance for each party after each transaction</li>
                <li><strong>Date Filtering:</strong> Filter entries by date range while maintaining accurate running balances</li>
                <li><strong>Party Selection:</strong> Select a specific party to view their transactions and balances only</li>
                <li><strong>Export Options:</strong> Download ledger data as CSV or PDF for record keeping</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Ledger;
