import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { BookOpen, Search, Download, Eye, FileText, RefreshCw, AlertCircle } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { salesService } from '../../services/api/salesService';
import { purchaseRecordService } from '../../services/api/purchaseRecordService';
import Dropdown from '../../components/ui/Dropdown';

interface TransactionEntry {
  id: string;
  date: string;
  description: string;
  type: 'debit' | 'credit';
  amount: number;
  balance: number;
  reference: string;
  category: string;
}

const Transactions: React.FC = () => {
  const navigate = useNavigate();
  const [entries, setEntries] = useState<TransactionEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [dateRange, setDateRange] = useState({
    startDate: new Date().toISOString().split('T')[0], // Default to today
    endDate: new Date().toISOString().split('T')[0]    // Default to today
  });
  const [quickFilter, setQuickFilter] = useState('today');

  useEffect(() => {
    loadTransactionData();
  }, [dateRange, quickFilter]);

  const getDateFilters = () => {
    const today = new Date();
    const filters: { startDate?: string; endDate?: string } = {};

    switch (quickFilter) {
      case 'today':
        filters.startDate = today.toISOString().split('T')[0];
        filters.endDate = today.toISOString().split('T')[0];
        break;
      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        filters.startDate = yesterday.toISOString().split('T')[0];
        filters.endDate = yesterday.toISOString().split('T')[0];
        break;
      case 'week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - 7);
        filters.startDate = weekStart.toISOString().split('T')[0];
        filters.endDate = today.toISOString().split('T')[0];
        break;
      case 'month':
        const monthStart = new Date(today);
        monthStart.setDate(today.getDate() - 30);
        filters.startDate = monthStart.toISOString().split('T')[0];
        filters.endDate = today.toISOString().split('T')[0];
        break;
      case 'custom':
        if (dateRange.startDate && dateRange.endDate) {
          filters.startDate = dateRange.startDate;
          filters.endDate = dateRange.endDate;
        }
        break;
    }

    return filters;
  };

  const handleQuickFilterChange = (filter: string) => {
    setQuickFilter(filter);
    if (filter !== 'custom') {
      // Update dateRange state to reflect the quick filter selection
      const today = new Date();
      let startDate = today.toISOString().split('T')[0];
      let endDate = today.toISOString().split('T')[0];

      switch (filter) {
        case 'yesterday':
          const yesterday = new Date(today);
          yesterday.setDate(yesterday.getDate() - 1);
          startDate = yesterday.toISOString().split('T')[0];
          endDate = yesterday.toISOString().split('T')[0];
          break;
        case 'week':
          const weekStart = new Date(today);
          weekStart.setDate(today.getDate() - 7);
          startDate = weekStart.toISOString().split('T')[0];
          endDate = today.toISOString().split('T')[0];
          break;
        case 'month':
          const monthStart = new Date(today);
          monthStart.setDate(today.getDate() - 30);
          startDate = monthStart.toISOString().split('T')[0];
          endDate = today.toISOString().split('T')[0];
          break;
      }

      setDateRange({ startDate, endDate });
    }
  };

  const loadTransactionData = async () => {
    try {
      setError(null);
      setLoading(true);

      const filters = getDateFilters();

      // Fetch both sales orders and purchase records with date filters
      const [salesOrders, purchaseRecords] = await Promise.all([
        salesService.getAll(filters),
        purchaseRecordService.getAll(filters)
      ]);

      const transactionEntries: TransactionEntry[] = [];

      // Transform sales orders into credit entries - EXCLUDE CANCELLED ORDERS
      if (salesOrders) {
        salesOrders.forEach((order: any) => {
          // Skip cancelled orders - they should not appear in transactions
          if (order.status === 'cancelled') {
            return;
          }
          
          transactionEntries.push({
            id: `SO-${order.id}`,
            date: order.order_date,
            description: `Sales to ${order.customer?.name || 'Unknown Customer'} - ${order.order_number}`,
            type: 'credit',
            amount: order.total_amount ?? 0,
            balance: 0, // Will be calculated later
            reference: order.order_number,
            category: 'Sales'
          });
        });
      }

      // Transform purchase records into debit entries
      if (purchaseRecords) {
        purchaseRecords.forEach((record: any) => {
          transactionEntries.push({
            id: `PR-${record.id}`,
            date: record.record_date,
            description: `Purchase from ${record.supplier} - ${record.record_number}`,
            type: 'debit',
            amount: record.total_amount ?? 0,
            balance: 0, // Will be calculated later
            reference: record.record_number,
            category: 'Purchase'
          });
        });
      }

      // Sort entries by date (oldest first) to calculate running balance
      transactionEntries.sort((a: TransactionEntry, b: TransactionEntry) => new Date(a.date).getTime() - new Date(b.date).getTime());

      // Calculate running balance
      let runningBalance = 0;
      transactionEntries.forEach((entry: TransactionEntry) => {
        if (entry.type === 'credit') {
          runningBalance += entry.amount;
        } else {
          runningBalance -= entry.amount;
        }
        entry.balance = runningBalance;
      });

      // Sort by date (newest first) for display
      transactionEntries.sort((a: TransactionEntry, b: TransactionEntry) => new Date(b.date).getTime() - new Date(a.date).getTime());

      setEntries(transactionEntries);
    } catch (error) {
      console.error('Error loading transaction data:', error);
      setError('Failed to load transaction data. Please check your connection and try again.');
      toast.error('Failed to load transaction data');
    } finally {
      setLoading(false);
    }
  };

  const filteredEntries = entries.filter(entry => {
    const matchesSearch = 
      entry.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.reference.toLowerCase().includes(searchTerm.toLowerCase());
      
    const matchesCategory = selectedCategory === 'all' || entry.category === selectedCategory;
    const matchesType = selectedType === 'all' || entry.type === selectedType;
    
    // Date filtering is now handled server-side, so we don't need client-side filtering
    // unless using custom date range inputs
    return matchesSearch && matchesCategory && matchesType;
  });

  const categories = Array.from(new Set(entries.map(entry => entry.category)));
  
  const totalCredits = filteredEntries
    .filter(entry => entry.type === 'credit')
    .reduce((sum, entry) => sum + (Number(entry.amount) || 0), 0);
    
  const totalDebits = filteredEntries
    .filter(entry => entry.type === 'debit')
    .reduce((sum, entry) => sum + (Number(entry.amount) || 0), 0);
    
  const netBalance = totalCredits - totalDebits;

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const handleViewTransaction = (entry: TransactionEntry) => {
    if (entry.id.startsWith('SO-')) {
      // Sales Order - extract ID and navigate to sales view page with from parameter
      const salesOrderId = entry.id.replace('SO-', '');
      navigate(`/sales/view/${salesOrderId}?from=transactions`);
    } else if (entry.id.startsWith('PR-')) {
      // Purchase Record - extract ID and navigate to purchase record view page
      const purchaseRecordId = entry.id.replace('PR-', '');
      navigate(`/record-purchase/view/${purchaseRecordId}`);
    } else {
      // Fallback - show error message
      toast.error('Unable to determine transaction type');
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <BookOpen className="h-6 w-6 text-green-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-800">Transactions</h1>
          </div>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading transaction data...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <BookOpen className="h-6 w-6 text-green-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-800">Transactions</h1>
          </div>
          <button 
            onClick={loadTransactionData}
            className="bg-green-600 text-white rounded-md px-4 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Retry
          </button>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-sm font-medium text-red-800">{error}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-0">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="flex items-center">
          <BookOpen className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mr-2" />
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800">Transactions</h1>
        </div>
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
          <button 
            onClick={loadTransactionData}
            className="bg-blue-600 text-white rounded-md px-3 sm:px-4 py-2 text-sm font-medium hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">Refresh</span>
            <span className="sm:hidden">Refresh</span>
          </button>
          <button 
            className="bg-green-600 text-white rounded-md px-3 sm:px-4 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center justify-center"
          >
            <Download className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">Export Transactions</span>
            <span className="sm:hidden">Export</span>
          </button>
        </div>
      </div>
      
      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500">Total Credits (Sales)</p>
              <p className="text-lg sm:text-2xl font-bold text-green-600 break-all">₹{(totalCredits ?? 0).toLocaleString()}</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600 flex-shrink-0 ml-2">
              <BookOpen className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500">Total Debits (Purchases)</p>
              <p className="text-lg sm:text-2xl font-bold text-red-600 break-all">₹{(totalDebits ?? 0).toLocaleString()}</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-red-100 text-red-600 flex-shrink-0 ml-2">
              <BookOpen className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Single Line Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row lg:items-center gap-4">
          {/* Search Bar */}
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
              placeholder="Search transactions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {/* All Filters in Single Row */}
          <div className="flex flex-wrap gap-2 items-center">
            {/* Category Filter */}
            <div className="min-w-0">
              <Dropdown
                options={[
                  { value: 'all', label: 'All Categories' },
                  ...categories.map(category => ({ value: category, label: category }))
                ]}
                value={selectedCategory}
                onChange={setSelectedCategory}
                placeholder="Select category"
                className="w-48"
              />
            </div>
            
            {/* Type Filter */}
            <div className="min-w-0">
              <Dropdown
                options={[
                  { value: 'all', label: 'All Types' },
                  { value: 'debit', label: 'Debit (Purchases)' },
                  { value: 'credit', label: 'Credit (Sales)' }
                ]}
                value={selectedType}
                onChange={setSelectedType}
                placeholder="Select type"
                className="w-48"
              />
            </div>

            {/* Date Range Filter */}
            <div className="min-w-0">
              <Dropdown
                options={[
                  { value: 'today', label: 'Today' },
                  { value: 'yesterday', label: 'Yesterday' },
                  { value: 'week', label: 'Last Week' },
                  { value: 'month', label: 'Last Month' },
                  { value: 'quarter', label: 'Last Quarter' },
                  { value: 'year', label: 'This Year' },
                  { value: 'custom', label: 'Custom Range' }
                ]}
                value={quickFilter}
                onChange={handleQuickFilterChange}
                placeholder="Select date range"
                className="w-48"
              />
            </div>

            {/* Custom Date Range - Only show when custom is selected */}
            {quickFilter === 'custom' && (
              <>
                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-gray-700 max-w-[200px]">From:</label>
                  <input
                    type="date"
                    value={dateRange.startDate}
                    onChange={(e) => {
                      setDateRange(prev => ({ ...prev, startDate: e.target.value }));
                      setQuickFilter('custom');
                    }}
                    className="border border-gray-300 rounded-lg text-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-gray-700 max-w-[200px]">To:</label>
                  <input
                    type="date"
                    value={dateRange.endDate}
                    onChange={(e) => {
                      setDateRange(prev => ({ ...prev, endDate: e.target.value }));
                      setQuickFilter('custom');
                    }}
                    className="border border-gray-300 rounded-lg text-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      
      {/* Ledger Table - Desktop View */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        {/* Desktop Table View */}
        <div className="hidden lg:block">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-4 xl:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider table-col-sm">
                    Date
                  </th>
                  <th scope="col" className="px-4 xl:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider table-col-xl">
                    Description
                  </th>
                  <th scope="col" className="px-4 xl:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider table-col-md">
                    Reference
                  </th>
                  <th scope="col" className="px-4 xl:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider table-col-sm">
                    Category
                  </th>
                  <th scope="col" className="px-4 xl:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider table-col-sm">
                    Debit
                  </th>
                  <th scope="col" className="px-4 xl:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider table-col-sm">
                    Credit
                  </th>
                  <th scope="col" className="px-4 xl:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider table-col-sm">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredEntries.map((entry) => (
                  <tr key={entry.id} className="hover:bg-gray-50">
                    <td className="px-4 xl:px-6 py-4 table-col-sm text-sm text-gray-500">
                      {formatDateTime(entry.date)}
                    </td>
                    <td className="px-4 xl:px-6 py-4 table-col-xl text-sm text-gray-900">
                      <div className="break-words" title={entry.description}>
                        {entry.description}
                      </div>
                    </td>
                    <td className="px-4 xl:px-6 py-4 table-col-md text-sm text-gray-500 break-words">
                      {entry.reference}
                    </td>
                    <td className="px-4 xl:px-6 py-4 table-col-sm">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        entry.category === 'Sales' 
                          ? 'bg-green-100 text-green-800'
                          : entry.category === 'Purchase'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                      }`}>
                        {entry.category}
                      </span>
                    </td>
                    <td className="px-4 xl:px-6 py-4 table-col-sm text-sm text-red-600 font-medium">
                      {entry.type === 'debit' ? `₹${(entry.amount ?? 0).toLocaleString()}` : '-'}
                    </td>
                    <td className="px-4 xl:px-6 py-4 table-col-sm text-sm text-green-600 font-medium">
                      {entry.type === 'credit' ? `₹${(entry.amount ?? 0).toLocaleString()}` : '-'}
                    </td>
                    <td className="px-4 xl:px-6 py-4 table-col-sm text-sm font-medium">
                      <div className="flex space-x-2">
                        <button 
                          onClick={() => handleViewTransaction(entry)}
                          className="text-indigo-600 hover:text-indigo-900 transition-colors duration-200"
                          title="View transaction details"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-900 transition-colors duration-200">
                          <FileText className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Mobile/Tablet Card View */}
        <div className="lg:hidden">
          <div className="space-y-4">
            {filteredEntries.map((entry) => (
              <div key={entry.id} className="bg-white rounded-lg border border-gray-200 p-4 hover:bg-gray-50 shadow-sm">
                {/* Header with category, date and actions */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <span className={`px-3 py-1 text-xs font-semibold rounded-full flex-shrink-0 ${
                      entry.category === 'Sales' 
                        ? 'bg-green-100 text-green-800'
                        : entry.category === 'Purchase'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                    }`}>
                      {entry.category}
                    </span>
                    <span className="text-xs text-gray-500 truncate">
                      {formatDateTime(entry.date)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2 ml-3 flex-shrink-0">
                    <button 
                      onClick={() => handleViewTransaction(entry)}
                      className="text-indigo-600 hover:text-indigo-900 transition-colors duration-200 p-2 rounded-md hover:bg-indigo-50"
                      title="View transaction details"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="text-gray-600 hover:text-gray-900 transition-colors duration-200 p-2 rounded-md hover:bg-gray-50">
                      <FileText className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                
                {/* Description */}
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-900 leading-tight mb-2" title={entry.description}>
                    {entry.description}
                  </h3>
                  <p className="text-xs text-gray-500">
                    Ref: {entry.reference}
                  </p>
                </div>
                
                {/* Amount section */}
                <div className="flex justify-between items-end pt-3 border-t border-gray-100">
                  <div className="flex space-x-8">
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">Debit</span>
                      <div className="text-sm font-medium text-red-600">
                        {entry.type === 'debit' ? `₹${(entry.amount ?? 0).toLocaleString()}` : '-'}
                      </div>
                    </div>
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">Credit</span>
                      <div className="text-sm font-medium text-green-600">
                        {entry.type === 'credit' ? `₹${(entry.amount ?? 0).toLocaleString()}` : '-'}
                      </div>
                    </div>
                  </div>
                  <div className="text-right flex-shrink-0 ml-4">
                    <div className={`text-lg font-bold ${
                      entry.type === 'credit' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {entry.type === 'credit' ? '+' : '-'}₹{(entry.amount ?? 0).toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {filteredEntries.length === 0 && (
          <div className="py-8 sm:py-12 text-center text-gray-500 px-4">
            <BookOpen className="h-8 w-8 sm:h-12 sm:w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No Transaction Entries Found</h3>
            <p className="text-sm text-gray-500 max-w-md mx-auto">
              {entries.length === 0 
                ? "No sales orders or purchase records found. Transaction entries will appear automatically when you create sales orders and purchase records."
                : "No entries match your current search and filter criteria."
              }
            </p>
          </div>
        )}
      </div>

      {/* Information Note */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-3 sm:p-4">
        <div className="flex flex-col sm:flex-row">
          <div className="flex-shrink-0 mb-2 sm:mb-0">
            <BookOpen className="h-5 w-5 text-blue-400" />
          </div>
          <div className="sm:ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Transaction Information
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Transaction entries are automatically generated from sales orders and purchase records</li>
                <li>Sales orders appear as credit entries (money coming in)</li>
                <li>Purchase records appear as debit entries (money going out)</li>
                <li>Use filters to analyze specific time periods or transaction types</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Transactions;
