import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Building2, ArrowLeft, Phone, Mail, MapPin, User, Percent, Plus, Trash2, DollarSign } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { organizationService, Organization, UpdateOrganizationData } from '../../services/api/organizationService';
import Dropdown from '../../components/ui/Dropdown';

interface CostItem {
  id: string;
  name: string;
  amount: number;
  type: 'fixed' | 'percentage' | 'per_box';
}

interface BusinessFormData {
  name: string;
  owner_name: string;
  phone: string;
  address: string;
  default_commission_percentage: number;
  email: string;
  fixed_price_costs: CostItem[];
  commission_costs: CostItem[];
}

const BusinessProfile: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<BusinessFormData>({
    name: '',
    owner_name: '',
    phone: '',
    address: '',
    default_commission_percentage: 0,
    email: '',
    fixed_price_costs: [],
    commission_costs: []
  });

  useEffect(() => {
    loadOrganizationData();
  }, []);

  const loadOrganizationData = async () => {
    try {
      setIsLoading(true);
      const organization = await organizationService.getCurrentOrganization();
      
      // Convert organization cost data to CostItem format
      const convertCosts = (costs: any[]): CostItem[] => {
        if (!costs || !Array.isArray(costs)) return [];
        return costs.map((cost, index) => ({
          id: cost.id || `cost_${index}_${Date.now()}`,
          name: cost.name || '',
          amount: cost.amount || 0,
          type: cost.type || 'fixed'
        }));
      };
      
      setFormData({
        name: organization.name || '',
        owner_name: organization.owner_name || '',
        phone: organization.phone || '',
        address: organization.address || '',
        default_commission_percentage: organization.default_commission_percentage || 0,
        email: organization.email || '',
        fixed_price_costs: convertCosts(organization.fixed_price_costs || []),
        commission_costs: convertCosts(organization.commission_costs || [])
      });
    } catch (error) {
      console.error('Error loading organization data:', error);
      toast.error('Failed to load business profile');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'default_commission_percentage' ? parseFloat(value) || 0 : value
    }));
  };

  const handleAddCost = (section: 'fixed_price' | 'commission') => {
    const newCost: CostItem = {
      id: `cost_${Date.now()}_${Math.random()}`,
      name: '',
      amount: 0,
      type: 'fixed'
    };

    setFormData(prev => ({
      ...prev,
      [section === 'fixed_price' ? 'fixed_price_costs' : 'commission_costs']: [
        ...prev[section === 'fixed_price' ? 'fixed_price_costs' : 'commission_costs'],
        newCost
      ]
    }));
  };

  const handleCostChange = (
    section: 'fixed_price' | 'commission',
    costId: string,
    field: keyof CostItem,
    value: string | number
  ) => {
    const costArrayKey = section === 'fixed_price' ? 'fixed_price_costs' : 'commission_costs';
    
    setFormData(prev => ({
      ...prev,
      [costArrayKey]: prev[costArrayKey].map(cost =>
        cost.id === costId ? { ...cost, [field]: value } : cost
      )
    }));
  };

  const handleRemoveCost = (section: 'fixed_price' | 'commission', costId: string) => {
    const costArrayKey = section === 'fixed_price' ? 'fixed_price_costs' : 'commission_costs';
    
    setFormData(prev => ({
      ...prev,
      [costArrayKey]: prev[costArrayKey].filter(cost => cost.id !== costId)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name.trim()) {
      toast.error('Organization name is required');
      return;
    }

    // Validate mobile number format only if provided (exactly 10 digits)
    if (formData.phone.trim() && !/^\d{10}$/.test(formData.phone.trim())) {
      toast.error('Mobile number must be exactly 10 digits');
      return;
    }

    // Commission percentage validation
    if (formData.default_commission_percentage < 0 || formData.default_commission_percentage > 100) {
      toast.error('Commission percentage must be between 0 and 100');
      return;
    }

    // Email validation only if email is provided
    if (formData.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert CostItem arrays to the format expected by the API
      const convertCostsForAPI = (costs: CostItem[]) => {
        return costs.map(cost => ({
          name: cost.name,
          amount: cost.amount,
          type: cost.type
        }));
      };

      const updateData: UpdateOrganizationData = {
        name: formData.name.trim(),
        owner_name: formData.owner_name.trim(),
        phone: formData.phone.trim(),
        address: formData.address.trim() || undefined,
        default_commission_percentage: formData.default_commission_percentage,
        email: formData.email.trim() || undefined,
        fixed_price_costs: convertCostsForAPI(formData.fixed_price_costs),
        commission_costs: convertCostsForAPI(formData.commission_costs)
      };

      await organizationService.updateCurrentOrganization(updateData);
      toast.success('Business profile updated successfully!');
    } catch (error) {
      console.error('Error updating business profile:', error);
      // Global error handler will show the backend error message
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate(-1)}
            className="text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <ArrowLeft className="h-6 w-6" />
          </button>
          <div className="flex items-center">
            <Building2 className="h-6 w-6 text-green-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-800">Business Profile</h1>
          </div>
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h2>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Organization Name <span className="text-red-500">*</span>
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Building2 className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Owner Name
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    name="owner_name"
                    value={formData.owner_name}
                    onChange={handleChange}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="border-t pt-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h2>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Mobile Number
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700">
                  Address
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 pt-2 flex items-start pointer-events-none">
                    <MapPin className="h-5 w-5 text-gray-400" />
                  </div>
                  <textarea
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    rows={3}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Business Settings */}
          <div className="border-t pt-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Business Settings</h2>

            {/* Fixed Price Section */}
            <div className="mt-8">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-md font-medium text-gray-900 flex items-center">
                  <DollarSign className="h-5 w-5 text-blue-600 mr-2" />
                  Fixed Price Costs
                </h3>
                <button
                  type="button"
                  onClick={() => handleAddCost('fixed_price')}
                  className="bg-blue-600 text-white rounded-md px-3 py-2 text-sm font-medium hover:bg-blue-700 transition-colors duration-200 flex items-center"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Cost
                </button>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Define default additional costs that will be automatically added when "Fixed Price" pricing model is selected in purchase records.
              </p>
              
              {formData.fixed_price_costs.length > 0 ? (
                <div className="space-y-3">
                  {formData.fixed_price_costs.map((cost, index) => (
                    <div key={cost.id} className="bg-gray-50 rounded-lg p-4 border">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Cost Name
                          </label>
                          <input
                            type="text"
                            value={cost.name}
                            onChange={(e) => handleCostChange('fixed_price', cost.id, 'name', e.target.value)}
                            className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                            placeholder="e.g., Transportation"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Type
                          </label>
                          <Dropdown
                            options={[
                              { value: 'fixed', label: 'Fixed Amount' },
                              { value: 'percentage', label: 'Percentage' },
                              { value: 'per_box', label: 'Per Box' }
                            ]}
                            value={cost.type}
                            onChange={(value) => handleCostChange('fixed_price', cost.id, 'type', value as 'fixed' | 'percentage' | 'per_box')}
                            placeholder="Select type..."
                            className="w-full"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Amount {cost.type === 'percentage' ? '(%)' : '(₹)'}
                          </label>
                          <input
                            type="number"
                            value={cost.amount}
                            onChange={(e) => handleCostChange('fixed_price', cost.id, 'amount', Number(e.target.value))}
                            min="0"
                            step="0.01"
                            className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                            placeholder="0.00"
                          />
                        </div>
                        
                        <div className="flex items-end">
                          <button
                            type="button"
                            onClick={() => handleRemoveCost('fixed_price', cost.id)}
                            className="text-red-600 hover:text-red-900 p-2"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <DollarSign className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm">No fixed price costs defined yet.</p>
                  <p className="text-xs">Click "Add Cost" to define default costs for fixed price model.</p>
                </div>
              )}
            </div>

            {/* Commission Section */}
            <div className="mt-8">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-md font-medium text-gray-900 flex items-center">
                  <Percent className="h-5 w-5 text-green-600 mr-2" />
                  Commission Costs
                </h3>
                <button
                  type="button"
                  onClick={() => handleAddCost('commission')}
                  className="bg-green-600 text-white rounded-md px-3 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Cost
                </button>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Define default additional costs that will be automatically added when "Commission Based" pricing model is selected in purchase records.
              </p>
              
              {formData.commission_costs.length > 0 ? (
                <div className="space-y-3">
                  {formData.commission_costs.map((cost, index) => (
                    <div key={cost.id} className="bg-gray-50 rounded-lg p-4 border">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Cost Name
                          </label>
                          <input
                            type="text"
                            value={cost.name}
                            onChange={(e) => handleCostChange('commission', cost.id, 'name', e.target.value)}
                            className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                            placeholder="e.g., Commission"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Type
                          </label>
                          <Dropdown
                            options={[
                              { value: 'fixed', label: 'Fixed Amount' },
                              { value: 'percentage', label: 'Percentage' },
                              { value: 'per_box', label: 'Per Box' }
                            ]}
                            value={cost.type}
                            onChange={(value) => handleCostChange('commission', cost.id, 'type', value as 'fixed' | 'percentage' | 'per_box')}
                            placeholder="Select type..."
                            className="w-full"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Amount {cost.type === 'percentage' ? '(%)' : '(₹)'}
                          </label>
                          <input
                            type="number"
                            value={cost.amount}
                            onChange={(e) => handleCostChange('commission', cost.id, 'amount', Number(e.target.value))}
                            min="0"
                            step="0.01"
                            className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                            placeholder="0.00"
                          />
                        </div>
                        
                        <div className="flex items-end">
                          <button
                            type="button"
                            onClick={() => handleRemoveCost('commission', cost.id)}
                            className="text-red-600 hover:text-red-900 p-2"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <Percent className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm">No commission costs defined yet.</p>
                  <p className="text-xs">Click "Add Cost" to define default costs for commission model.</p>
                </div>
              )}
            </div>
          </div>


          {/* Form Actions */}
          <div className="border-t pt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => navigate(-1)}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Updating Profile...' : 'Update Profile'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BusinessProfile;
