const axios = require('axios');

const headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'x-organization-id': 'default-org-id',
  'Content-Type': 'application/json'
};

async function debugMerge() {
  try {
    // Get current inventory
    const inventoryResponse = await axios.get('http://localhost:9000/api/inventory', { headers });
    const grapeInventory = inventoryResponse.data.find(item => item.product_name === 'grape');
    
    if (!grapeInventory) {
      console.log('Grape not found in inventory');
      return;
    }
    
    console.log('=== Current State ===');
    console.log('Grape inventory:', {
      product_id: grapeInventory.product_id,
      sku_id: grapeInventory.sku_id,
      sku_code: grapeInventory.sku_code,
      quantity: grapeInventory.available_quantity
    });
    
    // Try the merge
    const mergeData = {
      current_product_id: grapeInventory.product_id,
      current_sku_id: grapeInventory.sku_id,
      reason: "SKU code correction",
      new_product_name: "Orange",
      new_sku_code: "apple"
    };
    
    console.log('\n=== Merge Request ===');
    console.log(JSON.stringify(mergeData, null, 2));
    
    try {
      const response = await axios.post('http://localhost:9000/api/inventory/update-sku', mergeData, { headers });
      console.log('\n✅ Success:', response.data);
    } catch (error) {
      console.log('\n❌ Error Response:');
      console.log('Status:', error.response?.status);
      console.log('Data:', JSON.stringify(error.response?.data, null, 2));
      
      // If it's a 500 error, the backend logs should show more details
      if (error.response?.status === 500) {
        console.log('\nThis is a server error. Check the backend logs for details.');
      }
    }
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

debugMerge();
