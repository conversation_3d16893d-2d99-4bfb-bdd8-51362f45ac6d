const mysql = require('mysql2/promise');
require('dotenv').config();

async function verifyMigrations() {
  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'fxd_partner_erp'
    });

    console.log('🔌 Connected to database successfully');
    console.log('🔍 Starting migration verification...\n');

    // 1. Check migration table and executed migrations
    console.log('📊 MIGRATION STATUS CHECK');
    console.log('=' .repeat(50));
    
    const [migrations] = await connection.execute(
      'SELECT version, filename, executed_at FROM migrations ORDER BY executed_at DESC'
    );
    
    console.log(`✅ Total migrations executed: ${migrations.length}`);
    migrations.forEach(migration => {
      console.log(`   - ${migration.version} (${migration.executed_at})`);
    });

    // 2. Verify critical tables exist
    console.log('\n🏗️  TABLE STRUCTURE VERIFICATION');
    console.log('=' .repeat(50));
    
    const criticalTables = [
      'organizations', 'users', 'customers', 'suppliers', 'products', 
      'sales_orders', 'sales_order_items', 'sales_order_payments',
      'purchase_records', 'vehicle_arrivals', 'current_inventory', 'payments'
    ];

    for (const table of criticalTables) {
      try {
        const [result] = await connection.execute(`SHOW TABLES LIKE '${table}'`);
        if (result.length > 0) {
          const [count] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
          console.log(`✅ ${table}: ${count[0].count} records`);
        } else {
          console.log(`❌ ${table}: Table missing!`);
        }
      } catch (error) {
        console.log(`❌ ${table}: Error checking table - ${error.message}`);
      }
    }

    // 3. Verify sales order status standardization
    console.log('\n📋 SALES ORDER STATUS VERIFICATION');
    console.log('=' .repeat(50));
    
    const [statusCounts] = await connection.execute(`
      SELECT status, COUNT(*) as count 
      FROM sales_orders 
      GROUP BY status 
      ORDER BY count DESC
    `);
    
    const validStatuses = ['pending', 'pending_approval', 'confirmed', 'processing', 'dispatch_pending', 'dispatched', 'delivered', 'completed', 'cancelled'];
    let invalidStatusFound = false;
    
    statusCounts.forEach(row => {
      if (validStatuses.includes(row.status)) {
        console.log(`✅ ${row.status}: ${row.count} orders`);
      } else {
        console.log(`❌ ${row.status}: ${row.count} orders (INVALID STATUS)`);
        invalidStatusFound = true;
      }
    });

    if (!invalidStatusFound) {
      console.log('✅ All sales order statuses are valid');
    }

    // 4. Verify payment status standardization
    console.log('\n💰 PAYMENT STATUS VERIFICATION');
    console.log('=' .repeat(50));
    
    const [paymentStatusCounts] = await connection.execute(`
      SELECT payment_status, COUNT(*) as count 
      FROM sales_orders 
      GROUP BY payment_status 
      ORDER BY count DESC
    `);
    
    const validPaymentStatuses = ['pending', 'partial', 'paid', 'overdue'];
    let invalidPaymentStatusFound = false;
    
    paymentStatusCounts.forEach(row => {
      if (validPaymentStatuses.includes(row.payment_status)) {
        console.log(`✅ ${row.payment_status}: ${row.count} orders`);
      } else {
        console.log(`❌ ${row.payment_status}: ${row.count} orders (INVALID PAYMENT STATUS)`);
        invalidPaymentStatusFound = true;
      }
    });

    if (!invalidPaymentStatusFound) {
      console.log('✅ All payment statuses are valid');
    }

    // 5. Verify sales order totals integrity
    console.log('\n🧮 SALES ORDER TOTALS VERIFICATION');
    console.log('=' .repeat(50));
    
    const [totalChecks] = await connection.execute(`
      SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN subtotal IS NULL THEN 1 END) as null_subtotals,
        COUNT(CASE WHEN total_amount IS NULL THEN 1 END) as null_totals,
        COUNT(CASE WHEN total_amount < 0 THEN 1 END) as negative_totals,
        AVG(total_amount) as avg_total
      FROM sales_orders
    `);
    
    const totals = totalChecks[0];
    console.log(`✅ Total sales orders: ${totals.total_orders}`);
    console.log(`${totals.null_subtotals === 0 ? '✅' : '❌'} Orders with null subtotals: ${totals.null_subtotals}`);
    console.log(`${totals.null_totals === 0 ? '✅' : '❌'} Orders with null totals: ${totals.null_totals}`);
    console.log(`${totals.negative_totals === 0 ? '✅' : '❌'} Orders with negative totals: ${totals.negative_totals}`);
    console.log(`📊 Average order value: ₹${parseFloat(totals.avg_total || 0).toFixed(2)}`);

    // 6. Verify item totals match order subtotals
    console.log('\n🔢 ORDER-ITEM TOTAL CONSISTENCY CHECK');
    console.log('=' .repeat(50));
    
    const [inconsistentTotals] = await connection.execute(`
      SELECT COUNT(*) as inconsistent_count
      FROM sales_orders so
      LEFT JOIN (
        SELECT 
          sales_order_id, 
          SUM(total_price) as calculated_subtotal
        FROM sales_order_items 
        GROUP BY sales_order_id
      ) items ON so.id = items.sales_order_id
      WHERE ABS(COALESCE(so.subtotal, 0) - COALESCE(items.calculated_subtotal, 0)) > 0.01
    `);
    
    const inconsistentCount = inconsistentTotals[0].inconsistent_count;
    console.log(`${inconsistentCount === 0 ? '✅' : '❌'} Orders with inconsistent subtotals: ${inconsistentCount}`);

    // 7. Check for orphaned records
    console.log('\n🔗 ORPHANED RECORDS CHECK');
    console.log('=' .repeat(50));
    
    // Check for sales order items without parent orders
    const [orphanedItems] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM sales_order_items soi
      LEFT JOIN sales_orders so ON soi.sales_order_id = so.id
      WHERE so.id IS NULL
    `);
    console.log(`${orphanedItems[0].count === 0 ? '✅' : '❌'} Orphaned sales order items: ${orphanedItems[0].count}`);

    // Check for sales order payments without parent orders
    const [orphanedPayments] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM sales_order_payments sop
      LEFT JOIN sales_orders so ON sop.sales_order_id = so.id
      WHERE so.id IS NULL
    `);
    console.log(`${orphanedPayments[0].count === 0 ? '✅' : '❌'} Orphaned sales order payments: ${orphanedPayments[0].count}`);

    // 8. Final summary
    console.log('\n📋 MIGRATION VERIFICATION SUMMARY');
    console.log('=' .repeat(50));
    
    const issues = [];
    if (invalidStatusFound) issues.push('Invalid sales order statuses found');
    if (invalidPaymentStatusFound) issues.push('Invalid payment statuses found');
    if (totals.null_subtotals > 0) issues.push('Orders with null subtotals');
    if (totals.null_totals > 0) issues.push('Orders with null totals');
    if (totals.negative_totals > 0) issues.push('Orders with negative totals');
    if (inconsistentCount > 0) issues.push('Orders with inconsistent subtotals');
    if (orphanedItems[0].count > 0) issues.push('Orphaned sales order items');
    if (orphanedPayments[0].count > 0) issues.push('Orphaned sales order payments');

    if (issues.length === 0) {
      console.log('🎉 ALL MIGRATIONS VERIFIED SUCCESSFULLY!');
      console.log('✅ Database integrity is maintained');
      console.log('✅ All data has been properly migrated');
      console.log('✅ No data inconsistencies found');
    } else {
      console.log('⚠️  ISSUES FOUND:');
      issues.forEach(issue => console.log(`   - ${issue}`));
      console.log('\n🔧 Please review and fix these issues');
    }

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run verification
verifyMigrations();
