-- Migration to add GRN Complete workflow with Return/PDD functionality

-- Update sales_orders status enum to include new statuses
ALTER TABLE sales_orders 
MODIFY COLUMN status ENUM(
    'pending', 
    'pending_approval', 
    'confirmed', 
    'processing', 
    'dispatch_pending', 
    'dispatched', 
    'delivered', 
    'completed', 
    'grn_complete',
    'grn_pending_approval',
    'cancelled'
) DEFAULT 'pending';

-- Create table for GRN Return/PDD requests
CREATE TABLE grn_return_pdd_requests (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    sales_order_id VARCHAR(36) NOT NULL,
    request_type ENUM('return', 'pdd', 'both') NOT NULL,
    requested_by VARCHAR(36) NOT NULL,
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    approved_by VARCHAR(36) NULL,
    approved_at TIMESTAMP NULL,
    rejection_reason TEXT NULL,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (requested_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    
    INDEX idx_grn_requests_organization (organization_id),
    INDEX idx_grn_requests_sales_order (sales_order_id),
    INDEX idx_grn_requests_status (approval_status),
    INDEX idx_grn_requests_requested_at (requested_at)
);

-- Create table for GRN Return/PDD items
CREATE TABLE grn_return_pdd_items (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    grn_request_id VARCHAR(36) NOT NULL,
    sales_order_item_id VARCHAR(36) NOT NULL,
    original_quantity INTEGER NOT NULL,
    return_quantity INTEGER DEFAULT 0,
    pdd_percentage DECIMAL(5,2) DEFAULT 0.00,
    pdd_amount DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (grn_request_id) REFERENCES grn_return_pdd_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (sales_order_item_id) REFERENCES sales_order_items(id) ON DELETE CASCADE,
    
    INDEX idx_grn_items_organization (organization_id),
    INDEX idx_grn_items_request (grn_request_id),
    INDEX idx_grn_items_sales_order_item (sales_order_item_id)
);
