-- Create User Page Permissions table for user-specific page access
CREATE TABLE IF NOT EXISTS user_page_permissions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    page_id VARCHAR(36) NOT NULL,
    can_view BOOLEAN DEFAULT FALSE,
    can_edit BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_page (user_id, page_id),
    INDEX idx_user_page_permissions_user_id (user_id),
    INDEX idx_user_page_permissions_page_id (page_id),
    INDEX idx_user_page_permissions_can_view (can_view),
    INDEX idx_user_page_permissions_can_edit (can_edit)
);

-- Give all existing users default dashboard view permission
INSERT INTO user_page_permissions (user_id, page_id, can_view, can_edit)
SELECT 
    u.id as user_id,
    p.id as page_id,
    TRUE as can_view,
    FALSE as can_edit
FROM users u
CROSS JOIN pages p
WHERE p.name = 'dashboard';
