-- Create Vehicle Arrivals table with organization_id
CREATE TABLE vehicle_arrivals (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    vehicle_number VARCHAR(50),
    supplier VARCHAR(255) NOT NULL,
    driver_name VARCHAR(255),
    driver_contact VARCHAR(20),
    arrival_time TIMESTAMP NOT NULL,
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    INDEX idx_vehicle_arrivals_organization_id (organization_id),
    INDEX idx_vehicle_arrivals_status (status),
    INDEX idx_vehicle_arrivals_arrival_time (arrival_time)
);

-- Create Vehicle Arrival Items table with organization_id
CREATE TABLE vehicle_arrival_items (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    vehicle_arrival_id VARCHAR(36) NOT NULL,
    product_id VARCHAR(36) NOT NULL,
    sku_id VARCHAR(36) NOT NULL,
    unit_type ENUM('box', 'loose') NOT NULL,
    unit_weight DECIMAL(10,3),
    quantity INT NOT NULL,
    total_weight DECIMAL(10,3) NOT NULL,
    final_quantity INT DEFAULT 0,
    final_total_weight DECIMAL(10,3) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_arrival_id) REFERENCES vehicle_arrivals(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (sku_id) REFERENCES skus(id) ON DELETE CASCADE,
    INDEX idx_vehicle_arrival_items_organization_id (organization_id),
    INDEX idx_vehicle_arrival_items_vehicle_arrival_id (vehicle_arrival_id),
    INDEX idx_vehicle_arrival_items_product_id (product_id),
    INDEX idx_vehicle_arrival_items_sku_id (sku_id)
);

-- Create Vehicle Arrival Attachments table with organization_id
CREATE TABLE vehicle_arrival_attachments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    vehicle_arrival_id VARCHAR(36) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    file_url TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_arrival_id) REFERENCES vehicle_arrivals(id) ON DELETE CASCADE,
    INDEX idx_vehicle_arrival_attachments_organization_id (organization_id),
    INDEX idx_vehicle_arrival_attachments_vehicle_arrival_id (vehicle_arrival_id)
);

-- Create Purchase Records table with organization_id
CREATE TABLE purchase_records (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    vehicle_arrival_id VARCHAR(36),
    supplier_id VARCHAR(36),
    record_number VARCHAR(100) NOT NULL,
    supplier VARCHAR(255) NOT NULL,
    record_date DATE NOT NULL,
    arrival_timestamp TIMESTAMP NOT NULL,
    pricing_model ENUM('market_price', 'fixed_price', 'commission_based') DEFAULT 'market_price',
    default_commission DECIMAL(5,2),
    payment_terms INT,
    items_subtotal DECIMAL(15,2) DEFAULT 0.00,
    additional_costs_total DECIMAL(15,2) DEFAULT 0.00,
    total_amount DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('draft', 'confirmed', 'completed', 'cancelled') DEFAULT 'draft',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_arrival_id) REFERENCES vehicle_arrivals(id) ON DELETE SET NULL,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
    UNIQUE KEY unique_record_number_per_org (organization_id, record_number),
    INDEX idx_purchase_records_organization_id (organization_id),
    INDEX idx_purchase_records_status (status),
    INDEX idx_purchase_records_record_date (record_date)
);

-- Create Purchase Record Items table with organization_id
CREATE TABLE purchase_record_items (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    purchase_record_id VARCHAR(36) NOT NULL,
    product_id VARCHAR(36) NOT NULL,
    sku_id VARCHAR(36) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    sku_code VARCHAR(100) NOT NULL,
    category VARCHAR(100) NOT NULL,
    quantity INT NOT NULL,
    unit_type ENUM('box', 'loose') NOT NULL,
    total_weight DECIMAL(10,3) NOT NULL,
    market_price DECIMAL(10,2),
    commission DECIMAL(5,2),
    unit_price DECIMAL(10,2) NOT NULL,
    total DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (purchase_record_id) REFERENCES purchase_records(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (sku_id) REFERENCES skus(id) ON DELETE CASCADE,
    INDEX idx_purchase_record_items_organization_id (organization_id),
    INDEX idx_purchase_record_items_purchase_record_id (purchase_record_id),
    INDEX idx_purchase_record_items_product_id (product_id),
    INDEX idx_purchase_record_items_sku_id (sku_id)
);

-- Create Purchase Record Costs table with organization_id
CREATE TABLE purchase_record_costs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    purchase_record_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    type ENUM('fixed', 'percentage') DEFAULT 'fixed',
    calculated_amount DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (purchase_record_id) REFERENCES purchase_records(id) ON DELETE CASCADE,
    INDEX idx_purchase_record_costs_organization_id (organization_id),
    INDEX idx_purchase_record_costs_purchase_record_id (purchase_record_id)
);

-- Create Sales Orders table with organization_id
CREATE TABLE sales_orders (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    order_number VARCHAR(100) NOT NULL,
    customer_id VARCHAR(36) NOT NULL,
    order_date DATE DEFAULT (CURRENT_DATE),
    delivery_date DATE,
    delivery_address TEXT,
    payment_terms INT,
    payment_mode ENUM('cash', 'credit', 'online', 'cheque') DEFAULT 'cash',
    payment_status ENUM('pending', 'partial', 'paid', 'overdue') DEFAULT 'pending',
    subtotal DECIMAL(15,2),
    tax_amount DECIMAL(15,2),
    discount_amount DECIMAL(15,2),
    total_amount DECIMAL(15,2),
    status ENUM('draft', 'confirmed', 'dispatched', 'delivered', 'cancelled') DEFAULT 'draft',
    notes TEXT,
    vehicle_number VARCHAR(50),
    driver_name VARCHAR(255),
    driver_contact VARCHAR(20),
    delivery_location_confirmed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_order_number_per_org (organization_id, order_number),
    INDEX idx_sales_orders_organization_id (organization_id),
    INDEX idx_sales_orders_customer_id (customer_id),
    INDEX idx_sales_orders_status (status),
    INDEX idx_sales_orders_order_date (order_date)
);

-- Create Sales Order Items table with organization_id
CREATE TABLE sales_order_items (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    sales_order_id VARCHAR(36) NOT NULL,
    product_id VARCHAR(36) NOT NULL,
    sku_id VARCHAR(36) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    sku_code VARCHAR(100) NOT NULL,
    quantity INT NOT NULL,
    unit_type ENUM('box', 'loose') NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (sku_id) REFERENCES skus(id) ON DELETE CASCADE,
    INDEX idx_sales_order_items_organization_id (organization_id),
    INDEX idx_sales_order_items_sales_order_id (sales_order_id),
    INDEX idx_sales_order_items_product_id (product_id),
    INDEX idx_sales_order_items_sku_id (sku_id)
);

-- Create Sales Order Payments table with organization_id
CREATE TABLE sales_order_payments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    sales_order_id VARCHAR(36) NOT NULL,
    payment_type ENUM('advance', 'partial', 'full', 'refund') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    reference_number VARCHAR(255),
    proof_url TEXT,
    remarks TEXT,
    status ENUM('pending', 'confirmed', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id) ON DELETE CASCADE,
    INDEX idx_sales_order_payments_organization_id (organization_id),
    INDEX idx_sales_order_payments_sales_order_id (sales_order_id),
    INDEX idx_sales_order_payments_status (status)
);

-- Create Current Inventory table with organization_id
CREATE TABLE current_inventory (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    product_id VARCHAR(36) NOT NULL,
    sku_id VARCHAR(36) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    sku_code VARCHAR(100) NOT NULL,
    category VARCHAR(100) NOT NULL,
    unit_type ENUM('box', 'loose') NOT NULL,
    available_quantity INT DEFAULT 0,
    total_weight DECIMAL(10,3) DEFAULT 0,
    last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (sku_id) REFERENCES skus(id) ON DELETE CASCADE,
    UNIQUE KEY unique_inventory_per_org (organization_id, product_id, sku_id),
    INDEX idx_current_inventory_organization_id (organization_id),
    INDEX idx_current_inventory_product_id (product_id),
    INDEX idx_current_inventory_sku_id (sku_id)
);

-- Create Payments table with organization_id
CREATE TABLE payments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    type ENUM('received', 'paid') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    payment_date DATE DEFAULT (CURRENT_DATE),
    party_id VARCHAR(36),
    party_type ENUM('customer', 'supplier'),
    party_name VARCHAR(255),
    reference_id VARCHAR(36),
    reference_type ENUM('sales_order', 'purchase_record'),
    reference_number VARCHAR(255),
    mode ENUM('cash', 'online', 'cheque', 'bank_transfer') DEFAULT 'cash',
    status ENUM('pending', 'confirmed', 'failed') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    INDEX idx_payments_organization_id (organization_id),
    INDEX idx_payments_type (type),
    INDEX idx_payments_party_id (party_id),
    INDEX idx_payments_reference_id (reference_id),
    INDEX idx_payments_payment_date (payment_date)
);

-- Create Customer Credit Extensions table with organization_id
CREATE TABLE customer_credit_extensions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    customer_id VARCHAR(36) NOT NULL,
    sales_order_id VARCHAR(36),
    amount DECIMAL(15,2) NOT NULL,
    remarks TEXT,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    approved_by VARCHAR(36),
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id) ON DELETE SET NULL,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_customer_credit_extensions_organization_id (organization_id),
    INDEX idx_customer_credit_extensions_customer_id (customer_id),
    INDEX idx_customer_credit_extensions_status (status)
);
