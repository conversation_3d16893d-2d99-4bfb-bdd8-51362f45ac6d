-- Migration: Fix Inventory Unit Type Separation
-- Description: Ensures that products with different unit types are treated as distinct inventory items
-- Problem: The original constraint UNIQUE KEY unique_inventory_per_org (organization_id, product_id, sku_id)
--          does not include unit_type, causing different unit types to merge incorrectly

-- Step 1: Create backup table for safety
CREATE TABLE IF NOT EXISTS current_inventory_backup_unit_fix AS
SELECT * FROM current_inventory;

-- Step 2: Update any inventory records where unit_type doesn't match the SKU's unit type
UPDATE current_inventory 
JOIN skus ON current_inventory.sku_id = skus.id 
SET current_inventory.unit_type = skus.unit_type
WHERE current_inventory.unit_type != skus.unit_type;

-- Step 3: Identify records that will violate the new constraint
-- These are records with same (org, product, sku, unit_type) but different IDs
CREATE TEMPORARY TABLE duplicate_groups AS
SELECT 
    organization_id, 
    product_id, 
    sku_id, 
    unit_type,
    COUNT(*) as record_count,
    MIN(id) as keep_id,
    SUM(available_quantity) as total_quantity,
    SUM(CAST(total_weight AS DECIMAL(10,3))) as total_weight_sum
FROM current_inventory
GROUP BY organization_id, product_id, sku_id, unit_type
HAVING COUNT(*) > 1;

-- Step 4: Create audit trail for consolidations before making changes
INSERT INTO inventory_transactions (
    organization_id, product_id, sku_id, transaction_type,
    quantity_change, weight_change, reference_type, reference_id,
    reason, notes, created_at, updated_at
)
SELECT 
    dg.organization_id, 
    dg.product_id, 
    dg.sku_id,
    'adjustment',
    dg.total_quantity,
    dg.total_weight_sum,
    'manual_adjustment',
    dg.keep_id,
    'Inventory unit type separation fix - duplicate consolidation',
    CONCAT('Consolidated ', dg.record_count, ' duplicate inventory records for same product/SKU/unit_type combination'),
    NOW(),
    NOW()
FROM duplicate_groups dg;

-- Step 5: Update the records we're keeping with consolidated quantities
UPDATE current_inventory ci
JOIN duplicate_groups dg ON ci.id = dg.keep_id
SET 
    ci.available_quantity = dg.total_quantity,
    ci.total_weight = dg.total_weight_sum,
    ci.last_updated_at = NOW();

-- Step 6: Delete duplicate records (keeping only one per group)
DELETE ci FROM current_inventory ci
INNER JOIN duplicate_groups dg 
    ON ci.organization_id = dg.organization_id 
    AND ci.product_id = dg.product_id 
    AND ci.sku_id = dg.sku_id 
    AND ci.unit_type = dg.unit_type
WHERE ci.id != dg.keep_id;

-- Step 7: Create summary of changes
SELECT 
    (SELECT COUNT(*) FROM current_inventory_backup_unit_fix) as records_before_fix,
    (SELECT COUNT(*) FROM current_inventory) as records_after_fix,
    'Migration 046 completed successfully - constraints must be handled manually' as status;
