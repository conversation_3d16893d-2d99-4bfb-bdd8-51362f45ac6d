-- Migration to fix sales order total calculations
-- This migration recalculates all sales order totals to ensure consistency

-- First, update all item total_price calculations to ensure they're correct
UPDATE sales_order_items 
SET total_price = ROUND(quantity * unit_price, 2)
WHERE total_price != ROUND(quantity * unit_price, 2);

-- Then, recalculate subtotals for all sales orders based on their items
UPDATE sales_orders 
SET subtotal = (
    SELECT COALESCE(SUM(total_price), 0)
    FROM sales_order_items 
    WHERE sales_order_id = sales_orders.id
);

-- Finally, recalculate total_amount for all sales orders
UPDATE sales_orders 
SET total_amount = ROUND(
    COALESCE(subtotal, 0) + COALESCE(tax_amount, 0) - COALESCE(discount_amount, 0), 
    2
);

-- Add a check to ensure no negative totals exist (data validation)
UPDATE sales_orders 
SET total_amount = 0 
WHERE total_amount < 0;
