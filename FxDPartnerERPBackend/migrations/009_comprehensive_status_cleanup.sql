-- Comprehensive migration to clean up all legacy status values
-- This ensures all orders use the standardized status format

-- First, let's see what statuses currently exist (this will be logged)
-- Then standardize everything to the current workflow

-- Standardize dispatch_pending status (all variations)
UPDATE sales_orders 
SET status = 'dispatch_pending' 
WHERE status IN ('Dispatch Pending', 'dispatch pending', 'Dispatch_Pending', 'DISPATCH_PENDING');

-- Standardize pending_approval status (all variations)
UPDATE sales_orders 
SET status = 'pending_approval' 
WHERE status IN ('Pending Approval', 'pending approval', 'Pending_Approval', 'PENDING_APPROVAL');

-- Standardize dispatched status
UPDATE sales_orders 
SET status = 'dispatched' 
WHERE status IN ('Dispatched', 'DISPATCHED');

-- Standardize completed status
UPDATE sales_orders 
SET status = 'completed' 
WHERE status IN ('Completed', 'COMPLETED');

-- Standardize cancelled status
UPDATE sales_orders 
SET status = 'cancelled' 
WHERE status IN ('Cancelled', 'Canceled', 'CANCELLED', 'CANCELED');

-- Handle legacy "processing" status intelligently
-- If has delivery info -> it's an outstation sale -> dispatch_pending
-- If no delivery info -> it's a counter sale -> completed
UPDATE sales_orders 
SET status = 'dispatch_pending' 
WHERE status IN ('Processing', 'processing', 'PROCESSING') 
  AND (delivery_date IS NOT NULL OR delivery_address IS NOT NULL);

UPDATE sales_orders 
SET status = 'completed' 
WHERE status IN ('Processing', 'processing', 'PROCESSING') 
  AND delivery_date IS NULL 
  AND delivery_address IS NULL;

-- Convert other legacy completion statuses
UPDATE sales_orders 
SET status = 'completed' 
WHERE status IN ('Delivered', 'delivered', 'DELIVERED', 'Shipped', 'shipped', 'SHIPPED');

-- Convert draft/pending statuses to pending_approval
UPDATE sales_orders 
SET status = 'pending_approval' 
WHERE status IN ('Draft', 'draft', 'DRAFT', 'Pending', 'pending', 'PENDING');

-- Handle any other edge cases that might exist
-- Convert any status that doesn't match our standard workflow
UPDATE sales_orders 
SET status = 'completed' 
WHERE status NOT IN ('dispatch_pending', 'pending_approval', 'dispatched', 'completed', 'cancelled')
  AND (delivery_date IS NULL AND delivery_address IS NULL);

UPDATE sales_orders 
SET status = 'dispatch_pending' 
WHERE status NOT IN ('dispatch_pending', 'pending_approval', 'dispatched', 'completed', 'cancelled')
  AND (delivery_date IS NOT NULL OR delivery_address IS NOT NULL);
