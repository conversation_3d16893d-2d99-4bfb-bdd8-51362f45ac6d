-- Migration: Add discount source tracking to sales orders and create detailed discount tracking table
-- Date: 2025-01-08

-- Add discount source and reason fields to sales_orders table
ALTER TABLE sales_orders 
ADD COLUMN discount_source ENUM('manual', 'pdd', 'promotional', 'customer_credit', 'bulk_discount', 'damaged_goods') NULL,
ADD COLUMN discount_reason TEXT NULL;

-- Create sales_order_discounts table for detailed discount tracking
CREATE TABLE sales_order_discounts (
    id CHAR(36) PRIMARY KEY,
    organization_id CHAR(36) NOT NULL,
    sales_order_id CHAR(36) NOT NULL,
    sales_order_item_id CHAR(36) NULL, -- Optional - for item-level discounts
    discount_type ENUM('manual', 'pdd', 'promotional', 'customer_credit', 'bulk_discount', 'damaged_goods') NOT NULL,
    discount_amount DECIMAL(10,2) NOT NULL,
    discount_percentage DECIMAL(5,2) NULL,
    reason TEXT NULL,
    return_quantity INT NULL, -- For PDD discounts - quantity returned
    pdd_request_id CHAR(36) NULL, -- Link to PDD request if applicable
    applied_by CHAR(36) NOT NULL,
    applied_at DATETIME NOT NULL,
    status ENUM('active', 'reversed', 'expired') NOT NULL DEFAULT 'active',
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_sales_order_discounts_organization 
        FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT fk_sales_order_discounts_sales_order 
        FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id) ON DELETE CASCADE,
    CONSTRAINT fk_sales_order_discounts_sales_order_item 
        FOREIGN KEY (sales_order_item_id) REFERENCES sales_order_items(id) ON DELETE CASCADE,
    CONSTRAINT fk_sales_order_discounts_pdd_request 
        FOREIGN KEY (pdd_request_id) REFERENCES grn_return_pdd_requests(id) ON DELETE SET NULL,
    CONSTRAINT fk_sales_order_discounts_applied_by 
        FOREIGN KEY (applied_by) REFERENCES users(id) ON DELETE RESTRICT,
        
    -- Indexes for performance
    INDEX idx_sales_order_discounts_sales_order (sales_order_id),
    INDEX idx_sales_order_discounts_organization (organization_id),
    INDEX idx_sales_order_discounts_type (discount_type),
    INDEX idx_sales_order_discounts_status (status),
    INDEX idx_sales_order_discounts_pdd_request (pdd_request_id)
);

-- Update existing sales orders with default discount source for existing discounts
UPDATE sales_orders 
SET discount_source = 'manual', 
    discount_reason = 'Legacy discount - source not tracked'
WHERE discount_amount > 0 AND discount_source IS NULL;

-- Add comment to document the migration
ALTER TABLE sales_orders COMMENT = 'Sales orders with discount source tracking - Updated 2025-01-08';
ALTER TABLE sales_order_discounts COMMENT = 'Detailed discount tracking for sales orders - Created 2025-01-08';
