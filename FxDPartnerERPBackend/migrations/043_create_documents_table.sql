-- Migration: Create documents table for file upload system
-- Date: 2025-01-17
-- Description: Creates the documents table to store file metadata for S3 uploads

CREATE TABLE documents (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    file_name VARCHAR(255) NOT NULL,
    file_key VARCHAR(255) NOT NULL UNIQUE,
    file_url TEXT,
    file_size INT NOT NULL,
    content_type VARCHAR(255) NOT NULL,
    entity_type ENUM(
        'purchase_record',
        'sales_order', 
        'vehicle_arrival',
        'payment',
        'supplier',
        'customer',
        'inventory',
        'grn_return'
    ) NOT NULL,
    entity_id VARCHAR(255) NOT NULL,
    display_name VA<PERSON>HAR(255),
    organization_id CHAR(36) NOT NULL,
    uploaded_by CHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_documents_entity (entity_type, entity_id),
    INDEX idx_documents_organization (organization_id),
    INDEX idx_documents_uploaded_by (uploaded_by),
    INDEX idx_documents_created_at (created_at),
    
    -- Foreign key constraints
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Add comments for documentation
ALTER TABLE documents COMMENT = 'Stores metadata for files uploaded to S3, linked to various entities';
ALTER TABLE documents MODIFY COLUMN file_name VARCHAR(255) NOT NULL COMMENT 'Original filename as uploaded by user';
ALTER TABLE documents MODIFY COLUMN file_key VARCHAR(255) NOT NULL COMMENT 'Unique S3 object key for the file';
ALTER TABLE documents MODIFY COLUMN file_url TEXT COMMENT 'Pre-signed URL for file access (expires after 24 hours)';
ALTER TABLE documents MODIFY COLUMN file_size INT NOT NULL COMMENT 'File size in bytes';
ALTER TABLE documents MODIFY COLUMN content_type VARCHAR(255) NOT NULL COMMENT 'MIME type of the file';
ALTER TABLE documents MODIFY COLUMN entity_type ENUM(
    'purchase_record',
    'sales_order', 
    'vehicle_arrival',
    'payment',
    'supplier',
    'customer',
    'inventory',
    'grn_return'
) NOT NULL COMMENT 'Type of entity this document is attached to';
ALTER TABLE documents MODIFY COLUMN entity_id VARCHAR(255) NOT NULL COMMENT 'ID of the entity this document is attached to';
ALTER TABLE documents MODIFY COLUMN display_name VARCHAR(255) COMMENT 'User-friendly name for the document';
ALTER TABLE documents MODIFY COLUMN organization_id CHAR(36) NOT NULL COMMENT 'Organization that owns this document';
ALTER TABLE documents MODIFY COLUMN uploaded_by CHAR(36) NOT NULL COMMENT 'User who uploaded this document';
