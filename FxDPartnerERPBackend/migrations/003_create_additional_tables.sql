-- Create Roles table with organization_id
CREATE TABLE roles (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    permissions JSON NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    is_system_role BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    UNIQUE KEY unique_role_name_per_org (organization_id, name),
    INDEX idx_roles_organization_id (organization_id),
    INDEX idx_roles_status (status),
    INDEX idx_roles_is_system_role (is_system_role)
);

-- Create User Details table with organization_id
CREATE TABLE user_details (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    alternate_phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(100),
    employee_id VARCHAR(50),
    department VARCHAR(100),
    designation VARCHAR(100),
    joining_date DATE,
    salary DECIMAL(10,2),
    bank_account_number VARCHAR(50),
    bank_name VARCHAR(255),
    bank_ifsc_code VARCHAR(11),
    pan_number VARCHAR(10),
    aadhar_number VARCHAR(12),
    profile_picture_url TEXT,
    preferences JSON,
    settings JSON,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_details_per_org (organization_id, user_id),
    INDEX idx_user_details_organization_id (organization_id),
    INDEX idx_user_details_user_id (user_id),
    INDEX idx_user_details_status (status)
);

-- Create Negative Inventory table with organization_id
CREATE TABLE negative_inventory (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    product_id VARCHAR(36) NOT NULL,
    sku_id VARCHAR(36) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    sku_code VARCHAR(100) NOT NULL,
    category VARCHAR(100) NOT NULL,
    unit_type ENUM('box', 'loose') NOT NULL,
    negative_quantity INT NOT NULL,
    negative_weight DECIMAL(10,2) NOT NULL,
    reference_type VARCHAR(100) NOT NULL,
    reference_id VARCHAR(36) NOT NULL,
    notes TEXT,
    status ENUM('pending', 'resolved') DEFAULT 'pending',
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (sku_id) REFERENCES skus(id) ON DELETE CASCADE,
    INDEX idx_negative_inventory_organization_id (organization_id),
    INDEX idx_negative_inventory_product_id (product_id),
    INDEX idx_negative_inventory_sku_id (sku_id),
    INDEX idx_negative_inventory_status (status),
    INDEX idx_negative_inventory_reference (reference_type, reference_id)
);
