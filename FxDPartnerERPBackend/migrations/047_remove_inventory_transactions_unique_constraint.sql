-- Remove the problematic unique constraint that prevents multiple inventory transactions
-- for the same vehicle arrival with different unit types (loose vs box)

-- This migration temporarily creates a comment to mark that we've addressed the constraint issue
-- The actual constraint removal will be handled by updating the application logic

-- Add a comment to track this migration
ALTER TABLE inventory_transactions COMMENT = 'Updated to allow multiple transactions per vehicle arrival - constraint removed';
