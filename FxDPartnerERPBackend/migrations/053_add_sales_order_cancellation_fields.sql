-- Add cancellation tracking fields to sales_orders table
ALTER TABLE sales_orders 
ADD COLUMN cancelled_at TIMESTAMP NULL,
ADD COLUMN cancelled_by CHAR(36) NULL,
ADD COLUMN cancellation_reason TEXT NULL;

-- Add foreign key constraint for cancelled_by
ALTER TABLE sales_orders 
ADD CONSTRAINT fk_sales_orders_cancelled_by 
FOREIGN KEY (cancelled_by) REFERENCES users(id);

-- Add index for cancelled orders
CREATE INDEX idx_sales_orders_cancelled_at ON sales_orders(cancelled_at);
