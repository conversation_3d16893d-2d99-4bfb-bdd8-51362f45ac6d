-- Create inventory_transactions table for tracking all inventory movements
CREATE TABLE inventory_transactions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    product_id VARCHAR(36) NOT NULL,
    sku_id VARCHAR(36) NOT NULL,
    transaction_type VARCHAR(50) NOT NULL,
    quantity_change INTEGER NOT NULL,
    weight_change DECIMAL(10, 2) DEFAULT 0,
    reference_type VARCHAR(50) NOT NULL,
    reference_id VARCHAR(36) NOT NULL,
    performed_by VARCHAR(36) NULL,
    reason TEXT NOT NULL,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Add constraints
    CONSTRAINT chk_transaction_type CHECK (transaction_type IN ('return_to_inventory', 'sale', 'purchase', 'adjustment', 'vehicle_arrival')),
    CONSTRAINT chk_reference_type CHECK (reference_type IN ('grn_return', 'sales_order', 'purchase_record', 'vehicle_arrival', 'manual_adjustment')),
    
    -- Add foreign key constraints
    CONSTRAINT fk_inventory_transactions_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT fk_inventory_transactions_product FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    CONSTRAINT fk_inventory_transactions_sku FOREIGN KEY (sku_id) REFERENCES skus(id) ON DELETE CASCADE,
    CONSTRAINT fk_inventory_transactions_user FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Create indexes for better query performance
CREATE INDEX idx_inventory_transactions_organization_id ON inventory_transactions(organization_id);
CREATE INDEX idx_inventory_transactions_product_sku ON inventory_transactions(product_id, sku_id);
CREATE INDEX idx_inventory_transactions_reference ON inventory_transactions(reference_type, reference_id);
CREATE INDEX idx_inventory_transactions_type ON inventory_transactions(transaction_type);
CREATE INDEX idx_inventory_transactions_created_at ON inventory_transactions(created_at);
