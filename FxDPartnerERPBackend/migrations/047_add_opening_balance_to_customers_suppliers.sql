-- Migration: Add opening balance fields to customers and suppliers
-- Created: 2025-07-22

-- Add opening balance fields to customers table
ALTER TABLE customers 
ADD COLUMN opening_balance DECIMAL(15,2) DEFAULT 0.00,
ADD COLUMN opening_balance_type ENUM('debit', 'credit') DEFAULT NULL;

-- Add opening balance fields to suppliers table  
ALTER TABLE suppliers
ADD COLUMN opening_balance DECIMAL(15,2) DEFAULT 0.00,
ADD COLUMN opening_balance_type ENUM('debit', 'credit') DEFAULT NULL;

-- Update all existing records to have 0 opening balance
UPDATE customers SET opening_balance = 0.00 WHERE opening_balance IS NULL;
UPDATE suppliers SET opening_balance = 0.00 WHERE opening_balance IS NULL;
