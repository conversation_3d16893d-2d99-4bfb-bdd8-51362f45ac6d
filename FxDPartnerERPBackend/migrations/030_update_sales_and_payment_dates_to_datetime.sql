-- Migration to change order_date and payment_date from DATE to DATETIME to support both date and time
-- This allows sales orders and payments to store precise date and time information

-- Update sales_orders table
ALTER TABLE sales_orders 
MODIFY COLUMN order_date DATETIME DEFAULT (NOW());

ALTER TABLE sales_orders 
MODIFY COLUMN delivery_date DATETIME NULL;

-- Update payments table
ALTER TABLE payments 
MODIFY COLUMN payment_date DATETIME DEFAULT (NOW());

-- Update indexes to reflect the new data types
DROP INDEX idx_sales_orders_order_date ON sales_orders;
CREATE INDEX idx_sales_orders_order_date ON sales_orders(order_date);

DROP INDEX idx_payments_payment_date ON payments;
CREATE INDEX idx_payments_payment_date ON payments(payment_date);
