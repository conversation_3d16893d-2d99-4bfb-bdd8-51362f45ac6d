-- Note: GRN approval columns may not exist in current schema, skipping column drops
-- Focus on sales order status updates which are the main requirement

-- Update existing pending GRN requests to be considered as processed
UPDATE grn_return_pdd_requests
SET requested_at = NOW()
WHERE requested_at IS NULL;

-- Convert pending_approval and grn_pending_approval statuses
UPDATE sales_orders
SET status = 'processing'
WHERE status = 'pending_approval';

UPDATE sales_orders
SET status = 'completed'
WHERE status = 'grn_pending_approval';

-- Modify column to remove enum constraint temporarily
ALTER TABLE sales_orders MODIFY COLUMN status VARCHAR(50);

-- Add new check constraint with updated values
ALTER TABLE sales_orders
  ADD CONSTRAINT sales_orders_status_check CHECK (
    status IN (
      'pending',
      'confirmed',
      'processing',
      'dispatch_pending',
      'dispatched',
      'delivered',
      'completed',
      'grn_complete',
      'cancelled'
    )
  );
