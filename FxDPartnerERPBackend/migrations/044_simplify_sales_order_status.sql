-- Migration to simplify sales order status flow
-- Remove grn_complete status and update existing records

-- Update existing grn_complete orders to completed status
UPDATE sales_orders
SET status = 'completed'
WHERE status = 'grn_complete';

-- Drop the existing constraint
ALTER TABLE sales_orders DROP CONSTRAINT sales_orders_status_check;

-- Modify column to remove enum constraint temporarily
ALTER TABLE sales_orders MODIFY COLUMN status VARCHAR(50);

-- Add new check constraint with simplified status values
ALTER TABLE sales_orders
  ADD CONSTRAINT sales_orders_status_check CHECK (
    status IN (
      'pending',
      'confirmed',
      'processing',
      'dispatch_pending',
      'dispatched',
      'delivered',
      'completed',
      'cancelled'
    )
  );
