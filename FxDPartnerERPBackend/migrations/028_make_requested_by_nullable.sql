-- Migration to make requested_by nullable in grn_return_pdd_requests table

-- Remove the foreign key constraint first
ALTER TABLE grn_return_pdd_requests DROP FOREIGN KEY grn_return_pdd_requests_ibfk_3;

-- Modify the column to be nullable
ALTER TABLE grn_return_pdd_requests 
MODIFY COLUMN requested_by CHAR(36) NULL;

-- Re-add the foreign key constraint
ALTER TABLE grn_return_pdd_requests 
ADD CONSTRAINT grn_return_pdd_requests_ibfk_3 
FOREIGN KEY (requested_by) REFERENCES users(id);
