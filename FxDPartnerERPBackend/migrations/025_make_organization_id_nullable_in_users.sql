-- Make organization_id nullable in users table to allow simple user creation
-- Users can still belong to multiple organizations via the user_organizations junction table

ALTER TABLE users MODIFY COLUMN organization_id VARCHAR(36) NULL;

-- Add the new foreign key constraint with explicit name
-- Note: If the old constraint exists, this will fail, but that's expected
-- The migration system should handle this gracefully
ALTER TABLE users ADD CONSTRAINT fk_users_organization_id 
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE SET NULL;
