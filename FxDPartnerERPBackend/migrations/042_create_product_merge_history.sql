-- Create product merge history table to track product merges
CREATE TABLE IF NOT EXISTS product_merge_history (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    source_product_id VARCHAR(36) NOT NULL,
    source_product_name VA<PERSON>HAR(255) NOT NULL,
    target_product_id VARCHAR(36) NOT NULL,
    target_product_name VARCHAR(255) NOT NULL,
    merge_reason VARCHAR(255) NOT NULL,
    merge_details JSON,
    merged_by VA<PERSON>HA<PERSON>(36),
    merged_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIG<PERSON> KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (merged_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_product_merge_history_org (organization_id),
    INDEX idx_product_merge_history_source (source_product_id),
    INDEX idx_product_merge_history_target (target_product_id),
    INDEX idx_product_merge_history_date (merged_at)
);
