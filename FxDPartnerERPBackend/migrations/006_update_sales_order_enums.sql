-- Update sales_orders status enum to match the model
ALTER TABLE sales_orders 
MODIFY COLUMN status ENUM(
    'pending', 
    'pending_approval', 
    'confirmed', 
    'processing', 
    'dispatch_pending', 
    'dispatched', 
    'delivered', 
    'completed', 
    'cancelled'
) DEFAULT 'pending';

-- Update sales_order_payments payment_type enum to match the model
ALTER TABLE sales_order_payments 
MODIFY COLUMN payment_type ENUM(
    'cash', 
    'credit', 
    'online', 
    'cheque', 
    'upi'
) NOT NULL;
