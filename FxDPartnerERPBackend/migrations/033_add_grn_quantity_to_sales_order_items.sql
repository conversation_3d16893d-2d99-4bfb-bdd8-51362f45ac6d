-- Migration: Add GRN quantity tracking to sales order items
-- This allows us to track the actual quantity after GRN completion
-- without losing the original ordered quantity

ALTER TABLE sales_order_items 
ADD COLUMN grn_quantity DECIMAL(10,3) DEFAULT NULL COMMENT 'Actual quantity after GRN completion';

-- Update existing records to set grn_quantity = quantity for completed orders
UPDATE sales_order_items soi
JOIN sales_orders so ON soi.sales_order_id = so.id
SET soi.grn_quantity = soi.quantity
WHERE so.status IN ('grn_complete', 'completed');
