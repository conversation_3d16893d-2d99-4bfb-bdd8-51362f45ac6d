-- Create Roles table
CREATE TABLE IF NOT EXISTS roles (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    permissions JSON NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    is_system_role BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    UNIQUE KEY unique_role_name_per_org (organization_id, name),
    INDEX idx_roles_organization_id (organization_id),
    INDEX idx_roles_status (status),
    INDEX idx_roles_is_system_role (is_system_role)
);

-- Insert default system roles for the default organization
INSERT IGNORE INTO roles (id, organization_id, name, display_name, description, permissions, status, is_system_role) VALUES
(
    'admin-role-id',
    'default-org-id',
    'admin',
    'Administrator',
    'Full access to all features',
    JSON_ARRAY(
        'user:create', 'user:read', 'user:update', 'user:delete',
        'customer:create', 'customer:read', 'customer:update', 'customer:delete',
        'supplier:create', 'supplier:read', 'supplier:update', 'supplier:delete',
        'product:create', 'product:read', 'product:update', 'product:delete',
        'inventory:create', 'inventory:read', 'inventory:update', 'inventory:delete', 'inventory:adjust',
        'sales:create', 'sales:read', 'sales:update', 'sales:delete', 'sales:approve',
        'purchase:create', 'purchase:read', 'purchase:update', 'purchase:delete', 'purchase:approve',
        'payment:create', 'payment:read', 'payment:update', 'payment:delete',
        'reports:view', 'reports:export',
        'settings:view', 'settings:update',
        'organization:manage',
        'role:create', 'role:read', 'role:update', 'role:delete'
    ),
    'active',
    TRUE
),
(
    'manager-role-id',
    'default-org-id',
    'manager',
    'Manager',
    'Management access with approval permissions',
    JSON_ARRAY(
        'user:read',
        'customer:create', 'customer:read', 'customer:update',
        'supplier:create', 'supplier:read', 'supplier:update',
        'product:create', 'product:read', 'product:update',
        'inventory:read', 'inventory:adjust',
        'sales:create', 'sales:read', 'sales:update', 'sales:approve',
        'purchase:create', 'purchase:read', 'purchase:update', 'purchase:approve',
        'payment:create', 'payment:read', 'payment:update',
        'reports:view', 'reports:export',
        'settings:view'
    ),
    'active',
    TRUE
),
(
    'sales-executive-role-id',
    'default-org-id',
    'sales_executive',
    'Sales Executive',
    'Sales and customer management access',
    JSON_ARRAY(
        'customer:create', 'customer:read', 'customer:update',
        'product:read',
        'inventory:read',
        'sales:create', 'sales:read', 'sales:update',
        'payment:read',
        'reports:view'
    ),
    'active',
    TRUE
),
(
    'purchase-executive-role-id',
    'default-org-id',
    'purchase_executive',
    'Purchase Executive',
    'Purchase and supplier management access',
    JSON_ARRAY(
        'supplier:create', 'supplier:read', 'supplier:update',
        'product:read',
        'inventory:read',
        'purchase:create', 'purchase:read', 'purchase:update',
        'payment:read',
        'reports:view'
    ),
    'active',
    TRUE
),
(
    'inventory-manager-role-id',
    'default-org-id',
    'inventory_manager',
    'Inventory Manager',
    'Inventory and product management access',
    JSON_ARRAY(
        'product:create', 'product:read', 'product:update',
        'inventory:create', 'inventory:read', 'inventory:update', 'inventory:adjust',
        'reports:view'
    ),
    'active',
    TRUE
),
(
    'viewer-role-id',
    'default-org-id',
    'viewer',
    'Viewer',
    'Read-only access to most features',
    JSON_ARRAY(
        'customer:read',
        'supplier:read',
        'product:read',
        'inventory:read',
        'sales:read',
        'purchase:read',
        'payment:read',
        'reports:view'
    ),
    'active',
    TRUE
);
