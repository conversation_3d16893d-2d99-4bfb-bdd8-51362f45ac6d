-- Migration: Add unit_type column to inventory_transactions table
-- Description: Fixes schema mismatch where Sequelize model has unit_type but database table doesn't
-- Problem: Application is trying to insert unit_type but column doesn't exist in database

-- Step 1: Add the unit_type column to inventory_transactions table
ALTER TABLE inventory_transactions 
ADD COLUMN unit_type ENUM('box', 'loose') DEFAULT 'box' AFTER sku_id;

-- Step 2: Update existing records with correct unit_type from their associated SKUs
UPDATE inventory_transactions it
JOIN skus s ON it.sku_id = s.id
SET it.unit_type = s.unit_type
WHERE it.unit_type IS NULL OR it.unit_type = 'box';

-- Step 3: Add index for better query performance on unit_type
CREATE INDEX idx_inventory_transactions_unit_type ON inventory_transactions(unit_type);

-- Step 4: Add composite index for common queries filtering by product, sku, and unit_type
CREATE INDEX idx_inventory_transactions_product_sku_unit ON inventory_transactions(product_id, sku_id, unit_type);

-- Step 5: Verify the migration was successful
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT,
    COLUMN_TYPE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'inventory_transactions' 
AND TABLE_SCHEMA = DATABASE()
AND COLUMN_NAME = 'unit_type';

-- Step 6: Show summary of existing records and their unit_types
SELECT 
    unit_type,
    COUNT(*) as record_count,
    'Records updated with unit_type' as status
FROM inventory_transactions 
GROUP BY unit_type;
