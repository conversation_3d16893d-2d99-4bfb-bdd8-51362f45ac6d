-- Migration to standardize sales order status values
-- Convert from various legacy formats to standardized "lowercase_with_underscores"

-- Standardize dispatch_pending status
UPDATE sales_orders 
SET status = 'dispatch_pending' 
WHERE status IN ('Dispatch Pending', 'dispatch pending', 'Dispatch_Pending', 'DISPATCH_PENDING');

-- Standardize pending_approval status
UPDATE sales_orders 
SET status = 'pending_approval' 
WHERE status IN ('Pending Approval', 'pending approval', 'Pending_Approval', 'PENDING_APPROVAL');

-- Standardize dispatched status
UPDATE sales_orders 
SET status = 'dispatched' 
WHERE status IN ('Dispatched', 'DISPATCHED');

-- Standardize completed status
UPDATE sales_orders 
SET status = 'completed' 
WHERE status IN ('Completed', 'COMPLETED');

-- Standardize cancelled status
UPDATE sales_orders 
SET status = 'cancelled' 
WHERE status IN ('Cancelled', 'Canceled', 'CANCELLED', 'CANCELED');

-- Convert legacy "processing" status to appropriate status based on delivery info
-- If has delivery_date/delivery_address -> dispatch_pending (outstation)
-- If no delivery info -> completed (counter sale)
UPDATE sales_orders 
SET status = 'dispatch_pending' 
WHERE status IN ('Processing', 'processing', 'PROCESSING') 
  AND (delivery_date IS NOT NULL OR delivery_address IS NOT NULL);

UPDATE sales_orders 
SET status = 'completed' 
WHERE status IN ('Processing', 'processing', 'PROCESSING') 
  AND delivery_date IS NULL 
  AND delivery_address IS NULL;

-- Convert any remaining legacy statuses
UPDATE sales_orders 
SET status = 'completed' 
WHERE status IN ('Delivered', 'delivered', 'DELIVERED', 'Shipped', 'shipped', 'SHIPPED');

-- Convert draft/pending statuses
UPDATE sales_orders 
SET status = 'pending_approval' 
WHERE status IN ('Draft', 'draft', 'DRAFT', 'Pending', 'pending', 'PENDING');
