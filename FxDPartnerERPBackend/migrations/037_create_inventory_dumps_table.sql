-- Migration: Create inventory_dumps table for tracking dumped inventory items
-- This table will store information about inventory items that have been marked as dump
-- due to damage, expiry, quality issues, etc.

CREATE TABLE inventory_dumps (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    product_id VARCHAR(36) NOT NULL,
    sku_id VARCHAR(36) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    sku_code VARCHAR(100) NOT NULL,
    dumped_quantity INT NOT NULL,
    dumped_weight DECIMAL(10,3) DEFAULT 0,
    reason ENUM('damaged', 'expired', 'quality_issue', 'contaminated', 'other') NOT NULL,
    reason_notes TEXT,
    dumped_by VARCHAR(36) NOT NULL,
    dumped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOR<PERSON><PERSON><PERSON> KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (sku_id) REFERENCES skus(id) ON DELETE CASCADE,
    FOREIGN KEY (dumped_by) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_inventory_dumps_organization_id (organization_id),
    INDEX idx_inventory_dumps_product_sku (product_id, sku_id),
    INDEX idx_inventory_dumps_dumped_at (dumped_at),
    INDEX idx_inventory_dumps_reason (reason),
    INDEX idx_inventory_dumps_dumped_by (dumped_by)
);
