-- Migration to change record_date from DATE to DATETIME to support both date and time
-- This allows the purchase record to store precise date and time information

ALTER TABLE purchase_records 
MODIFY COLUMN record_date DATETIME NOT NULL;

-- Update the index to reflect the new data type
DROP INDEX idx_purchase_records_record_date ON purchase_records;
CREATE INDEX idx_purchase_records_record_date ON purchase_records(record_date);
