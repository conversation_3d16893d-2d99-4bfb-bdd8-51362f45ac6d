-- Create Pages table to define all available pages in the system
CREATE TABLE IF NOT EXISTS pages (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL UNIQUE,
    display_name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    route_path VARCHAR(255),
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_pages_category (category),
    INDEX idx_pages_is_active (is_active),
    INDEX idx_pages_sort_order (sort_order)
);

-- Insert default pages based on the system structure
INSERT IGNORE INTO pages (name, display_name, category, route_path, icon, sort_order, is_active) VALUES
-- Dashboard
('dashboard', 'Dashboard', 'MAIN', '/dashboard', 'home', 1, TRUE),

-- Procurement
('vehicle_arrival', 'Vehicle Arrival', 'PROCUREMENT', '/procurement/vehicle-arrival', 'truck', 10, TRUE),
('record_purchase', 'Record Purchase', 'PROCUREMENT', '/procurement/record-purchase', 'clipboard-list', 11, TRUE),

-- Inventory
('inventory', 'Inventory', 'INVENTORY', '/inventory', 'package', 20, TRUE),

-- Sales
('sales', 'Sales', 'SALES', '/sales', 'shopping-cart', 30, TRUE),
('pending_approvals', 'Pending Approvals', 'SALES', '/sales/pending-approvals', 'clock', 31, TRUE),
('dispatch', 'Dispatch', 'SALES', '/sales/dispatch', 'truck', 32, TRUE),

-- Financials
('ledger', 'Ledger', 'FINANCIALS', '/finance/ledger', 'book-open', 40, TRUE),
('payments', 'Payments', 'FINANCIALS', '/finance/payments', 'credit-card', 41, TRUE),

-- Partners
('suppliers', 'Suppliers', 'PARTNERS', '/partners/suppliers', 'users', 50, TRUE),
('customers', 'Customers', 'PARTNERS', '/partners/customers', 'user', 51, TRUE),

-- System
('settings', 'Settings', 'SYSTEM', '/settings', 'settings', 60, TRUE);
