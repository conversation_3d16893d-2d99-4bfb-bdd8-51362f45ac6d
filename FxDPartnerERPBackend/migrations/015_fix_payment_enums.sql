-- Migration 015: Fix Payment Enums for Frontend-Backend Compatibility
-- This migration fixes the payment type and status enums to support both frontend and backend expectations

-- Add 'made' as an alias for 'paid' in payment type enum
ALTER TABLE payments MODIFY COLUMN type ENUM('received', 'paid', 'made', 'expense') NOT NULL;

-- Add 'completed' to status enum to match frontend expectations
ALTER TABLE payments MODIFY COLUMN status ENUM('pending', 'confirmed', 'failed', 'completed') NOT NULL DEFAULT 'pending';

-- Update any existing 'made' payments to 'paid' for internal consistency (if any exist)
UPDATE payments SET type = 'paid' WHERE type = 'made';

-- Update any existing 'completed' status to 'confirmed' for consistency (if any exist)
-- But we'll keep 'completed' as a valid option going forward
UPDATE payments SET status = 'confirmed' WHERE status = 'completed';
