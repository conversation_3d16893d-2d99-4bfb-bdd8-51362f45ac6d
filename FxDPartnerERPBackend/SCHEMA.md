# Database Schema Documentation

This document provides a comprehensive overview of the FxD Partner database schema, including table structures, relationships, and business logic.

## Table of Contents
- [Overview](#overview)
- [Multi-Tenant Architecture](#multi-tenant-architecture)
- [Core Tables](#core-tables)
- [Business Entity Tables](#business-entity-tables)
- [Operational Tables](#operational-tables)
- [Financial Tables](#financial-tables)
- [Permission System Tables](#permission-system-tables)
- [Junction Tables](#junction-tables)
- [Indexes and Performance](#indexes-and-performance)
- [Data Types and Constraints](#data-types-and-constraints)
- [Migration History](#migration-history)

## Overview

The FxD Partner database is designed with a multi-tenant architecture supporting complete data isolation between organizations. The schema includes 20+ tables covering all aspects of ERP functionality from user management to financial transactions.

### Key Design Principles
- **Multi-tenant**: Every business table includes `organization_id` for data isolation
- **Audit Trail**: All tables include `created_at` and `updated_at` timestamps
- **Referential Integrity**: Comprehensive foreign key constraints
- **Flexible Permissions**: JSON-based role and permission system
- **Scalable Design**: Proper indexing and normalized structure

## Multi-Tenant Architecture

```mermaid
graph TB
    subgraph "Organization A"
        A1[Users A] --> A2[Customers A]
        A1 --> A3[Products A]
        A1 --> A4[Sales Orders A]
        A1 --> A5[Inventory A]
    end
    
    subgraph "Organization B"
        B1[Users B] --> B2[Customers B]
        B1 --> B3[Products B]
        B1 --> B4[Sales Orders B]
        B1 --> B5[Inventory B]
    end
    
    subgraph "Shared Infrastructure"
        C1[Organizations Table]
        C2[System Roles]
        C3[Pages/Permissions]
    end
    
    C1 --> A1
    C1 --> B1
```

Every business table includes:
```sql
organization_id VARCHAR(36) NOT NULL,
FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
```

## Core Tables

### organizations
Central table for multi-tenant architecture.

```sql
CREATE TABLE organizations (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    address TEXT,
    gst_number VARCHAR(15),
    pan_number VARCHAR(10),
    status ENUM('active', 'inactive') DEFAULT 'active',
    subscription_plan VARCHAR(50) DEFAULT 'basic',
    subscription_expires_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**Key Features:**
- Unique organization codes for identification
- Subscription management support
- Tax registration details (GST, PAN)
- Soft delete via status field

### users
User accounts with organization relationships.

```sql
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('super_admin', 'admin', 'manager', 'user') DEFAULT 'user',
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    last_login_at TIMESTAMP NULL,
    email_verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);
```

**Key Features:**
- Bcrypt password hashing
- Email verification tracking
- Login activity tracking
- Hierarchical role system

## Business Entity Tables

### products
Product catalog with categories.

```sql
CREATE TABLE products (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL DEFAULT 'general',
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);
```

### skus
Stock Keeping Units with product variants.

```sql
CREATE TABLE skus (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    product_id VARCHAR(36) NOT NULL,
    code VARCHAR(100) NOT NULL,
    unit_type ENUM('box', 'loose') DEFAULT 'box',
    unit_weight DECIMAL(10,3),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE KEY unique_sku_code_per_org (organization_id, code)
);
```

**Key Features:**
- Hierarchical product-SKU relationship
- Unit type support (box/loose)
- Weight tracking
- Unique SKU codes per organization

### customers
Customer relationship management.

```sql
CREATE TABLE customers (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    customer_type ENUM('individual', 'business') DEFAULT 'business',
    contact VARCHAR(20) NOT NULL,
    email VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    delivery_addresses JSON,
    gst_number VARCHAR(15),
    pan_number VARCHAR(10),
    credit_limit DECIMAL(15,2) DEFAULT 0.00,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    payment_terms INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);
```

**Key Features:**
- Individual vs business customer types
- Multiple delivery addresses (JSON)
- Credit management
- Tax registration details

### suppliers
Supplier relationship management.

```sql
CREATE TABLE suppliers (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    company_name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    gst_number VARCHAR(15),
    pan_number VARCHAR(10),
    bank_name VARCHAR(255),
    account_number VARCHAR(50),
    ifsc_code VARCHAR(11),
    payment_terms INT DEFAULT 0,
    credit_limit DECIMAL(15,2) DEFAULT 0.00,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    products JSON,
    status ENUM('active', 'inactive') DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);
```

**Key Features:**
- Banking details for payments
- Product catalog (JSON)
- Credit and payment terms
- Contact person tracking

## Operational Tables

### vehicle_arrivals
Incoming vehicle tracking for procurement.

```sql
CREATE TABLE vehicle_arrivals (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    vehicle_number VARCHAR(50),
    supplier VARCHAR(255) NOT NULL,
    driver_name VARCHAR(255),
    driver_contact VARCHAR(20),
    arrival_time TIMESTAMP NOT NULL,
    source_location VARCHAR(255),
    status ENUM('pending', 'in_progress', 'completed', 'cancelled', 'po_created') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);
```

### purchase_records
Purchase order management.

```sql
CREATE TABLE purchase_records (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    vehicle_arrival_id VARCHAR(36),
    supplier_id VARCHAR(36),
    record_number VARCHAR(100) NOT NULL,
    supplier VARCHAR(255) NOT NULL,
    record_date DATE NOT NULL,
    arrival_timestamp TIMESTAMP NOT NULL,
    pricing_model ENUM('market_price', 'fixed_price', 'commission_based') DEFAULT 'market_price',
    default_commission DECIMAL(5,2),
    payment_terms INT,
    items_subtotal DECIMAL(15,2) DEFAULT 0.00,
    additional_costs_total DECIMAL(15,2) DEFAULT 0.00,
    total_amount DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('draft', 'confirmed', 'completed', 'cancelled', 'closed') DEFAULT 'draft',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_arrival_id) REFERENCES vehicle_arrivals(id) ON DELETE SET NULL,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
    UNIQUE KEY unique_record_number_per_org (organization_id, record_number)
);
```

**Key Features:**
- Multiple pricing models
- Commission-based pricing support
- Additional costs tracking
- Automatic total calculations

### sales_orders
Sales order processing.

```sql
CREATE TABLE sales_orders (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    order_number VARCHAR(100) NOT NULL,
    customer_id VARCHAR(36) NOT NULL,
    order_date DATE DEFAULT (CURRENT_DATE),
    delivery_date DATE,
    delivery_address TEXT,
    payment_terms INT,
    payment_mode ENUM('cash', 'credit', 'online', 'cheque', 'upi') DEFAULT 'cash',
    payment_status ENUM('pending', 'partial', 'paid', 'overdue', 'unpaid') DEFAULT 'pending',
    subtotal DECIMAL(15,2),
    tax_amount DECIMAL(15,2),
    discount_amount DECIMAL(15,2),
    total_amount DECIMAL(15,2),
    status ENUM('draft', 'confirmed', 'dispatched', 'delivered', 'cancelled', 'shipped') DEFAULT 'draft',
    notes TEXT,
    vehicle_number VARCHAR(50),
    driver_name VARCHAR(255),
    driver_contact VARCHAR(20),
    delivery_location_confirmed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_order_number_per_org (organization_id, order_number)
);
```

**Key Features:**
- Complete order lifecycle tracking
- Multiple payment modes including UPI
- Delivery tracking
- Tax and discount calculations

## Financial Tables

### payments
Financial transaction management.

```sql
CREATE TABLE payments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    type ENUM('received', 'paid') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    payment_date DATE DEFAULT (CURRENT_DATE),
    party_id VARCHAR(36),
    party_type ENUM('customer', 'supplier'),
    party_name VARCHAR(255),
    reference_id VARCHAR(36),
    reference_type ENUM('sales_order', 'purchase_record'),
    reference_number VARCHAR(255),
    mode ENUM('cash', 'online', 'cheque', 'bank_transfer', 'upi') DEFAULT 'cash',
    status ENUM('pending', 'confirmed', 'failed') DEFAULT 'pending',
    expense_type ENUM('operational', 'administrative', 'other'),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);
```

**Key Features:**
- Dual-purpose (received/paid)
- Reference linking to orders/records
- Multiple payment modes
- Expense categorization

### current_inventory
Real-time inventory tracking.

```sql
CREATE TABLE current_inventory (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    product_id VARCHAR(36) NOT NULL,
    sku_id VARCHAR(36) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    sku_code VARCHAR(100) NOT NULL,
    category VARCHAR(100) NOT NULL,
    unit_type ENUM('box', 'loose') NOT NULL,
    available_quantity INT DEFAULT 0,
    total_weight DECIMAL(10,3) DEFAULT 0,
    last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (sku_id) REFERENCES skus(id) ON DELETE CASCADE,
    UNIQUE KEY unique_inventory_per_org (organization_id, product_id, sku_id)
);
```

### negative_inventory
Stock shortage tracking.

```sql
CREATE TABLE negative_inventory (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    product_id VARCHAR(36) NOT NULL,
    sku_id VARCHAR(36) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    sku_code VARCHAR(100) NOT NULL,
    category VARCHAR(100) NOT NULL,
    unit_type ENUM('box', 'loose') NOT NULL,
    negative_quantity INT NOT NULL,
    negative_weight DECIMAL(10,2) NOT NULL,
    reference_type VARCHAR(100) NOT NULL,
    reference_id VARCHAR(36) NOT NULL,
    notes TEXT,
    status ENUM('pending', 'resolved') DEFAULT 'pending',
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (sku_id) REFERENCES skus(id) ON DELETE CASCADE
);
```

## Permission System Tables

### roles
Role definitions with JSON permissions.

```sql
CREATE TABLE roles (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    permissions JSON NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    is_system_role BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    UNIQUE KEY unique_role_name_per_org (organization_id, name)
);
```

**Permission Examples:**
```json
[
  "user:create",
  "user:read",
  "user:update",
  "user:delete",
  "customer:create",
  "inventory:adjust",
  "reports:export"
]
```

### user_details
Extended user profile information.

```sql
CREATE TABLE user_details (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    organization_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    alternate_phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(100),
    employee_id VARCHAR(50),
    department VARCHAR(100),
    designation VARCHAR(100),
    joining_date DATE,
    salary DECIMAL(10,2),
    bank_account_number VARCHAR(50),
    bank_name VARCHAR(255),
    bank_ifsc_code VARCHAR(11),
    pan_number VARCHAR(10),
    aadhar_number VARCHAR(12),
    profile_picture_url TEXT,
    preferences JSON,
    settings JSON,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_details_per_org (organization_id, user_id)
);
```

## Junction Tables

### user_organizations
Many-to-many user-organization relationships.

```sql
CREATE TABLE user_organizations (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    organization_id VARCHAR(36) NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_organization (user_id, organization_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);
```

### user_roles
User role assignments per organization.

```sql
CREATE TABLE user_roles (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    role_id VARCHAR(36) NOT NULL,
    organization_id VARCHAR(36) NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    status ENUM('active', 'inactive') DEFAULT 'active',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_role_org (user_id, role_id, organization_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);
```

## Indexes and Performance

### Primary Indexes
All tables have UUID primary keys with proper indexing:
```sql
INDEX idx_table_name_organization_id (organization_id)
INDEX idx_table_name_status (status)
INDEX idx_table_name_created_at (created_at)
```

### Business Logic Indexes
```sql
-- Customer and Supplier lookups
INDEX idx_customers_name (name)
INDEX idx_suppliers_company_name (company_name)

-- Product and SKU searches
INDEX idx_products_category (category)
INDEX idx_skus_code (code)

-- Order and Record searches
INDEX idx_sales_orders_order_number (order_number)
INDEX idx_purchase_records_record_number (record_number)

-- Date-based queries
INDEX idx_sales_orders_order_date (order_date)
INDEX idx_payments_payment_date (payment_date)

-- Status-based filtering
INDEX idx_vehicle_arrivals_status (status)
INDEX idx_purchase_records_status (status)
INDEX idx_sales_orders_status (status)
```

### Composite Indexes
```sql
-- Multi-column searches
INDEX idx_inventory_product_sku (product_id, sku_id)
INDEX idx_payments_party_type_id (party_type, party_id)
INDEX idx_user_roles_user_org (user_id, organization_id)
```

## Data Types and Constraints

### UUID Fields
All primary keys use VARCHAR(36) to store UUID values:
```sql
id VARCHAR(36) PRIMARY KEY DEFAULT (UUID())
```

### Decimal Precision
Financial amounts use DECIMAL(15,2) for accuracy:
```sql
total_amount DECIMAL(15,2) DEFAULT 0.00
```

### JSON Fields
Flexible data storage using JSON:
```sql
permissions JSON NOT NULL
delivery_addresses JSON
preferences JSON
```

### ENUM Constraints
Controlled vocabulary using ENUM:
```sql
status ENUM('active', 'inactive', 'pending') DEFAULT 'active'
payment_mode ENUM('cash', 'credit', 'online', 'cheque', 'upi')
```

### Timestamp Fields
Automatic timestamp management:
```sql
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
```

## Migration History

### Migration Timeline
1. **001-003**: Core schema foundation (organizations, users, products, business tables)
2. **004-012**: Status enum refinements and bug fixes
3. **013**: Role-based permission system introduction
4. **014-015**: Payment system enhancements (UPI support)
5. **016-017**: Page-based permission system (deprecated)
6. **018**: User-organization and user-role junction tables
7. **019-023**: Permission system refinements and cleanup

### Key Schema Evolution
- **Multi-tenancy**: Added organization_id to all business tables
- **Permissions**: Evolved from simple roles to JSON-based permissions
- **Payment Modes**: Extended to include UPI and other modern payment methods
- **Status Enums**: Standardized across all operational tables
- **Junction Tables**: Implemented many-to-many relationships for users

### Current Schema Version
The schema is at version 23 with the latest migration being:
```
023_drop_assigned_by_foreign_key.sql
```

This represents a mature, production-ready schema supporting all ERP functionality with proper data isolation, referential integrity, and performance optimization.

---

**Related Documentation**:
- [Backend README](./README.md)
- [Frontend README](../FxDPartnerERP/README.md)
- [API Documentation](./API.md)
- [Migration Files](./migrations/)
