import fs from 'fs';
import path from 'path';
import { executeQuery } from '../config/database';

export interface MigrationFile {
  filename: string;
  version: string;
  sql: string;
}

export const runMigrations = async (): Promise<void> => {
  try {
    // Create migrations table if it doesn't exist
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        version VARCHAR(255) UNIQUE NOT NULL,
        filename VARCHAR(255) NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Get list of executed migrations
    const executedMigrations = await executeQuery(
      'SELECT version FROM migrations ORDER BY version'
    );
    const executedVersions = new Set(
      executedMigrations.map((row: any) => row.version)
    );

    // Read migration files
    const migrationsDir = path.join(__dirname, '../../migrations');
    const migrationFiles = fs
      .readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();

    console.log(`Found ${migrationFiles.length} migration files`);

    // Execute pending migrations
    for (const filename of migrationFiles) {
      const version = filename.replace('.sql', '');
      
      if (executedVersions.has(version)) {
        console.log(`⏭️  Skipping migration ${version} (already executed)`);
        continue;
      }

      console.log(`🔄 Executing migration ${version}...`);
      
      const filePath = path.join(migrationsDir, filename);
      const sql = fs.readFileSync(filePath, 'utf8');
      
      // Split SQL by semicolons and execute each statement
      const statements = sql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);

      for (const statement of statements) {
        await executeQuery(statement);
      }

      // Record migration as executed
      await executeQuery(
        'INSERT INTO migrations (version, filename) VALUES (?, ?)',
        [version, filename]
      );

      console.log(`✅ Migration ${version} completed`);
    }

    console.log('🎉 All migrations completed successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
};

export const rollbackMigration = async (version: string): Promise<void> => {
  try {
    // Remove migration record
    await executeQuery(
      'DELETE FROM migrations WHERE version = ?',
      [version]
    );
    
    console.log(`✅ Rolled back migration ${version}`);
    console.log('⚠️  Note: You need to manually undo the database changes');
  } catch (error) {
    console.error('❌ Rollback failed:', error);
    throw error;
  }
};

export const getMigrationStatus = async (): Promise<any[]> => {
  try {
    const migrations = await executeQuery(
      'SELECT * FROM migrations ORDER BY executed_at DESC'
    );
    return migrations;
  } catch (error) {
    console.error('❌ Failed to get migration status:', error);
    throw error;
  }
};
