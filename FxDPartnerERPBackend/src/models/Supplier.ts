import {
  Table,
  Column,
  Model,
  <PERSON>Type,
  <PERSON><PERSON>ey,
  <PERSON>fault,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Organization } from './Organization';

@Table({
  tableName: 'suppliers',
  timestamps: true,
  underscored: true,
})
export class Supplier extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @Column(DataType.STRING)
  company_name!: string;

  @Column(DataType.STRING)
  contact_person?: string;

  @Column(DataType.STRING)
  phone?: string;

  @Column(DataType.STRING)
  email?: string;

  @Column(DataType.TEXT)
  address?: string;

  @Column(DataType.STRING)
  gst_number?: string;

  @Column(DataType.STRING)
  pan_number?: string;

  @Column(DataType.STRING)
  bank_name?: string;

  @Column(DataType.STRING)
  account_number?: string;

  @Column(DataType.STRING)
  ifsc_code?: string;

  @Default(30)
  @Column(DataType.INTEGER)
  payment_terms!: number;

  @Default(0)
  @Column(DataType.DECIMAL(10, 2))
  credit_limit!: number;

  @Default(0)
  @Column(DataType.DECIMAL(10, 2))
  current_balance!: number;

  @Default(0)
  @Column(DataType.DECIMAL(15, 2))
  opening_balance!: number;

  @Column(DataType.ENUM('debit', 'credit'))
  opening_balance_type?: string;

  @Column(DataType.JSON)
  products?: string[];

  @Default('active')
  @Column(DataType.ENUM('active', 'inactive'))
  status!: string;

  @Column(DataType.TEXT)
  notes?: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;
}

// Export interfaces for backward compatibility
export interface CreateSupplierData {
  organization_id: string;
  company_name: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  address?: string;
  gst_number?: string;
  pan_number?: string;
  bank_name?: string;
  account_number?: string;
  ifsc_code?: string;
  payment_terms?: number;
  credit_limit?: number;
  opening_balance?: number;
  opening_balance_type?: 'debit' | 'credit';
  products?: string[];
  notes?: string;
}

export interface UpdateSupplierData {
  company_name?: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  address?: string;
  gst_number?: string;
  pan_number?: string;
  bank_name?: string;
  account_number?: string;
  ifsc_code?: string;
  payment_terms?: number;
  credit_limit?: number;
  current_balance?: number;
  opening_balance?: number;
  opening_balance_type?: 'debit' | 'credit';
  products?: string[];
  status?: 'active' | 'inactive';
  notes?: string;
}
