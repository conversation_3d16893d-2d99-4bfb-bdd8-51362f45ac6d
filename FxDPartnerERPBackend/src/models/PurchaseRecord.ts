import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Organization } from './Organization';
import { Supplier } from './Supplier';
import { VehicleArrival } from './VehicleArrival';

@Table({
  tableName: 'purchase_records',
  timestamps: true,
  underscored: true,
})
export class PurchaseRecord extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @Column(DataType.STRING)
  record_number!: string;

  @ForeignKey(() => Supplier)
  @Column(DataType.UUID)
  supplier_id?: string;

  @Column(DataType.STRING)
  supplier!: string;

  @ForeignKey(() => VehicleArrival)
  @Column(DataType.UUID)
  vehicle_arrival_id?: string;

  @Column(DataType.DATE)
  record_date!: Date;

  @Column(DataType.DATE)
  arrival_timestamp?: Date;

  @Default('market_price')
  @Column(DataType.ENUM('market_price', 'fixed_price', 'commission_based'))
  pricing_model?: string;

  @Column(DataType.DECIMAL(5, 2))
  default_commission?: number;

  @Column(DataType.INTEGER)
  payment_terms?: number;

  @Column(DataType.DECIMAL(15, 2))
  items_subtotal?: number;

  @Column(DataType.DECIMAL(15, 2))
  additional_costs_total?: number;

  @Column(DataType.DECIMAL(15, 2))
  total_amount?: number;

  @Default('draft')
  @Column(DataType.ENUM('draft', 'confirmed', 'completed', 'cancelled', 'partial_closure', 'full_closure'))
  status!: string;

  @Column(DataType.TEXT)
  notes?: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => Supplier)
  supplierDetails!: Supplier;

  @BelongsTo(() => VehicleArrival)
  vehicle_arrival!: VehicleArrival;

  @HasMany(() => PurchaseRecordItem)
  items!: PurchaseRecordItem[];

  @HasMany(() => PurchaseRecordCost)
  costs!: PurchaseRecordCost[];
}

@Table({
  tableName: 'purchase_record_items',
  timestamps: true,
  underscored: true,
})
export class PurchaseRecordItem extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => PurchaseRecord)
  @Column(DataType.UUID)
  purchase_record_id!: string;

  @Column(DataType.UUID)
  product_id!: string;

  @Column(DataType.UUID)
  sku_id!: string;

  @Column(DataType.STRING)
  product_name!: string;

  @Column(DataType.STRING)
  sku_code!: string;

  @Column(DataType.STRING)
  category!: string;

  @Column(DataType.INTEGER)
  quantity!: number;

  @Column(DataType.ENUM('box', 'loose'))
  unit_type!: string;

  @Column(DataType.DECIMAL(10, 3))
  total_weight!: number;

  @Column(DataType.DECIMAL(10, 2))
  market_price?: number;

  @Column(DataType.DECIMAL(5, 2))
  commission?: number;

  @Column(DataType.DECIMAL(10, 2))
  unit_price!: number;

  @Column(DataType.DECIMAL(15, 2))
  total!: number;

  @CreatedAt
  created_at!: Date;

  // Associations
  @BelongsTo(() => PurchaseRecord)
  purchase_record!: PurchaseRecord;
}

@Table({
  tableName: 'purchase_record_costs',
  timestamps: false,
  underscored: true,
})
export class PurchaseRecordCost extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => PurchaseRecord)
  @Column(DataType.UUID)
  purchase_record_id!: string;

  @Column(DataType.STRING)
  name!: string;

  @Column(DataType.DECIMAL(15, 2))
  amount!: number;

  @Column(DataType.ENUM('fixed', 'percentage', 'per_box'))
  type!: string;

  @Column(DataType.DECIMAL(15, 2))
  calculated_amount?: number;

  @CreatedAt
  created_at!: Date;

  // Associations
  @BelongsTo(() => PurchaseRecord)
  purchase_record!: PurchaseRecord;
}

// Export interfaces for backward compatibility
export interface CreatePurchaseRecordData {
  organization_id: string;
  record_number: string;
  supplier_id?: string;
  supplier?: string;
  vehicle_arrival_id?: string;
  record_date: Date;
  arrival_timestamp?: Date;
  pricing_model?: 'market_price' | 'fixed_price' | 'commission_based' | 'fixed' | 'commission';
  default_commission?: number;
  payment_terms?: number;
  items_subtotal?: number;
  additional_costs_total?: number;
  total_amount?: number;
  status?: 'draft' | 'confirmed' | 'completed' | 'cancelled' | 'partial_closure' | 'full_closure';
  closure_date?: Date;
  closure_notes?: string;
  notes?: string;
}

export interface UpdatePurchaseRecordData {
  record_number?: string;
  supplier_id?: string;
  record_date?: Date;
  items_subtotal?: number;
  additional_costs_total?: number;
  total_amount?: number;
  status?: 'partial_closure' | 'full_closure' | 'cancelled';
  closure_date?: Date;
  closure_notes?: string;
  notes?: string;
}

export interface CreatePurchaseRecordItemData {
  purchase_record_id: string;
  product_id: string;
  sku_id: string;
  product_name: string;
  sku_code: string;
  category?: string;
  quantity: number;
  unit_type: 'box' | 'loose';
  unit_weight?: number;
  total_weight: number;
  market_price?: number;
  commission?: number;
  unit_price: number;
  total: number;
}

export interface CreatePurchaseRecordCostData {
  purchase_record_id: string;
  name: string;
  amount: number;
  type: 'fixed' | 'percentage' | 'per_box';
  calculated_amount?: number;
}
