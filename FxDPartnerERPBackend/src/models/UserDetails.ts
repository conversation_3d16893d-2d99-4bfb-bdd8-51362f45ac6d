import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON><PERSON>ey,
  <PERSON><PERSON>ult,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Organization } from './Organization';
import { User } from './User';

@Table({
  tableName: 'user_details',
  timestamps: true,
  underscored: true,
})
export class UserDetails extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  user_id!: string; // Reference to users table

  @Column(DataType.STRING)
  first_name!: string;

  @Column(DataType.STRING)
  phone?: string;

  @Column(DataType.STRING)
  alternate_phone?: string;

  @Column(DataType.TEXT)
  address?: string;

  @Column(DataType.STRING)
  city?: string;

  @Column(DataType.STRING)
  state?: string;

  @Column(DataType.STRING)
  country?: string;

  @Column(DataType.STRING)
  postal_code?: string;

  @Column(DataType.DATE)
  date_of_birth?: Date;

  @Column(DataType.ENUM('male', 'female', 'other'))
  gender?: string;

  @Column(DataType.STRING)
  emergency_contact_name?: string;

  @Column(DataType.STRING)
  emergency_contact_phone?: string;

  @Column(DataType.STRING)
  emergency_contact_relationship?: string;

  @Column(DataType.STRING)
  employee_id?: string;

  @Column(DataType.STRING)
  department?: string;

  @Column(DataType.STRING)
  designation?: string;

  @Column(DataType.DATE)
  joining_date?: Date;

  @Column(DataType.DECIMAL(10, 2))
  salary?: number;

  @Column(DataType.STRING)
  bank_account_number?: string;

  @Column(DataType.STRING)
  bank_name?: string;

  @Column(DataType.STRING)
  bank_ifsc_code?: string;

  @Column(DataType.STRING)
  pan_number?: string;

  @Column(DataType.STRING)
  aadhar_number?: string;

  @Column(DataType.STRING)
  profile_picture_url?: string;

  @Column(DataType.JSON)
  preferences?: Record<string, any>; // User preferences like theme, language, etc.

  @Column(DataType.JSON)
  settings?: Record<string, any>; // User-specific settings

  @Default('active')
  @Column(DataType.ENUM('active', 'inactive', 'suspended'))
  status!: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  // Note: User association will be added later to avoid circular imports
}

// Export interfaces for type safety
export interface CreateUserDetailsData {
  organization_id: string;
  user_id: string;
  first_name: string;
  phone?: string;
  alternate_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  date_of_birth?: Date;
  gender?: 'male' | 'female' | 'other';
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  employee_id?: string;
  department?: string;
  designation?: string;
  joining_date?: Date;
  salary?: number;
  bank_account_number?: string;
  bank_name?: string;
  bank_ifsc_code?: string;
  pan_number?: string;
  aadhar_number?: string;
  profile_picture_url?: string;
  preferences?: Record<string, any>;
  settings?: Record<string, any>;
  status?: 'active' | 'inactive' | 'suspended';
}

export interface UpdateUserDetailsData {
  first_name?: string;
  phone?: string;
  alternate_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  date_of_birth?: Date;
  gender?: 'male' | 'female' | 'other';
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  employee_id?: string;
  department?: string;
  designation?: string;
  joining_date?: Date;
  salary?: number;
  bank_account_number?: string;
  bank_name?: string;
  bank_ifsc_code?: string;
  pan_number?: string;
  aadhar_number?: string;
  profile_picture_url?: string;
  preferences?: Record<string, any>;
  settings?: Record<string, any>;
  status?: 'active' | 'inactive' | 'suspended';
}
