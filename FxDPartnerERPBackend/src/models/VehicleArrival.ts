import {
  Table,
  Column,
  Model,
  DataType,
  Primary<PERSON>ey,
  <PERSON><PERSON>ult,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Organization } from './Organization';
import { Product, SKU } from './Product';
import { PurchaseRecord } from './PurchaseRecord';
import { Document, EntityType } from './Document';

@Table({
  tableName: 'vehicle_arrivals',
  timestamps: true,
  underscored: true,
})
export class VehicleArrival extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @Column(DataType.STRING)
  vehicle_number?: string;

  @Column(DataType.STRING)
  supplier!: string;

  @Column(DataType.STRING)
  reference_number?: string;

  @Column(DataType.STRING)
  driver_name?: string;

  @Column(DataType.STRING)
  driver_contact?: string;

  @Column(DataType.DATE)
  arrival_time!: Date;

  @Column(DataType.STRING)
  source_location?: string;

  @Default('pending')
  @Column(DataType.ENUM('pending', 'in_progress', 'completed', 'cancelled', 'po_created'))
  status!: string;

  @Column(DataType.TEXT)
  notes?: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @HasMany(() => VehicleArrivalItem)
  items!: VehicleArrivalItem[];

  @HasMany(() => PurchaseRecord)
  purchase_records!: PurchaseRecord[];

  // Helper method to get documents/attachments
  async getDocuments(): Promise<Document[]> {
    return Document.findByEntity(EntityType.VEHICLE_ARRIVAL, this.id, this.organization_id);
  }

  // Helper method to get documents count
  async getDocumentsCount(): Promise<number> {
    const documents = await this.getDocuments();
    return documents.length;
  }
}

@Table({
  tableName: 'vehicle_arrival_items',
  timestamps: true,
  underscored: true,
})
export class VehicleArrivalItem extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => VehicleArrival)
  @Column(DataType.UUID)
  vehicle_arrival_id!: string;

  @ForeignKey(() => Product)
  @Column(DataType.UUID)
  product_id!: string;

  @ForeignKey(() => SKU)
  @Column(DataType.UUID)
  sku_id!: string;

  @Column(DataType.ENUM('box', 'loose'))
  unit_type!: string;

  @Column(DataType.DECIMAL(8, 2))
  unit_weight?: number;

  @Column(DataType.INTEGER)
  quantity!: number;

  @Column(DataType.DECIMAL(10, 2))
  total_weight!: number;

  @Column(DataType.INTEGER)
  final_quantity?: number;

  @Column(DataType.DECIMAL(10, 2))
  final_total_weight?: number;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => VehicleArrival)
  vehicle_arrival!: VehicleArrival;

  @BelongsTo(() => Product)
  product!: Product;

  @BelongsTo(() => SKU)
  sku!: SKU;
}
