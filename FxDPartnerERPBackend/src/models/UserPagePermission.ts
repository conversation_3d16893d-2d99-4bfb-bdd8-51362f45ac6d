import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON>Key,
  <PERSON><PERSON>ult,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './User';
import { Page } from './Page';

@Table({
  tableName: 'user_page_permissions',
  timestamps: true,
  underscored: true,
})
export class UserPagePermission extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  user_id!: string;

  @ForeignKey(() => Page)
  @Column(DataType.UUID)
  page_id!: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  can_view!: boolean;

  @Default(false)
  @Column(DataType.BOOLEAN)
  can_edit!: boolean;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => User)
  user!: User;

  @BelongsTo(() => Page)
  page!: Page;
}

// Export interfaces for type safety
export interface CreateUserPagePermissionData {
  user_id: string;
  page_id: string;
  can_view?: boolean;
  can_edit?: boolean;
}

export interface UpdateUserPagePermissionData {
  can_view?: boolean;
  can_edit?: boolean;
}

export interface UserPagePermissionWithDetails extends UserPagePermission {
  page: Page;
}
