import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './User';
import { Role } from './Role';
import { Organization } from './Organization';

@Table({
  tableName: 'user_roles',
  timestamps: true,
  underscored: true,
})
export class UserRole extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  user_id!: string;

  @ForeignKey(() => Role)
  @Column(DataType.UUID)
  role_id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  is_primary!: boolean;

  @Default('active')
  @Column(DataType.ENUM('active', 'inactive'))
  status!: string;

  @Default(DataType.NOW)
  @Column(DataType.DATE)
  assigned_at!: Date;

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  assigned_by?: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => User, 'user_id')
  user!: User;

  @BelongsTo(() => Role)
  role!: Role;

  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => User, 'assigned_by')
  assigner!: User;
}

// Export interfaces for type safety
export interface CreateUserRoleData {
  user_id: string;
  role_id: string;
  organization_id: string;
  is_primary?: boolean;
  status?: 'active' | 'inactive';
  assigned_at?: Date;
  assigned_by?: string;
}

export interface UpdateUserRoleData {
  is_primary?: boolean;
  status?: 'active' | 'inactive';
  assigned_at?: Date;
  assigned_by?: string;
}
