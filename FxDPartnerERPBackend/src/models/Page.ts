import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  CreatedAt,
  UpdatedAt,
  HasMany,
  Unique,
} from 'sequelize-typescript';
import { UserPagePermission } from './UserPagePermission';

@Table({
  tableName: 'pages',
  timestamps: true,
  underscored: true,
})
export class Page extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @Unique
  @Column(DataType.STRING)
  name!: string;

  @Column(DataType.STRING)
  display_name!: string;

  @Column(DataType.STRING)
  category!: string;

  @Column(DataType.STRING)
  route_path?: string;

  @Column(DataType.STRING)
  icon?: string;

  @Default(0)
  @Column(DataType.INTEGER)
  sort_order!: number;

  @Default(true)
  @Column(DataType.BOOLEAN)
  is_active!: boolean;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @HasMany(() => UserPagePermission)
  user_permissions!: UserPagePermission[];
}

// Export interfaces for type safety
export interface CreatePageData {
  name: string;
  display_name: string;
  category: string;
  route_path?: string;
  icon?: string;
  sort_order?: number;
  is_active?: boolean;
}

export interface UpdatePageData {
  name?: string;
  display_name?: string;
  category?: string;
  route_path?: string;
  icon?: string;
  sort_order?: number;
  is_active?: boolean;
}

// Page categories
export const PAGE_CATEGORIES = {
  MAIN: 'MAIN',
  PROCUREMENT: 'PROCUREMENT',
  INVENTORY: 'INVENTORY',
  SALES: 'SALES',
  FINANCIALS: 'FINANCIALS',
  PARTNERS: 'PARTNERS',
  SYSTEM: 'SYSTEM',
} as const;

export type PageCategory = typeof PAGE_CATEGORIES[keyof typeof PAGE_CATEGORIES];
