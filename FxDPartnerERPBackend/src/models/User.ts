import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON><PERSON>ey,
  <PERSON>fault,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasOne,
  HasMany,
  BelongsToMany,
  Unique,
} from 'sequelize-typescript';
import { Organization } from './Organization';
import { UserDetails } from './UserDetails';
import { UserPagePermission } from './UserPagePermission';
import { UserOrganization } from './UserOrganization';
import { UserRole } from './UserRole';
import { Role } from './Role';

@Table({
  tableName: 'users',
  timestamps: true,
  underscored: true,
})
export class User extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @Unique
  @Column(DataType.STRING)
  email!: string;

  @Column(DataType.STRING)
  password_hash!: string;

  @Column(DataType.STRING)
  first_name!: string;

  @Column(DataType.STRING)
  phone?: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id?: string;

  @Default('pending')
  @Column(DataType.ENUM('active', 'inactive', 'pending'))
  status!: string;

  @Column(DataType.DATE)
  last_login_at?: Date;

  @Column(DataType.DATE)
  email_verified_at?: Date;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @HasOne(() => UserDetails)
  user_details!: UserDetails;

  @HasMany(() => UserPagePermission)
  page_permissions!: UserPagePermission[];

  // Many-to-many relationships through junction tables
  @HasMany(() => UserOrganization)
  user_organizations!: UserOrganization[];

  @HasMany(() => UserRole)
  user_roles!: UserRole[];

  @BelongsToMany(() => Organization, () => UserOrganization)
  organizations!: Organization[];

  @BelongsToMany(() => Role, () => UserRole)
  roles!: Role[];

  // Helper methods to get primary organization and roles
  async getPrimaryOrganization(): Promise<Organization | null> {
    const userOrg = await UserOrganization.findOne({
      where: { 
        user_id: this.id,
        is_primary: true,
        status: 'active'
      },
      include: [Organization]
    });
    return userOrg?.organization || null;
  }

  async getPrimaryRoleInOrganization(organizationId: string): Promise<Role | null> {
    const userRole = await UserRole.findOne({
      where: { 
        user_id: this.id,
        organization_id: organizationId,
        is_primary: true,
        status: 'active'
      },
      include: [Role]
    });
    return userRole?.role || null;
  }

  async getAllRolesInOrganization(organizationId: string): Promise<Role[]> {
    const userRoles = await UserRole.findAll({
      where: { 
        user_id: this.id,
        organization_id: organizationId,
        status: 'active'
      },
      include: [Role]
    });
    return userRoles.map(ur => ur.role);
  }

  async getAllOrganizations(): Promise<Organization[]> {
    const userOrgs = await UserOrganization.findAll({
      where: { 
        user_id: this.id,
        status: 'active'
      },
      include: [Organization]
    });
    return userOrgs.map(uo => uo.organization);
  }
}

// Export interfaces for type safety
export interface CreateUserData {
  email: string;
  password: string;
  first_name: string;
  phone?: string;
  organization_id?: string;
  status?: 'active' | 'inactive' | 'pending';
  organizations?: {
    organization_id: string;
    is_primary?: boolean;
    status?: 'active' | 'inactive' | 'pending';
  }[];
  roles?: {
    role_id: string;
    organization_id: string;
    is_primary?: boolean;
    status?: 'active' | 'inactive';
  }[];
}

export interface UpdateUserData {
  email?: string;
  first_name?: string;
  phone?: string;
  organization_id?: string;
  status?: 'active' | 'inactive' | 'pending';
  last_login_at?: Date;
  email_verified_at?: Date;
  reset_password_token?: string;
  reset_password_expires?: Date;
  email_verification_token?: string;
}

export interface UserWithoutPassword {
  id: string;
  email: string;
  first_name: string;
  phone?: string;
  organization_id?: string;
  status: string;
  last_login_at?: Date;
  email_verified_at?: Date;
  created_at: Date;
  updated_at: Date;
  organizations?: Organization[];
  roles?: Role[];
  user_organizations?: UserOrganization[];
  user_roles?: UserRole[];
}

// New interfaces for user management with multiple organizations and roles
export interface CreateUserWithOrganizationsAndRoles {
  user: CreateUserData;
  organizations: {
    organization_id: string;
    is_primary?: boolean;
    status?: 'active' | 'inactive' | 'pending';
  }[];
  roles: {
    role_id: string;
    organization_id: string;
    is_primary?: boolean;
    status?: 'active' | 'inactive';
    assigned_by?: string;
  }[];
}

export interface UserOrganizationRole {
  user_id: string;
  organization_id: string;
  role_id: string;
  is_primary_org?: boolean;
  is_primary_role?: boolean;
  org_status?: 'active' | 'inactive' | 'pending';
  role_status?: 'active' | 'inactive';
}
