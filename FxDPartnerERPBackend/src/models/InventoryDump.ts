import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  Min,
} from 'sequelize-typescript';
import { Organization } from './Organization';
import { Product } from './Product';
import { SKU } from './Product';
import { User } from './User';

@Table({
  tableName: 'inventory_dumps',
  timestamps: true,
  underscored: true,
})
export class InventoryDump extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => Product)
  @Column(DataType.UUID)
  product_id!: string;

  @ForeignKey(() => SKU)
  @Column(DataType.UUID)
  sku_id!: string;

  @Column(DataType.STRING(255))
  product_name!: string;

  @Column(DataType.STRING(100))
  sku_code!: string;

  @Min(1)
  @Column(DataType.INTEGER)
  dumped_quantity!: number;

  @Default(0)
  @Min(0)
  @Column(DataType.DECIMAL(10, 3))
  dumped_weight!: number;

  @Column(DataType.ENUM('damaged', 'expired', 'quality_issue', 'contaminated', 'other'))
  reason!: 'damaged' | 'expired' | 'quality_issue' | 'contaminated' | 'other';

  @Column(DataType.TEXT)
  reason_notes?: string;

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  dumped_by!: string;

  @Default(DataType.NOW)
  @Column(DataType.DATE)
  dumped_at!: Date;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => Product)
  product!: Product;

  @BelongsTo(() => SKU)
  sku!: SKU;

  @BelongsTo(() => User, 'dumped_by')
  dumper!: User;
}

export default InventoryDump;
