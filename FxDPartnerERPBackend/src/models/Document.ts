import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  Index,
} from 'sequelize-typescript';
import { Op } from 'sequelize';
import { Organization } from './Organization';
import { User } from './User';

export enum EntityType {
  PURCHASE_RECORD = 'purchase_record',
  SALES_ORDER = 'sales_order',
  VEHICLE_ARRIVAL = 'vehicle_arrival',
  PAYMENT = 'payment',
  SUPPLIER = 'supplier',
  CUSTOMER = 'customer',
  INVENTORY = 'inventory',
  GRN_RETURN = 'grn_return'
}

@Table({
  tableName: 'documents',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      name: 'idx_documents_entity',
      fields: ['entity_type', 'entity_id']
    },
    {
      name: 'idx_documents_organization',
      fields: ['organization_id']
    },
    {
      name: 'idx_documents_uploaded_by',
      fields: ['uploaded_by']
    }
  ]
})
export class Document extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: false
  })
  file_name!: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    unique: true
  })
  file_key!: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true
  })
  file_url?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false
  })
  file_size!: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: false
  })
  content_type!: string;

  @Column({
    type: DataType.ENUM(...Object.values(EntityType)),
    allowNull: false
  })
  entity_type!: EntityType;

  @Column({
    type: DataType.STRING(255),
    allowNull: false
  })
  entity_id!: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true
  })
  display_name?: string;

  @ForeignKey(() => Organization)
  @Column({
    type: DataType.UUID,
    allowNull: false
  })
  organization_id!: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false
  })
  uploaded_by!: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => User)
  uploader!: User;

  // Helper methods
  async refreshFileUrl(): Promise<void> {
    const s3Service = (await import('../services/s3Service')).default;
    this.file_url = await s3Service.getFileUrl(this.file_key);
    await this.save();
  }

  static async findByEntity(entityType: EntityType, entityId: string, organizationId?: string): Promise<Document[]> {
    const whereClause: any = {
      entity_type: entityType,
      entity_id: entityId
    };

    if (organizationId) {
      whereClause.organization_id = organizationId;
    }

    return this.findAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'first_name', 'email']
        }
      ]
    });
  }

  static async findByOrganization(organizationId: string, limit?: number): Promise<Document[]> {
    return this.findAll({
      where: { organization_id: organizationId },
      order: [['created_at', 'DESC']],
      limit: limit || 50,
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'first_name', 'email']
        }
      ]
    });
  }

  static async searchByFileName(searchTerm: string, organizationId?: string): Promise<Document[]> {
    const whereClause: any = {
      [Op.or]: [
        { file_name: { [Op.like]: `%${searchTerm}%` } },
        { display_name: { [Op.like]: `%${searchTerm}%` } }
      ]
    };

    if (organizationId) {
      whereClause.organization_id = organizationId;
    }

    return this.findAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit: 20,
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'first_name', 'email']
        }
      ]
    });
  }
}

// Export interfaces for type safety
export interface CreateDocumentData {
  file_name: string;
  file_key: string;
  file_url?: string;
  file_size: number;
  content_type: string;
  entity_type: EntityType;
  entity_id: string;
  display_name?: string;
  organization_id: string;
  uploaded_by: string;
}

export interface UpdateDocumentData {
  file_name?: string;
  file_url?: string;
  display_name?: string;
}

export interface DocumentWithUploader {
  id: string;
  file_name: string;
  file_key: string;
  file_url?: string;
  file_size: number;
  content_type: string;
  entity_type: EntityType;
  entity_id: string;
  display_name?: string;
  organization_id: string;
  uploaded_by: string;
  created_at: Date;
  updated_at: Date;
  uploader: {
    id: string;
    first_name: string;
    email: string;
  };
}

export interface DocumentSearchFilters {
  entity_type?: EntityType;
  entity_id?: string;
  organization_id?: string;
  uploaded_by?: string;
  content_type?: string;
  search_term?: string;
  date_from?: Date;
  date_to?: Date;
}

export interface DocumentStats {
  total_documents: number;
  total_size: number;
  by_entity_type: Record<EntityType, number>;
  by_content_type: Record<string, number>;
  recent_uploads: number; // Last 7 days
}
