import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON>Key,
  <PERSON><PERSON>ult,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './User';
import { Organization } from './Organization';

@Table({
  tableName: 'user_organizations',
  timestamps: true,
  underscored: true,
})
export class UserOrganization extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  user_id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  is_primary!: boolean;

  @Default('active')
  @Column(DataType.ENUM('active', 'inactive', 'pending'))
  status!: string;

  @Default(DataType.NOW)
  @Column(DataType.DATE)
  joined_at!: Date;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => User)
  user!: User;

  @BelongsTo(() => Organization)
  organization!: Organization;
}

// Export interfaces for type safety
export interface CreateUserOrganizationData {
  user_id: string;
  organization_id: string;
  is_primary?: boolean;
  status?: 'active' | 'inactive' | 'pending';
  joined_at?: Date;
}

export interface UpdateUserOrganizationData {
  is_primary?: boolean;
  status?: 'active' | 'inactive' | 'pending';
  joined_at?: Date;
}
