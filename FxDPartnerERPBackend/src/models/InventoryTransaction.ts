import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Organization } from './Organization';
import { Product, SKU } from './Product';
import { User } from './User';

@Table({
  tableName: 'inventory_transactions',
  timestamps: true,
  underscored: true,
})
export class InventoryTransaction extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => Product)
  @Column(DataType.UUID)
  product_id!: string;

  @ForeignKey(() => SKU)
  @Column(DataType.UUID)
  sku_id!: string;

  @Default('box')
  @Column(DataType.ENUM('box', 'loose'))
  unit_type?: string;

  @Column(DataType.ENUM('return_to_inventory', 'sale', 'purchase', 'adjustment', 'vehicle_arrival'))
  transaction_type!: string;

  @Column(DataType.INTEGER)
  quantity_change!: number;

  @Default(0)
  @Column(DataType.DECIMAL(10, 2))
  weight_change!: number;

  @Column(DataType.ENUM('grn_return', 'sales_order', 'purchase_record', 'vehicle_arrival', 'manual_adjustment'))
  reference_type!: string;

  @Column(DataType.UUID)
  reference_id!: string;

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  performed_by?: string;

  @Column(DataType.TEXT)
  reason!: string;

  @Column(DataType.TEXT)
  notes?: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => Product)
  product!: Product;

  @BelongsTo(() => SKU)
  sku!: SKU;

  @BelongsTo(() => User, 'performed_by')
  performer!: User;
}

// Export interfaces for type safety
export interface CreateInventoryTransactionData {
  organization_id: string;
  product_id: string;
  sku_id: string;
  transaction_type: 'return_to_inventory' | 'sale' | 'purchase' | 'adjustment' | 'vehicle_arrival';
  quantity_change: number;
  weight_change?: number;
  reference_type: 'grn_return' | 'sales_order' | 'purchase_record' | 'vehicle_arrival' | 'manual_adjustment';
  reference_id: string;
  performed_by?: string;
  reason: string;
  notes?: string;
  unit_type?: 'box' | 'loose'; // Add unit_type for proper inventory lookup
}

export interface InventoryTransactionFilters {
  organization_id: string;
  product_id?: string;
  sku_id?: string;
  transaction_type?: string;
  reference_type?: string;
  reference_id?: string;
  performed_by?: string;
  date_from?: Date;
  date_to?: Date;
}
