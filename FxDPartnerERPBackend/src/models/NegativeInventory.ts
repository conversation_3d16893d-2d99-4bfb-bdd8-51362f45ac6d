import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON><PERSON>ey,
  <PERSON><PERSON>ult,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Organization } from './Organization';

@Table({
  tableName: 'negative_inventory',
  timestamps: true,
  underscored: true,
})
export class NegativeInventory extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @Column(DataType.UUID)
  product_id!: string;

  @Column(DataType.UUID)
  sku_id!: string;

  @Column(DataType.STRING)
  product_name!: string;

  @Column(DataType.STRING)
  sku_code!: string;

  @Column(DataType.STRING)
  category!: string;

  @Column(DataType.ENUM('box', 'loose'))
  unit_type!: string;

  @Column(DataType.INTEGER)
  negative_quantity!: number;

  @Column(DataType.DECIMAL(10, 2))
  negative_weight!: number;

  @Column(DataType.STRING)
  reference_type!: string; // 'sales_order', 'adjustment', etc.

  @Column(DataType.UUID)
  reference_id!: string;

  @Column(DataType.TEXT)
  notes?: string;

  @Column(DataType.ENUM('pending', 'resolved'))
  status!: string;

  @Column(DataType.DATE)
  resolved_at?: Date;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;
}

// Export interfaces for type safety
export interface CreateNegativeInventoryData {
  organization_id: string;
  product_id: string;
  sku_id: string;
  product_name: string;
  sku_code: string;
  category: string;
  unit_type: 'box' | 'loose';
  negative_quantity: number;
  negative_weight: number;
  reference_type: string;
  reference_id: string;
  notes?: string;
  status?: 'pending' | 'resolved';
}

export interface UpdateNegativeInventoryData {
  negative_quantity?: number;
  negative_weight?: number;
  notes?: string;
  status?: 'pending' | 'resolved';
  resolved_at?: Date;
}
