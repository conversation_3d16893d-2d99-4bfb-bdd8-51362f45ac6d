import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON><PERSON>ey,
  <PERSON><PERSON>ult,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Organization } from './Organization';
import { SalesOrderItem } from './SalesOrder';
import { GRNReturnPDDRequest } from './GRNReturnPDDRequest';

@Table({
  tableName: 'grn_return_pdd_items',
  timestamps: true,
  underscored: true,
})
export class GRNReturnPDDItem extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => GRNReturnPDDRequest)
  @Column(DataType.UUID)
  grn_request_id!: string;

  @ForeignKey(() => SalesOrderItem)
  @Column(DataType.UUID)
  sales_order_item_id!: string;

  @Column(DataType.INTEGER)
  original_quantity!: number;

  @Column(DataType.INTEGER)
  return_quantity!: number;

  @Column(DataType.DECIMAL(5, 2))
  pdd_percentage!: number;

  @Column(DataType.DECIMAL(10, 2))
  pdd_amount!: number;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => GRNReturnPDDRequest)
  grn_request!: GRNReturnPDDRequest;

  @BelongsTo(() => SalesOrderItem)
  sales_order_item!: SalesOrderItem;
}
