import {
  Table,
  Column,
  Model,
  DataType,
  Primary<PERSON>ey,
  Default,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Organization } from './Organization';
import { User } from './User';

@Table({
  tableName: 'product_merge_history',
  timestamps: true,
  underscored: true,
})
export class ProductMergeHistory extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @Column(DataType.UUID)
  source_product_id!: string;

  @Column(DataType.STRING)
  source_product_name!: string;

  @Column(DataType.UUID)
  target_product_id!: string;

  @Column(DataType.STRING)
  target_product_name!: string;

  @Column(DataType.STRING)
  merge_reason!: string;

  @Column(DataType.JSON)
  merge_details?: any;

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  merged_by!: string;

  @Column(DataType.DATE)
  merged_at!: Date;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => User)
  merger!: User;
}
