import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Organization } from './Organization';
import { SalesOrder, SalesOrderItem } from './SalesOrder';
import { User } from './User';
import { GRNReturnPDDRequest } from './GRNReturnPDDRequest';

@Table({
  tableName: 'sales_order_discounts',
  timestamps: true,
  underscored: true,
})
export class SalesOrderDiscount extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => SalesOrder)
  @Column(DataType.UUID)
  sales_order_id!: string;

  @ForeignKey(() => SalesOrderItem)
  @Column(DataType.UUID)
  sales_order_item_id?: string; // Optional - for item-level discounts

  @Column(DataType.ENUM('manual', 'pdd', 'promotional', 'customer_credit', 'bulk_discount', 'damaged_goods'))
  discount_type!: string;

  @Column(DataType.DECIMAL(10, 2))
  discount_amount!: number;

  @Column(DataType.DECIMAL(5, 2))
  discount_percentage?: number;

  @Column(DataType.TEXT)
  reason?: string;

  @Column(DataType.INTEGER)
  return_quantity?: number; // For PDD discounts - quantity returned

  @ForeignKey(() => GRNReturnPDDRequest)
  @Column(DataType.UUID)
  pdd_request_id?: string; // Link to PDD request if applicable

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  applied_by!: string;

  @Column(DataType.DATE)
  applied_at!: Date;

  @Column(DataType.ENUM('active', 'reversed', 'expired'))
  status!: string;

  @Column(DataType.TEXT)
  notes?: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => SalesOrder)
  sales_order!: SalesOrder;

  @BelongsTo(() => SalesOrderItem)
  sales_order_item?: SalesOrderItem;

  @BelongsTo(() => User, 'applied_by')
  applied_by_user!: User;

  @BelongsTo(() => GRNReturnPDDRequest)
  pdd_request?: GRNReturnPDDRequest;
}
