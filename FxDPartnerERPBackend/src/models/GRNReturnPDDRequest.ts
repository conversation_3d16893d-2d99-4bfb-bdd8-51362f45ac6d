import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Organization } from './Organization';
import { SalesOrder } from './SalesOrder';
import { User } from './User';
import { GRNReturnPDDItem } from './GRNReturnPDDItem';

@Table({
  tableName: 'grn_return_pdd_requests',
  timestamps: true,
  underscored: true,
})
export class GRNReturnPDDRequest extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => SalesOrder)
  @Column(DataType.UUID)
  sales_order_id!: string;

  @Column(DataType.ENUM('return', 'pdd', 'both'))
  request_type!: string;

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  requested_by?: string;

  @Column(DataType.DATE)
  requested_at!: Date;

  @Column(DataType.TEXT)
  notes?: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => SalesOrder)
  sales_order!: SalesOrder;

  @BelongsTo(() => User, 'requested_by')
  requester!: User;

  @HasMany(() => GRNReturnPDDItem, 'grn_request_id')
  items!: GRNReturnPDDItem[];
}
