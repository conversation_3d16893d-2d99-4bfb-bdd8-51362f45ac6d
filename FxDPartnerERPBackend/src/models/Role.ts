import {
  Table,
  Column,
  Model,
  <PERSON>Type,
  <PERSON><PERSON>ey,
  <PERSON><PERSON>ult,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Organization } from './Organization';

@Table({
  tableName: 'roles',
  timestamps: true,
  underscored: true,
})
export class Role extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column({
    type: DataType.UUID,
    allowNull: true
  })
  organization_id?: string;

  @Column(DataType.STRING)
  name!: string;

  @Column(DataType.STRING)
  display_name!: string;

  @Column(DataType.TEXT)
  description?: string;

  @Column(DataType.JSON)
  permissions!: string[]; // Array of permission strings

  @Column(DataType.JSON)
  pages!: string[]; // Array of page IDs that this role can access

  @Default('active')
  @Column(DataType.ENUM('active', 'inactive'))
  status!: string;

  @<PERSON><PERSON><PERSON>(false)
  @Column(DataType.BOOLEAN)
  is_system_role!: boolean; // For built-in roles like admin, user, etc.

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  // Note: User association will be added later to avoid circular imports
}

// Export interfaces for type safety
export interface CreateRoleData {
  organization_id?: string;
  name: string;
  display_name: string;
  description?: string;
  permissions: string[];
  pages?: string[];
  status?: 'active' | 'inactive';
  is_system_role?: boolean;
}

export interface UpdateRoleData {
  name?: string;
  display_name?: string;
  description?: string;
  permissions?: string[];
  pages?: string[];
  status?: 'active' | 'inactive';
}

// Common permission constants
export const PERMISSIONS = {
  // User management
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  
  // Customer management
  CUSTOMER_CREATE: 'customer:create',
  CUSTOMER_READ: 'customer:read',
  CUSTOMER_UPDATE: 'customer:update',
  CUSTOMER_DELETE: 'customer:delete',
  
  // Supplier management
  SUPPLIER_CREATE: 'supplier:create',
  SUPPLIER_READ: 'supplier:read',
  SUPPLIER_UPDATE: 'supplier:update',
  SUPPLIER_DELETE: 'supplier:delete',
  
  // Product management
  PRODUCT_CREATE: 'product:create',
  PRODUCT_READ: 'product:read',
  PRODUCT_UPDATE: 'product:update',
  PRODUCT_DELETE: 'product:delete',
  
  // Inventory management
  INVENTORY_CREATE: 'inventory:create',
  INVENTORY_READ: 'inventory:read',
  INVENTORY_UPDATE: 'inventory:update',
  INVENTORY_DELETE: 'inventory:delete',
  INVENTORY_ADJUST: 'inventory:adjust',
  
  // Sales management
  SALES_CREATE: 'sales:create',
  SALES_READ: 'sales:read',
  SALES_UPDATE: 'sales:update',
  SALES_DELETE: 'sales:delete',
  SALES_APPROVE: 'sales:approve',
  
  // Purchase management
  PURCHASE_CREATE: 'purchase:create',
  PURCHASE_READ: 'purchase:read',
  PURCHASE_UPDATE: 'purchase:update',
  PURCHASE_DELETE: 'purchase:delete',
  PURCHASE_APPROVE: 'purchase:approve',
  
  // Payment management
  PAYMENT_CREATE: 'payment:create',
  PAYMENT_READ: 'payment:read',
  PAYMENT_UPDATE: 'payment:update',
  PAYMENT_DELETE: 'payment:delete',
  
  // Reports
  REPORTS_VIEW: 'reports:view',
  REPORTS_EXPORT: 'reports:export',
  
  // Settings
  SETTINGS_VIEW: 'settings:view',
  SETTINGS_UPDATE: 'settings:update',
  
  // Organization management
  ORG_MANAGE: 'organization:manage',
  
  // Role management
  ROLE_CREATE: 'role:create',
  ROLE_READ: 'role:read',
  ROLE_UPDATE: 'role:update',
  ROLE_DELETE: 'role:delete',
} as const;

// Default role templates
export const DEFAULT_ROLES = {
  ADMIN: {
    name: 'admin',
    display_name: 'Administrator',
    description: 'Full access to all features',
    permissions: Object.values(PERMISSIONS),
    is_system_role: true,
  },
  MANAGER: {
    name: 'manager',
    display_name: 'Manager',
    description: 'Management access with approval permissions',
    permissions: [
      PERMISSIONS.USER_READ,
      PERMISSIONS.CUSTOMER_CREATE,
      PERMISSIONS.CUSTOMER_READ,
      PERMISSIONS.CUSTOMER_UPDATE,
      PERMISSIONS.SUPPLIER_CREATE,
      PERMISSIONS.SUPPLIER_READ,
      PERMISSIONS.SUPPLIER_UPDATE,
      PERMISSIONS.PRODUCT_CREATE,
      PERMISSIONS.PRODUCT_READ,
      PERMISSIONS.PRODUCT_UPDATE,
      PERMISSIONS.INVENTORY_READ,
      PERMISSIONS.INVENTORY_ADJUST,
      PERMISSIONS.SALES_CREATE,
      PERMISSIONS.SALES_READ,
      PERMISSIONS.SALES_UPDATE,
      PERMISSIONS.SALES_APPROVE,
      PERMISSIONS.PURCHASE_CREATE,
      PERMISSIONS.PURCHASE_READ,
      PERMISSIONS.PURCHASE_UPDATE,
      PERMISSIONS.PURCHASE_APPROVE,
      PERMISSIONS.PAYMENT_CREATE,
      PERMISSIONS.PAYMENT_READ,
      PERMISSIONS.PAYMENT_UPDATE,
      PERMISSIONS.REPORTS_VIEW,
      PERMISSIONS.REPORTS_EXPORT,
      PERMISSIONS.SETTINGS_VIEW,
    ],
    is_system_role: true,
  },
  SALES_EXECUTIVE: {
    name: 'sales_executive',
    display_name: 'Sales Executive',
    description: 'Sales and customer management access',
    permissions: [
      PERMISSIONS.CUSTOMER_CREATE,
      PERMISSIONS.CUSTOMER_READ,
      PERMISSIONS.CUSTOMER_UPDATE,
      PERMISSIONS.PRODUCT_READ,
      PERMISSIONS.INVENTORY_READ,
      PERMISSIONS.SALES_CREATE,
      PERMISSIONS.SALES_READ,
      PERMISSIONS.SALES_UPDATE,
      PERMISSIONS.PAYMENT_READ,
      PERMISSIONS.REPORTS_VIEW,
    ],
    is_system_role: true,
  },
  PURCHASE_EXECUTIVE: {
    name: 'purchase_executive',
    display_name: 'Purchase Executive',
    description: 'Purchase and supplier management access',
    permissions: [
      PERMISSIONS.SUPPLIER_CREATE,
      PERMISSIONS.SUPPLIER_READ,
      PERMISSIONS.SUPPLIER_UPDATE,
      PERMISSIONS.PRODUCT_READ,
      PERMISSIONS.INVENTORY_READ,
      PERMISSIONS.PURCHASE_CREATE,
      PERMISSIONS.PURCHASE_READ,
      PERMISSIONS.PURCHASE_UPDATE,
      PERMISSIONS.PAYMENT_READ,
      PERMISSIONS.REPORTS_VIEW,
    ],
    is_system_role: true,
  },
  INVENTORY_MANAGER: {
    name: 'inventory_manager',
    display_name: 'Inventory Manager',
    description: 'Inventory and product management access',
    permissions: [
      PERMISSIONS.PRODUCT_CREATE,
      PERMISSIONS.PRODUCT_READ,
      PERMISSIONS.PRODUCT_UPDATE,
      PERMISSIONS.INVENTORY_CREATE,
      PERMISSIONS.INVENTORY_READ,
      PERMISSIONS.INVENTORY_UPDATE,
      PERMISSIONS.INVENTORY_ADJUST,
      PERMISSIONS.REPORTS_VIEW,
    ],
    is_system_role: true,
  },
  VIEWER: {
    name: 'viewer',
    display_name: 'Viewer',
    description: 'Read-only access to most features',
    permissions: [
      PERMISSIONS.CUSTOMER_READ,
      PERMISSIONS.SUPPLIER_READ,
      PERMISSIONS.PRODUCT_READ,
      PERMISSIONS.INVENTORY_READ,
      PERMISSIONS.SALES_READ,
      PERMISSIONS.PURCHASE_READ,
      PERMISSIONS.PAYMENT_READ,
      PERMISSIONS.REPORTS_VIEW,
    ],
    is_system_role: true,
  },
} as const;
