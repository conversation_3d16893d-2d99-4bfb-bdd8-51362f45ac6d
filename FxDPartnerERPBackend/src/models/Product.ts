import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Organization } from './Organization';

@Table({
  tableName: 'products',
  timestamps: true,
  underscored: true,
})
export class Product extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @Column(DataType.STRING)
  name!: string;

  @Column(DataType.TEXT)
  description?: string;

  @Default('active')
  @Column(DataType.ENUM('active', 'inactive'))
  status!: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @HasMany(() => SKU)
  skus!: SKU[];
}

@Table({
  tableName: 'skus',
  timestamps: true,
  underscored: true,
})
export class SKU extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => Product)
  @Column(DataType.UUID)
  product_id!: string;

  @Column(DataType.STRING)
  code!: string;

  @Column(DataType.ENUM('box', 'loose'))
  unit_type!: string;

  @Column(DataType.DECIMAL(8, 2))
  unit_weight?: number;

  @Default('active')
  @Column(DataType.ENUM('active', 'inactive'))
  status!: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => Product)
  product!: Product;
}

// Export interfaces for backward compatibility
export interface CreateProductData {
  organization_id: string;
  name: string;
  description?: string;
}

export interface UpdateProductData {
  name?: string;
  description?: string;
  status?: 'active' | 'inactive';
}

export interface CreateSKUData {
  product_id: string;
  code: string;
  unit_type: 'box' | 'loose';
  unit_weight?: number;
}

export interface UpdateSKUData {
  code?: string;
  unit_type?: 'box' | 'loose';
  unit_weight?: number;
  status?: 'active' | 'inactive';
}
