import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Organization } from './Organization';
import { Customer } from './Customer';
import { Supplier } from './Supplier';

@Table({
  tableName: 'payments',
  timestamps: true,
  underscored: true,
})
export class Payment extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @Column(DataType.ENUM('received', 'paid', 'made', 'expense'))
  type!: string;

  @Column(DataType.DECIMAL(10, 2))
  amount!: number;

  @Column(DataType.DATE)
  payment_date!: Date;

  @Column(DataType.UUID)
  party_id?: string;

  @Column(DataType.ENUM('customer', 'supplier'))
  party_type?: string;

  @Column(DataType.STRING)
  party_name!: string;

  @Column(DataType.UUID)
  reference_id?: string;

  @Column(DataType.ENUM('sales_order', 'purchase_record', 'general'))
  reference_type?: string;

  @Column(DataType.STRING)
  reference_number?: string;

  @Column(DataType.ENUM('cash', 'online', 'cheque', 'bank_transfer', 'upi'))
  mode!: string;

  @Default('pending')
  @Column(DataType.ENUM('pending', 'confirmed', 'failed', 'completed'))
  status!: string;

  @Column(DataType.TEXT)
  notes?: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;
}

@Table({
  tableName: 'customer_credit_extensions',
  timestamps: true,
  underscored: true,
})
export class CustomerCreditExtension extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => Customer)
  @Column(DataType.UUID)
  customer_id!: string;

  @Column(DataType.UUID)
  sales_order_id?: string;

  @Column(DataType.DECIMAL(10, 2))
  amount!: number;

  @Column(DataType.TEXT)
  remarks?: string;

  @Default('pending')
  @Column(DataType.ENUM('pending', 'approved', 'rejected'))
  status!: string;

  @Column(DataType.DATE)
  approved_at?: Date;

  @Column(DataType.STRING)
  approved_by?: string;

  @Column(DataType.TEXT)
  approval_notes?: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => Customer)
  customer!: Customer;
}

// Export interfaces for backward compatibility
export interface CreatePaymentData {
  organization_id: string;
  type: 'received' | 'paid' | 'made' | 'expense';
  amount: number;
  payment_date: Date;
  party_id?: string;
  party_type?: 'customer' | 'supplier';
  party_name: string;
  reference_id?: string;
  reference_type?: 'sales_order' | 'purchase_record';
  reference_number?: string;
  mode: 'cash' | 'online' | 'cheque' | 'bank_transfer' | 'upi';
  status?: 'pending' | 'confirmed' | 'failed' | 'completed';
  notes?: string;
}

export interface UpdatePaymentData {
  amount?: number;
  payment_date?: Date;
  mode?: 'cash' | 'online' | 'cheque' | 'bank_transfer' | 'upi';
  status?: 'pending' | 'confirmed' | 'failed' | 'completed';
  notes?: string;
}

export interface CreateCreditExtensionData {
  organization_id: string;
  customer_id: string;
  sales_order_id?: string;
  amount: number;
  remarks?: string;
}

export interface UpdateCreditExtensionData {
  status?: 'pending' | 'approved' | 'rejected';
  approved_at?: Date;
  approved_by?: string;
  approval_notes?: string;
}
