import { Request, Response } from 'express';
import { Role } from '../../models/Role';
import { Organization } from '../../models/Organization';
import { Page } from '../../models/Page';
import { AdminRequest } from '../middleware/adminAuth';

export class AdminRoleController {
  // Get all roles
  static async getAllRoles(req: AdminRequest, res: Response) {
    try {
      const { page = 1, limit = 10, search = '', organizationId = '', status = '' } = req.query;
      const offset = (Number(page) - 1) * Number(limit);

      // Build where clause
      const whereClause: any = {};
      
      if (search) {
        whereClause[require('sequelize').Op.or] = [
          { name: { [require('sequelize').Op.iLike]: `%${search}%` } },
          { display_name: { [require('sequelize').Op.iLike]: `%${search}%` } }
        ];
      }
      
      if (organizationId && organizationId !== 'all') {
        whereClause.organization_id = organizationId;
      }
      
      if (status && status !== 'all') {
        whereClause.status = status;
      }

      const { count, rows: roles } = await Role.findAndCountAll({
        where: whereClause,
        limit: Number(limit),
        offset,
        order: [['created_at', 'DESC']],
        include: [
          {
            model: Organization,
            as: 'organization',
            attributes: ['id', 'name', 'status']
          }
        ]
      });

      res.json({
        success: true,
        message: 'Roles retrieved successfully',
        data: {
          roles,
          pagination: {
            total: count,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(count / Number(limit))
          }
        }
      });
    } catch (error) {
      console.error('Get roles error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching roles'
      });
    }
  }

  // Get role by ID
  static async getRoleById(req: AdminRequest, res: Response) {
    try {
      const { id } = req.params;

      const role = await Role.findByPk(id, {
        include: [
          {
            model: Organization,
            as: 'organization',
            attributes: ['id', 'name', 'status']
          }
        ]
      });

      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role not found'
        });
      }

      res.json({
        success: true,
        message: 'Role retrieved successfully',
        data: { role }
      });
    } catch (error) {
      console.error('Get role by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching role'
      });
    }
  }

  // Create role
  static async createRole(req: AdminRequest, res: Response) {
    try {
      const { name, display_name, description, permissions, pages, status } = req.body;

      // Validate required fields
      if (!name || !display_name) {
        return res.status(400).json({
          success: false,
          message: 'Name and display name are required'
        });
      }

      // Validate permissions array
      if (permissions && !Array.isArray(permissions)) {
        return res.status(400).json({
          success: false,
          message: 'Permissions must be an array'
        });
      }

      // Validate pages array
      if (pages && !Array.isArray(pages)) {
        return res.status(400).json({
          success: false,
          message: 'Pages must be an array'
        });
      }

      // Check if role name already exists (globally, not per organization)
      const existingRole = await Role.findOne({
        where: { name }
      });

      if (existingRole) {
        return res.status(400).json({
          success: false,
          message: 'Role name already exists'
        });
      }

      // Create role without organization_id
      const role = await Role.create({
        name,
        display_name,
        description: description || '',
        permissions: permissions || [],
        pages: pages || [],
        status: status || 'active',
        is_system_role: false
      });

      // Fetch created role
      const createdRole = await Role.findByPk(role.id);

      res.status(201).json({
        success: true,
        message: 'Role created successfully',
        data: { role: createdRole }
      });
    } catch (error) {
      console.error('Create role error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while creating role'
      });
    }
  }

  // Update role
  static async updateRole(req: AdminRequest, res: Response) {
    try {
      const { id } = req.params;
      const { display_name, description, permissions, pages, status } = req.body;

      const role = await Role.findByPk(id);

      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role not found'
        });
      }

      // Validate required fields
      if (!display_name) {
        return res.status(400).json({
          success: false,
          message: 'Display name is required'
        });
      }

      // Validate permissions array
      if (permissions && !Array.isArray(permissions)) {
        return res.status(400).json({
          success: false,
          message: 'Permissions must be an array'
        });
      }

      // Validate pages array
      if (pages && !Array.isArray(pages)) {
        return res.status(400).json({
          success: false,
          message: 'Pages must be an array'
        });
      }

      // Update role
      await role.update({
        display_name,
        description: description !== undefined ? description : role.description,
        permissions: permissions !== undefined ? permissions : role.permissions,
        pages: pages !== undefined ? pages : role.pages,
        status: status !== undefined ? status : role.status
      });

      // Fetch updated role with organization
      const updatedRole = await Role.findByPk(id, {
        include: [
          {
            model: Organization,
            as: 'organization',
            attributes: ['id', 'name', 'status']
          }
        ]
      });

      res.json({
        success: true,
        message: 'Role updated successfully',
        data: { role: updatedRole }
      });
    } catch (error) {
      console.error('Update role error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while updating role'
      });
    }
  }

  // Delete role
  static async deleteRole(req: AdminRequest, res: Response) {
    try {
      const { id } = req.params;

      const role = await Role.findByPk(id);

      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role not found'
        });
      }

      // Check if role is a system role
      if (role.is_system_role) {
        return res.status(400).json({
          success: false,
          message: 'System roles cannot be deleted'
        });
      }

      // TODO: Check if role is assigned to any users
      // This would require checking the UserRole junction table
      // For now, we'll allow deletion

      await role.destroy();

      res.json({
        success: true,
        message: 'Role deleted successfully'
      });
    } catch (error) {
      console.error('Delete role error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while deleting role'
      });
    }
  }

  // Get available pages
  static async getAvailablePages(req: AdminRequest, res: Response) {
    try {
      const pages = await Page.findAll({
        where: { is_active: true },
        order: [['category', 'ASC'], ['sort_order', 'ASC'], ['display_name', 'ASC']],
        attributes: ['id', 'name', 'display_name', 'category', 'route_path', 'icon']
      });

      // Group pages by category
      const pageGroups: { [key: string]: any[] } = {};
      pages.forEach(page => {
        if (!pageGroups[page.category]) {
          pageGroups[page.category] = [];
        }
        pageGroups[page.category].push({
          id: page.id,
          name: page.name,
          display_name: page.display_name,
          route_path: page.route_path,
          icon: page.icon
        });
      });

      res.json({
        success: true,
        message: 'Available pages retrieved successfully',
        data: { pageGroups }
      });
    } catch (error) {
      console.error('Get pages error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching pages'
      });
    }
  }

  // Get available permissions
  static async getAvailablePermissions(req: AdminRequest, res: Response) {
    try {
      // Import permissions from Role model
      const { PERMISSIONS } = require('../../models/Role');
      
      // Group permissions by category
      const permissionGroups = {
        'User Management': [
          { key: PERMISSIONS.USER_CREATE, label: 'Create Users', description: 'Create new user accounts' },
          { key: PERMISSIONS.USER_READ, label: 'View Users', description: 'View user information' },
          { key: PERMISSIONS.USER_UPDATE, label: 'Update Users', description: 'Edit user information' },
          { key: PERMISSIONS.USER_DELETE, label: 'Delete Users', description: 'Delete user accounts' }
        ],
        'Customer Management': [
          { key: PERMISSIONS.CUSTOMER_CREATE, label: 'Create Customers', description: 'Add new customers' },
          { key: PERMISSIONS.CUSTOMER_READ, label: 'View Customers', description: 'View customer information' },
          { key: PERMISSIONS.CUSTOMER_UPDATE, label: 'Update Customers', description: 'Edit customer information' },
          { key: PERMISSIONS.CUSTOMER_DELETE, label: 'Delete Customers', description: 'Delete customer records' }
        ],
        'Supplier Management': [
          { key: PERMISSIONS.SUPPLIER_CREATE, label: 'Create Suppliers', description: 'Add new suppliers' },
          { key: PERMISSIONS.SUPPLIER_READ, label: 'View Suppliers', description: 'View supplier information' },
          { key: PERMISSIONS.SUPPLIER_UPDATE, label: 'Update Suppliers', description: 'Edit supplier information' },
          { key: PERMISSIONS.SUPPLIER_DELETE, label: 'Delete Suppliers', description: 'Delete supplier records' }
        ],
        'Product Management': [
          { key: PERMISSIONS.PRODUCT_CREATE, label: 'Create Products', description: 'Add new products' },
          { key: PERMISSIONS.PRODUCT_READ, label: 'View Products', description: 'View product information' },
          { key: PERMISSIONS.PRODUCT_UPDATE, label: 'Update Products', description: 'Edit product information' },
          { key: PERMISSIONS.PRODUCT_DELETE, label: 'Delete Products', description: 'Delete product records' }
        ],
        'Inventory Management': [
          { key: PERMISSIONS.INVENTORY_CREATE, label: 'Create Inventory', description: 'Create inventory records' },
          { key: PERMISSIONS.INVENTORY_READ, label: 'View Inventory', description: 'View inventory information' },
          { key: PERMISSIONS.INVENTORY_UPDATE, label: 'Update Inventory', description: 'Edit inventory records' },
          { key: PERMISSIONS.INVENTORY_DELETE, label: 'Delete Inventory', description: 'Delete inventory records' },
          { key: PERMISSIONS.INVENTORY_ADJUST, label: 'Adjust Inventory', description: 'Make inventory adjustments' }
        ],
        'Sales Management': [
          { key: PERMISSIONS.SALES_CREATE, label: 'Create Sales', description: 'Create sales orders' },
          { key: PERMISSIONS.SALES_READ, label: 'View Sales', description: 'View sales information' },
          { key: PERMISSIONS.SALES_UPDATE, label: 'Update Sales', description: 'Edit sales orders' },
          { key: PERMISSIONS.SALES_DELETE, label: 'Delete Sales', description: 'Delete sales orders' },
          { key: PERMISSIONS.SALES_APPROVE, label: 'Approve Sales', description: 'Approve sales orders' }
        ],
        'Purchase Management': [
          { key: PERMISSIONS.PURCHASE_CREATE, label: 'Create Purchases', description: 'Create purchase orders' },
          { key: PERMISSIONS.PURCHASE_READ, label: 'View Purchases', description: 'View purchase information' },
          { key: PERMISSIONS.PURCHASE_UPDATE, label: 'Update Purchases', description: 'Edit purchase orders' },
          { key: PERMISSIONS.PURCHASE_DELETE, label: 'Delete Purchases', description: 'Delete purchase orders' },
          { key: PERMISSIONS.PURCHASE_APPROVE, label: 'Approve Purchases', description: 'Approve purchase orders' }
        ],
        'Payment Management': [
          { key: PERMISSIONS.PAYMENT_CREATE, label: 'Create Payments', description: 'Record new payments' },
          { key: PERMISSIONS.PAYMENT_READ, label: 'View Payments', description: 'View payment information' },
          { key: PERMISSIONS.PAYMENT_UPDATE, label: 'Update Payments', description: 'Edit payment records' },
          { key: PERMISSIONS.PAYMENT_DELETE, label: 'Delete Payments', description: 'Delete payment records' }
        ],
        'Reports & Analytics': [
          { key: PERMISSIONS.REPORTS_VIEW, label: 'View Reports', description: 'Access reports and analytics' },
          { key: PERMISSIONS.REPORTS_EXPORT, label: 'Export Reports', description: 'Export reports to files' }
        ],
        'System Management': [
          { key: PERMISSIONS.SETTINGS_VIEW, label: 'View Settings', description: 'View system settings' },
          { key: PERMISSIONS.SETTINGS_UPDATE, label: 'Update Settings', description: 'Modify system settings' },
          { key: PERMISSIONS.ORG_MANAGE, label: 'Manage Organization', description: 'Manage organization settings' },
          { key: PERMISSIONS.ROLE_CREATE, label: 'Create Roles', description: 'Create new roles' },
          { key: PERMISSIONS.ROLE_READ, label: 'View Roles', description: 'View role information' },
          { key: PERMISSIONS.ROLE_UPDATE, label: 'Update Roles', description: 'Edit role permissions' },
          { key: PERMISSIONS.ROLE_DELETE, label: 'Delete Roles', description: 'Delete roles' }
        ]
      };

      res.json({
        success: true,
        message: 'Available permissions retrieved successfully',
        data: { permissionGroups }
      });
    } catch (error) {
      console.error('Get permissions error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching permissions'
      });
    }
  }

  // Get role statistics
  static async getRoleStats(req: AdminRequest, res: Response) {
    try {
      // Return zero values for now
      res.json({
        success: true,
        message: 'Role statistics retrieved successfully',
        data: {
          totalRoles: 0,
          activeRoles: 0,
          inactiveRoles: 0,
          systemRoles: 0,
          customRoles: 0
        }
      });
    } catch (error) {
      console.error('Get role stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching statistics'
      });
    }
  }
}
