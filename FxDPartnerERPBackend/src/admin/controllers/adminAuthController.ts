import { Request, Response } from 'express';
import { validateAdminCredentials, generateAdminToken } from '../middleware/adminAuth';

export class AdminAuthController {
  // Admin login
  static async login(req: Request, res: Response) {
    try {
      const { username, password } = req.body;

      // Validate input
      if (!username || !password) {
        return res.status(400).json({
          success: false,
          message: 'Username and password are required'
        });
      }

      // Validate credentials
      if (!validateAdminCredentials(username, password)) {
        return res.status(401).json({
          success: false,
          message: 'Invalid admin credentials'
        });
      }

      // Generate token
      const token = generateAdminToken(username);

      res.json({
        success: true,
        message: 'Admin login successful',
        data: {
          token,
          admin: {
            username,
            loginTime: new Date().toISOString()
          }
        }
      });
    } catch (error) {
      console.error('Admin login error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during admin login'
      });
    }
  }

  // Admin logout
  static async logout(req: Request, res: Response) {
    try {
      // In a real implementation, you might want to blacklist the token
      // For now, we'll just return success
      res.json({
        success: true,
        message: 'Admin logout successful'
      });
    } catch (error) {
      console.error('Admin logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during admin logout'
      });
    }
  }

  // Verify admin token
  static async verify(req: Request, res: Response) {
    try {
      // If we reach here, the adminAuth middleware has already validated the token
      res.json({
        success: true,
        message: 'Admin token is valid',
        data: {
          admin: (req as any).admin
        }
      });
    } catch (error) {
      console.error('Admin token verification error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during token verification'
      });
    }
  }
}
