import { Request, Response } from 'express';
import { Organization } from '../../models/Organization';
import { User } from '../../models/User';
import { UserOrganization } from '../../models/UserOrganization';
import { AdminRequest } from '../middleware/adminAuth';

export class AdminOrganizationController {
  // Get all organizations
  static async getAllOrganizations(req: AdminRequest, res: Response) {
    try {
      const { page = 1, limit = 10, search = '', status = '' } = req.query;
      const offset = (Number(page) - 1) * Number(limit);

      // Build where clause
      const whereClause: any = {};
      
      if (search) {
        whereClause.name = {
          [require('sequelize').Op.iLike]: `%${search}%`
        };
      }
      
      if (status && status !== 'all') {
        whereClause.status = status;
      }

      const { count, rows: organizations } = await Organization.findAndCountAll({
        where: whereClause,
        limit: Number(limit),
        offset,
        order: [['created_at', 'DESC']],
        include: [
          {
            model: User,
            through: { attributes: [] }, // Exclude junction table attributes
            attributes: ['id', 'first_name', 'email', 'status'],
            required: false
          }
        ]
      });

      res.json({
        success: true,
        message: 'Organizations retrieved successfully',
        data: {
          organizations,
          pagination: {
            total: count,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(count / Number(limit))
          }
        }
      });
    } catch (error) {
      console.error('Get organizations error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching organizations'
      });
    }
  }

  // Get organization by ID
  static async getOrganizationById(req: AdminRequest, res: Response) {
    try {
      const { id } = req.params;

      const organization = await Organization.findByPk(id, {
        include: [
          {
            model: User,
            through: { attributes: [] }, // Exclude junction table attributes
            attributes: ['id', 'first_name', 'email', 'status', 'created_at'],
            required: false
          }
        ]
      });

      if (!organization) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found'
        });
      }

      res.json({
        success: true,
        message: 'Organization retrieved successfully',
        data: { organization }
      });
    } catch (error) {
      console.error('Get organization by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching organization'
      });
    }
  }

  // Create new organization
  static async createOrganization(req: AdminRequest, res: Response) {
    try {
      const { name, address } = req.body;

      // Validate required fields
      if (!name) {
        return res.status(400).json({
          success: false,
          message: 'Organization name is required'
        });
      }

      // Check if organization with same name already exists
      const existingOrg = await Organization.findOne({
        where: { name }
      });

      if (existingOrg) {
        return res.status(400).json({
          success: false,
          message: 'Organization with this name already exists'
        });
      }

      // Generate organization code from name
      const code = name.toLowerCase().replace(/[^a-z0-9]/g, '').substring(0, 10) + '_' + Date.now().toString().slice(-4);

      const organization = await Organization.create({
        name,
        code,
        address: address || null,
        status: 'active'
      });

      res.status(201).json({
        success: true,
        message: 'Organization created successfully',
        data: { organization }
      });
    } catch (error) {
      console.error('Create organization error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while creating organization'
      });
    }
  }

  // Update organization
  static async updateOrganization(req: AdminRequest, res: Response) {
    try {
      const { id } = req.params;
      const { name, address, status } = req.body;

      const organization = await Organization.findByPk(id);

      if (!organization) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found'
        });
      }

      // Validate required fields
      if (!name) {
        return res.status(400).json({
          success: false,
          message: 'Organization name is required'
        });
      }

      // Check if another organization with same name exists
      if (name !== organization.name) {
        const existingOrg = await Organization.findOne({
          where: { 
            name,
            id: { [require('sequelize').Op.ne]: id }
          }
        });

        if (existingOrg) {
          return res.status(400).json({
            success: false,
            message: 'Another organization with this name already exists'
          });
        }
      }

      // Update organization
      await organization.update({
        name,
        address: address || null,
        status: status || organization.status
      });

      res.json({
        success: true,
        message: 'Organization updated successfully',
        data: { organization }
      });
    } catch (error) {
      console.error('Update organization error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while updating organization'
      });
    }
  }

  // Delete organization
  static async deleteOrganization(req: AdminRequest, res: Response) {
    try {
      const { id } = req.params;

      const organization = await Organization.findByPk(id);

      if (!organization) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found'
        });
      }

      // Check if organization has users through UserOrganization junction table
      const userCount = await UserOrganization.count({
        where: { organization_id: id }
      });

      if (userCount > 0) {
        return res.status(400).json({
          success: false,
          message: `Cannot delete organization. It has ${userCount} associated users. Please reassign or delete users first.`
        });
      }

      await organization.destroy();

      res.json({
        success: true,
        message: 'Organization deleted successfully'
      });
    } catch (error) {
      console.error('Delete organization error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while deleting organization'
      });
    }
  }

  // Get organization statistics
  static async getOrganizationStats(req: AdminRequest, res: Response) {
    try {
      // Get total organizations count
      const totalOrganizations = await Organization.count();
      
      // Get active organizations count
      const activeOrganizations = await Organization.count({
        where: { status: 'active' }
      });
      
      // Get inactive organizations count
      const inactiveOrganizations = await Organization.count({
        where: { status: 'inactive' }
      });
      
      // Get total users count
      const totalUsers = await User.count();

      res.json({
        success: true,
        message: 'Organization statistics retrieved successfully',
        data: {
          totalOrganizations,
          activeOrganizations,
          inactiveOrganizations,
          totalUsers
        }
      });
    } catch (error) {
      console.error('Get organization stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching statistics'
      });
    }
  }
}
