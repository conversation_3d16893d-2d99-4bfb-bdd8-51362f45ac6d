import { Router } from 'express';
import { AdminOrganizationController } from '../controllers/adminOrganizationController';
import { adminAuth } from '../middleware/adminAuth';

const router = Router();

// All organization routes require admin authentication
router.use(adminAuth);

// Organization CRUD routes
router.get('/', AdminOrganizationController.getAllOrganizations);
router.get('/stats', AdminOrganizationController.getOrganizationStats);
router.get('/:id', AdminOrganizationController.getOrganizationById);
router.post('/', AdminOrganizationController.createOrganization);
router.put('/:id', AdminOrganizationController.updateOrganization);
router.delete('/:id', AdminOrganizationController.deleteOrganization);

export default router;
