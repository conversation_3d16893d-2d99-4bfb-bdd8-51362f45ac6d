import { Router } from 'express';
import { AdminUserController } from '../controllers/adminUserControllerNew';
import { adminAuth } from '../middleware/adminAuth';

const router = Router();

// All user routes require admin authentication
router.use(adminAuth);

// User management routes
router.get('/', AdminUserController.getAllUsers);
router.post('/', AdminUserController.createUser);
router.post('/simple', AdminUserController.createSimpleUser);
router.get('/stats', AdminUserController.getUserStats);
router.get('/organization/:organizationId', AdminUserController.getUsersByOrganization);
router.get('/:id', AdminUserController.getUserById);
router.put('/:id', AdminUserController.updateUser);
router.delete('/:id', AdminUserController.deleteUser);

// Page permission routes
router.get('/:userId/permissions', AdminUserController.getUserPagePermissions);
router.put('/:userId/permissions', AdminUserController.updateUserPagePermissions);

// Organization and role management routes
router.post('/:userId/organizations', AdminUserController.addUserToOrganization);
router.delete('/:userId/organizations/:organizationId', AdminUserController.removeUserFromOrganization);
router.put('/:userId/organizations/:organizationId/roles', AdminUserController.updateUserRolesInOrganization);

// Atomic assignment update route
router.put('/:userId/assignments', AdminUserController.updateUserAssignments);

export default router;
