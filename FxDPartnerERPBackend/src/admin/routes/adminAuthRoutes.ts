import { Router } from 'express';
import { AdminAuthController } from '../controllers/adminAuthController';
import { adminAuth } from '../middleware/adminAuth';

const router = Router();

// Admin authentication routes
router.post('/login', AdminAuthController.login);
router.post('/logout', adminAuth, AdminAuthController.logout);
router.get('/verify', adminAuth, AdminAuthController.verify);

export default router;
