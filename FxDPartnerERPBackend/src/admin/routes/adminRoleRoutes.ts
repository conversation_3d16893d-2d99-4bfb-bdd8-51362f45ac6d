import { Router } from 'express';
import { AdminRoleController } from '../controllers/adminRoleController';
import { adminAuth } from '../middleware/adminAuth';

const router = Router();

// All role routes require admin authentication
router.use(adminAuth);

// Role management routes
router.get('/', AdminRoleController.getAllRoles);
router.post('/', AdminRoleController.createRole);
router.get('/stats', AdminRoleController.getRoleStats);
router.get('/permissions', AdminRoleController.getAvailablePermissions);
router.get('/pages', AdminRoleController.getAvailablePages);
router.get('/:id', AdminRoleController.getRoleById);
router.put('/:id', AdminRoleController.updateRole);
router.delete('/:id', AdminRoleController.deleteRole);

export default router;
