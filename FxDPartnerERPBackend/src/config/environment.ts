import dotenv from 'dotenv';

dotenv.config();

export interface EnvironmentConfig {
  port: number;
  nodeEnv: string;
  database: {
    host: string;
    port: number;
    user: string;
    password: string;
    name: string;
  };
  jwt: {
    secret: string;
  };
  cors: {
    origin: string;
  };
}

const config: EnvironmentConfig = {
  port: parseInt(process.env.PORT || '3001'),
  nodeEnv: process.env.NODE_ENV || 'development',
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    name: process.env.DB_NAME || 'fxd_partner_erp',
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'your-secret-key',
  },
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:5173' || 'https://fxdpartner-staging.vegrow.in',
  },
};

// Validate required environment variables
const requiredEnvVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];

if (config.nodeEnv === 'production') {
  requiredEnvVars.push('JWT_SECRET');
}

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingEnvVars.join(', '));
  if (config.nodeEnv === 'production') {
    process.exit(1);
  } else {
    console.warn('⚠️  Some environment variables are missing. Using defaults for development.');
  }
}

export default config;
