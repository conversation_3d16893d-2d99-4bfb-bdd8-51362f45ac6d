import { Role } from '../models/Role';
import sequelize from '../config/sequelize';

async function seedRoles() {
  try {
    await sequelize.authenticate();
    console.log('Database connected successfully');

    // Create default roles if they don't exist
    const [adminRole] = await Role.findOrCreate({
      where: { name: 'admin' },
      defaults: {
        name: 'admin',
        description: 'Administrator with full access',
        permissions: JSON.stringify(['*'])
      }
    });

    const [managerRole] = await Role.findOrCreate({
      where: { name: 'manager' },
      defaults: {
        name: 'manager',
        description: 'Manager with limited administrative access',
        permissions: JSON.stringify(['read', 'write', 'manage_inventory', 'manage_sales'])
      }
    });

    const [staffRole] = await Role.findOrCreate({
      where: { name: 'staff' },
      defaults: {
        name: 'staff',
        description: 'Staff member with basic access',
        permissions: JSON.stringify(['read', 'write'])
      }
    });

    console.log('✅ Roles created/found successfully');
    console.log('Available roles: admin, manager, staff');

  } catch (error) {
    console.error('❌ Error seeding roles:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the seeding function
seedRoles();
