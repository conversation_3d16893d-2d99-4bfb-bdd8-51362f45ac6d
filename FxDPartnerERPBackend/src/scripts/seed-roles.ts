import sequelize from '../config/sequelize';
import { Role, DEFAULT_ROLES, PERMISSIONS } from '../models/Role';
import { Organization } from '../models/Organization';

// Map permissions to pages
const getPagesByPermissions = (permissions: string[]): string[] => {
  const pages = new Set<string>();
  
  // Always include dashboard
  pages.add('dashboard');
  
  // Map permissions to pages
  permissions.forEach(permission => {
    switch (permission) {
      // Purchase/Procurement pages
      case PERMISSIONS.PURCHASE_CREATE:
      case PERMISSIONS.PURCHASE_READ:
      case PERMISSIONS.PURCHASE_UPDATE:
      case PERMISSIONS.PURCHASE_DELETE:
      case PERMISSIONS.PURCHASE_APPROVE:
        pages.add('vehicle_arrival');
        pages.add('record_purchase');
        break;
        
      // Inventory pages
      case PERMISSIONS.INVENTORY_CREATE:
      case PERMISSIONS.INVENTORY_READ:
      case PERMISSIONS.INVENTORY_UPDATE:
      case PERMISSIONS.INVENTORY_DELETE:
      case PERMISSIONS.INVENTORY_ADJUST:
      case PERMISSIONS.PRODUCT_CREATE:
      case PERMISSIONS.PRODUCT_READ:
      case PERMISSIONS.PRODUCT_UPDATE:
      case PERMISSIONS.PRODUCT_DELETE:
        pages.add('inventory');
        break;
        
      // Sales pages
      case PERMISSIONS.SALES_CREATE:
      case PERMISSIONS.SALES_READ:
      case PERMISSIONS.SALES_UPDATE:
      case PERMISSIONS.SALES_DELETE:
        pages.add('sales');
        pages.add('dispatch');
        break;
        
      case PERMISSIONS.SALES_APPROVE:
        pages.add('pending_approvals');
        break;
        
      // Customer pages
      case PERMISSIONS.CUSTOMER_CREATE:
      case PERMISSIONS.CUSTOMER_READ:
      case PERMISSIONS.CUSTOMER_UPDATE:
      case PERMISSIONS.CUSTOMER_DELETE:
        pages.add('customers');
        break;
        
      // Supplier pages
      case PERMISSIONS.SUPPLIER_CREATE:
      case PERMISSIONS.SUPPLIER_READ:
      case PERMISSIONS.SUPPLIER_UPDATE:
      case PERMISSIONS.SUPPLIER_DELETE:
        pages.add('suppliers');
        break;
        
      // Financial pages
      case PERMISSIONS.PAYMENT_CREATE:
      case PERMISSIONS.PAYMENT_READ:
      case PERMISSIONS.PAYMENT_UPDATE:
      case PERMISSIONS.PAYMENT_DELETE:
        pages.add('payments');
        break;
        
      case PERMISSIONS.REPORTS_VIEW:
      case PERMISSIONS.REPORTS_EXPORT:
        pages.add('ledger');
        break;
        
      // Settings - Removed from roles, only accessible via direct page permissions
      // case PERMISSIONS.SETTINGS_VIEW:
      // case PERMISSIONS.SETTINGS_UPDATE:
      //   pages.add('settings');
      //   break;
    }
  });
  
  return Array.from(pages);
};

async function seedRoles() {
  try {
    console.log('🌱 Seeding roles...');
    
    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connected successfully');
    
    // Get all organizations to create roles for each
    const organizations = await Organization.findAll({
      where: { status: 'active' }
    });
    
    console.log(`📋 Found ${organizations.length} organizations`);
    
    // Create system roles (organization-independent)
    console.log('🔧 Creating system roles...');
    
    for (const [roleKey, roleData] of Object.entries(DEFAULT_ROLES)) {
      try {
        // Check if system role already exists
        const existingSystemRole = await Role.findOne({
          where: {
            name: roleData.name,
            is_system_role: true,
            organization_id: null
          }
        });
        
        if (!existingSystemRole) {
          const pages = getPagesByPermissions([...roleData.permissions]);
          await Role.create({
            name: roleData.name,
            display_name: roleData.display_name,
            description: roleData.description,
            permissions: [...roleData.permissions],
            pages: pages,
            status: 'active',
            is_system_role: true,
            organization_id: null
          });
          console.log(`✅ Created system role: ${roleData.display_name} with ${pages.length} pages`);
        } else {
          console.log(`⏭️  System role already exists: ${roleData.display_name}`);
        }
      } catch (error) {
        console.error(`❌ Error creating system role ${roleData.name}:`, error);
      }
    }
    
    // Create organization-specific roles
    console.log('🏢 Creating organization-specific roles...');
    
    for (const organization of organizations) {
      console.log(`\n📁 Processing organization: ${organization.name}`);
      
      for (const [roleKey, roleData] of Object.entries(DEFAULT_ROLES)) {
        try {
          // Check if role already exists for this organization
          const existingRole = await Role.findOne({
            where: {
              name: roleData.name,
              organization_id: organization.id
            }
          });
          
          if (!existingRole) {
            const pages = getPagesByPermissions([...roleData.permissions]);
            await Role.create({
              name: roleData.name,
              display_name: roleData.display_name,
              description: roleData.description,
              permissions: [...roleData.permissions],
              pages: pages,
              status: 'active',
              is_system_role: false,
              organization_id: organization.id
            });
            console.log(`  ✅ Created role: ${roleData.display_name} with ${pages.length} pages`);
          } else {
            console.log(`  ⏭️  Role already exists: ${roleData.display_name}`);
          }
        } catch (error) {
          console.error(`  ❌ Error creating role ${roleData.name} for ${organization.name}:`, error);
        }
      }
    }
    
    // Summary
    const totalRoles = await Role.count();
    const systemRoles = await Role.count({ where: { is_system_role: true } });
    const orgRoles = await Role.count({ where: { is_system_role: false } });
    
    console.log('\n📊 Seeding Summary:');
    console.log(`   Total roles: ${totalRoles}`);
    console.log(`   System roles: ${systemRoles}`);
    console.log(`   Organization roles: ${orgRoles}`);
    
    console.log('🎉 Roles seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding roles:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the seeder if called directly
if (require.main === module) {
  seedRoles();
}

export default seedRoles;
