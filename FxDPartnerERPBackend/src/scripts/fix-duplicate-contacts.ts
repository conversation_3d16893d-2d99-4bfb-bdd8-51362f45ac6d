import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'fxd_partner_erp_backend',
};

async function fixDuplicateContacts() {
  let connection;
  
  try {
    console.log('🔗 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connected successfully');

    // First, let's identify the duplicate records
    console.log('\n🔍 Identifying duplicate contact records...');
    const [duplicates] = await connection.execute(`
      SELECT organization_id, contact, COUNT(*) as count 
      FROM customers 
      WHERE organization_id = 'default-org-id' AND contact = '' 
      GROUP BY organization_id, contact 
      HAVING COUNT(*) > 1
    `);

    if (Array.isArray(duplicates) && duplicates.length > 0) {
      console.log(`Found ${duplicates.length} duplicate contact groups`);
      
      // Get all records with empty contacts in default-org-id
      const [emptyContacts] = await connection.execute(`
        SELECT id, organization_id, contact, name 
        FROM customers 
        WHERE organization_id = 'default-org-id' AND contact = '' 
        ORDER BY id
      `);

      if (Array.isArray(emptyContacts) && emptyContacts.length > 0) {
        console.log(`\n📋 Found ${emptyContacts.length} customers with empty contacts:`);
        emptyContacts.forEach((customer: any) => {
          console.log(`  - ID: ${customer.id}, Name: ${customer.name || 'N/A'}`);
        });

        // Update empty contact fields with unique values
        console.log('\n🔄 Updating empty contact fields with unique values...');
        
        // Since contact field is VARCHAR(20), we need shorter unique values
        // We'll use a counter approach to create unique short identifiers
        let counter = 1;
        for (const customer of emptyContacts as any[]) {
          const uniqueContact = `no-contact-${counter}`;
          await connection.execute(`
            UPDATE customers 
            SET contact = ? 
            WHERE id = ?
          `, [uniqueContact, customer.id]);
          console.log(`  Updated customer ${customer.id} with contact: ${uniqueContact}`);
          counter++;
        }
        
        console.log(`✅ Updated ${emptyContacts.length} customer records`);

        // Verify the fix
        console.log('\n🔍 Verifying fix - checking for remaining duplicates...');
        const [remainingDuplicates] = await connection.execute(`
          SELECT organization_id, contact, COUNT(*) as count 
          FROM customers 
          GROUP BY organization_id, contact 
          HAVING COUNT(*) > 1
        `);

        if (Array.isArray(remainingDuplicates) && remainingDuplicates.length === 0) {
          console.log('✅ No duplicate contacts found - fix successful!');
        } else {
          console.log('⚠️  Some duplicates still exist:');
          console.log(remainingDuplicates);
        }

        // Show updated records
        console.log('\n📋 Updated customer records:');
        const [updatedRecords] = await connection.execute(`
          SELECT id, organization_id, contact, name 
          FROM customers 
          WHERE organization_id = 'default-org-id' AND contact LIKE 'no-contact-%' 
          ORDER BY id
        `);

        if (Array.isArray(updatedRecords)) {
          updatedRecords.forEach((customer: any) => {
            console.log(`  - ID: ${customer.id}, Contact: ${customer.contact}, Name: ${customer.name || 'N/A'}`);
          });
        }
      } else {
        console.log('No customers with empty contacts found');
      }
    } else {
      console.log('No duplicate contact records found');
    }

  } catch (error) {
    console.error('❌ Error fixing duplicate contacts:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the fix
fixDuplicateContacts()
  .then(() => {
    console.log('\n🎉 Duplicate contact fix completed successfully!');
    console.log('You can now try running the backend server again.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Failed to fix duplicate contacts:', error);
    process.exit(1);
  });
