import { Request, Response, NextFunction } from 'express';
import { User } from '../models/User';
import { Page } from '../models/Page';
import { UserPagePermission } from '../models/UserPagePermission';

interface AuthenticatedRequest extends Request {
  user?: User;
  userId?: string;
  organizationId?: string;
  userPagePermissions?: { pageName: string; canView: boolean; canEdit: boolean }[];
}

/**
 * Get user page permissions (NEW: Page-based permission system)
 */
export const getUserPagePermissions = async (userId: string): Promise<{ pageName: string; canView: boolean; canEdit: boolean }[]> => {
  try {
    const userPagePermissions = await UserPagePermission.findAll({
      where: {
        user_id: userId
      },
      include: [
        {
          model: Page,
          where: { is_active: true }
        }
      ]
    });

    return userPagePermissions.map(permission => ({
      pageName: permission.page.name,
      canView: permission.can_view,
      canEdit: permission.can_edit
    }));
  } catch (error) {
    console.error('Error getting user page permissions:', error);
    return [];
  }
};

/**
 * Check if user can view specific page
 */
export const canViewPage = async (userId: string, pageName: string): Promise<boolean> => {
  try {
    const permission = await UserPagePermission.findOne({
      where: {
        user_id: userId,
        can_view: true
      },
      include: [
        {
          model: Page,
          where: { 
            name: pageName,
            is_active: true 
          }
        }
      ]
    });

    return !!permission;
  } catch (error) {
    console.error('Error checking page view permission:', error);
    return false;
  }
};

/**
 * Check if user can edit specific page
 */
export const canEditPage = async (userId: string, pageName: string): Promise<boolean> => {
  try {
    const permission = await UserPagePermission.findOne({
      where: {
        user_id: userId,
        can_edit: true
      },
      include: [
        {
          model: Page,
          where: { 
            name: pageName,
            is_active: true 
          }
        }
      ]
    });

    return !!permission;
  } catch (error) {
    console.error('Error checking page edit permission:', error);
    return false;
  }
};

/**
 * NEW: Middleware to load user page permissions
 */
export const loadUserPagePermissions = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.userId) {
      return next();
    }

    // Load user page permissions
    const pagePermissions = await getUserPagePermissions(req.userId);
    
    // Store page permissions in request for easy access
    req.userPagePermissions = pagePermissions;
    
    console.log("User page permissions loaded:", pagePermissions);

    next();
  } catch (error) {
    console.error('Error loading user page permissions:', error);
    next();
  }
};


/**
 * NEW: Middleware factory to require page view permission
 */
export const requirePageView = (pageName: string) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTHENTICATION_REQUIRED'
      });
    }

    const hasViewPermission = await canViewPage(req.userId, pageName);
    
    if (!hasViewPermission) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Cannot view page: ${pageName}`,
        code: 'PAGE_VIEW_DENIED'
      });
    }
    
    next();
  };
};

/**
 * NEW: Middleware factory to require page edit permission
 */
export const requirePageEdit = (pageName: string) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTHENTICATION_REQUIRED'
      });
    }

    const hasEditPermission = await canEditPage(req.userId, pageName);
    
    if (!hasEditPermission) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Cannot edit page: ${pageName}`,
        code: 'PAGE_EDIT_DENIED'
      });
    }
    
    next();
  };
};

/**
 * TEMPORARY: Role-based functions for backward compatibility
 * These will be removed once all routes are converted to page-based permissions
 */
export const loadUserPermissions = loadUserPagePermissions; // Alias for compatibility

export const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    // For now, just pass through - this is a temporary compatibility layer
    // In a real implementation, you would check against user's role permissions
    console.warn(`Role-based permission check for: ${permission} - This should be converted to page-based permissions`);
    next();
  };
};

export const requireAnyRole = (roles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    // For now, just pass through - this is a temporary compatibility layer
    console.warn(`Role-based role check for: ${roles.join(', ')} - This should be converted to page-based permissions`);
    next();
  };
};

// TEMPORARY: Backward compatibility functions for authController
export const getUserPermissions = async (userId: string, organizationId: string): Promise<string[]> => {
  // For now, return empty array - this should be replaced with actual role-based logic if needed
  console.warn('getUserPermissions called - this is deprecated, use page-based permissions instead');
  return [];
};

export const getUserRoles = async (userId: string, organizationId: string): Promise<string[]> => {
  // For now, return empty array - this should be replaced with actual role-based logic if needed
  console.warn('getUserRoles called - this is deprecated, use page-based permissions instead');
  return [];
};

export const getUserPages = async (userId: string, organizationId: string): Promise<string[]> => {
  // For now, return empty array - this should be replaced with actual role-based logic if needed
  console.warn('getUserPages called - this is deprecated, use page-based permissions instead');
  return [];
};

// Export types for use in other files
export type { AuthenticatedRequest };
