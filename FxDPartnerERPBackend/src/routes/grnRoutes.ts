import { Router } from 'express';
import {
  markGRNComplete,
  createReturnPDDRequest,
  getGRNHistory,
  getGRNInventoryTransactions,
  getProductInventoryTransactions
} from '../controllers/grnController';
import { authenticateToken } from '../middleware/auth';
import { 
  loadUserPagePermissions, 
  requirePageView, 
  requirePageEdit
} from '../middleware/permissions';

const router = Router();

// Apply authentication and page-based permission loading to all routes
router.use(authenticateToken);
router.use(loadUserPagePermissions);

// Page-based permission routes for GRN (Goods Receipt Note)
// Page name: 'pending_approvals'

// Mark sales order as GRN Complete
router.patch('/sales-orders/:id/grn-complete', requirePageEdit('pending_approvals'), markGRNComplete);

// Create Return/PDD request
router.post('/return-pdd-requests', requirePageEdit('pending_approvals'), createReturnPDDRequest);

// Get GRN history for a sales order
router.get('/sales-orders/:salesOrderId/history', requirePageView('pending_approvals'), getGRNHistory);

// Get inventory transactions for a specific GRN request
router.get('/requests/:grnRequestId/inventory-transactions', requirePageView('pending_approvals'), getGRNInventoryTransactions);

// Get inventory transaction history for a product/SKU
router.get('/inventory-transactions/:productId/:skuId', requirePageView('pending_approvals'), getProductInventoryTransactions);

export default router;
