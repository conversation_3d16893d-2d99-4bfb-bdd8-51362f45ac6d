import { Router } from 'express';
import { DocumentController, uploadSingle, uploadMultiple } from '../controllers/documentController';
import { authenticateToken, extractOrganization } from '../middleware/auth';

const router = Router();

// Apply authentication to all routes
router.use(authenticateToken);
router.use(extractOrganization);

// Upload routes
router.post('/upload', uploadSingle, DocumentController.uploadFile);
router.post('/upload/multiple', uploadMultiple, DocumentController.uploadMultipleFiles);

// Document management routes
router.post('/:entityType/:entityId', DocumentController.attachDocument);
router.get('/:entityType/:entityId', DocumentController.getEntityDocuments);
router.delete('/:id', DocumentController.deleteDocument);

// Search and organization routes
router.get('/search', DocumentController.searchDocuments);
router.get('/organization/all', DocumentController.getOrganizationDocuments);

export default router;
