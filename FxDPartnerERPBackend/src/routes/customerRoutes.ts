import { Router } from 'express';
import {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  updateCustomerBalance,
  getSalesOrdersByCustomerId,
  getPaymentsByCustomerId,
  getCustomersWithStats,
  searchCustomers
} from '../controllers/customerController';
import { authenticateToken } from '../middleware/auth';
import { 
  loadUserPagePermissions, 
  requirePageView, 
  requirePageEdit
} from '../middleware/permissions';

const router = Router();

// Apply authentication and page-based permission loading to all routes
router.use(authenticateToken);
router.use(loadUserPagePermissions);

// Page-based permission routes for customers
// Page name: 'customers'

// Customer routes
router.get('/', requirePageView('customers'), getCustomers);
router.get('/search', requirePageView('customers'), searchCustomers);
router.get('/stats', requirePageView('customers'), getCustomersWithStats);
router.get('/:id', requirePageView('customers'), getCustomer);
router.post('/', requirePageEdit('customers'), createCustomer);
router.put('/:id', requirePageEdit('customers'), updateCustomer);
router.delete('/:id', requirePageEdit('customers'), deleteCustomer);

// Customer balance management
router.patch('/:id/balance', requirePageEdit('customers'), updateCustomerBalance);

// Customer related data
router.get('/:id/sales-orders', requirePageView('customers'), getSalesOrdersByCustomerId);
router.get('/:id/payments', requirePageView('customers'), getPaymentsByCustomerId);

export default router;
