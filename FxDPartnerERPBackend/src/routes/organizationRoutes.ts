import { Router } from 'express';
import { getCurrentOrganization, updateCurrentOrganization } from '../controllers/organizationController';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Organization routes
router.get('/current', getCurrentOrganization);
router.put('/current', updateCurrentOrganization);

export default router;
