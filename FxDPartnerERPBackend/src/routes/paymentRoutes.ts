import { Router } from 'express';
import multer from 'multer';
import {
  getPayments,
  getPayment,
  createPayment,
  updatePayment,
  deletePayment,
  getPaymentsByPartyId,
  getPaymentsByReferenceId,
  getCustomerCreditExtensions,
  createCustomerCreditExtension,
  updateCustomerCreditExtension,
  getAllCreditExtensions,
  searchPayments,
  getPaymentStatistics,
  getPaymentDocuments
} from '../controllers/paymentController';
import { authenticateToken } from '../middleware/auth';
import { 
  loadUserPagePermissions, 
  requirePageView, 
  requirePageEdit
} from '../middleware/permissions';

// Configure multer for handling FormData
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow images and PDFs
    if (file.mimetype.startsWith('image/') || file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only image and PDF files are allowed'));
    }
  }
});

const router = Router();

// Apply authentication and page-based permission loading to all routes
router.use(authenticateToken);
router.use(loadUserPagePermissions);

// Page-based permission routes for payments
// Page name: 'payments'

// Payment routes
router.get('/', requirePageView('payments'), getPayments);
router.get('/search', requirePageView('payments'), searchPayments);
router.get('/statistics', requirePageView('payments'), getPaymentStatistics);
router.get('/reference/:referenceId', requirePageView('payments'), getPaymentsByReferenceId);
router.get('/party/:partyId', requirePageView('payments'), getPaymentsByPartyId);
router.get('/customer/:customerId', requirePageView('payments'), getPaymentsByPartyId); // Alias for backward compatibility
router.get('/supplier/:supplierId', requirePageView('payments'), getPaymentsByPartyId); // Alias for backward compatibility
router.get('/:id/documents', requirePageView('payments'), getPaymentDocuments);
router.get('/:id', requirePageView('payments'), getPayment);
router.post('/', requirePageEdit('payments'), createPayment);
router.put('/:id', requirePageEdit('payments'), updatePayment);
router.delete('/:id', requirePageEdit('payments'), deletePayment);

// Customer credit extension routes
router.get('/credit-extensions/all', requirePageView('payments'), getAllCreditExtensions);
router.get('/credit-extensions/customer/:customerId', requirePageView('payments'), getCustomerCreditExtensions);
router.post('/credit-extensions', requirePageEdit('payments'), createCustomerCreditExtension);
router.put('/credit-extensions/:id', requirePageEdit('payments'), updateCustomerCreditExtension);

export default router;
