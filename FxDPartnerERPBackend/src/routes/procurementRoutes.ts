import express from 'express';
const router = express.Router();
import { authenticateToken } from '../middleware/auth';
import { loadUserPagePermissions } from '../middleware/permissions';

// Import sub-route modules
import vehicleArrivalRoutes from './vehicleArrivalRoutes';
import purchaseRecordRoutes from './purchaseRecordRoutes';

// Apply authentication and page-based permission loading to all procurement routes
router.use(authenticateToken);
router.use(loadUserPagePermissions);

// Mount sub-routes
router.use('/vehicle-arrivals', vehicleArrivalRoutes);
router.use('/purchase-records', purchaseRecordRoutes);

export default router;
