import { Router } from 'express';
import {
  getRoles,
  getSystemRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  getRoleUsers,
  assignRoleToUser,
  removeRoleFromUser,
  getAvailablePermissions
} from '../controllers/roleController';
import { authenticateToken } from '../middleware/auth';
import { 
  loadUserPagePermissions, 
  requirePageView, 
  requirePageEdit
} from '../middleware/permissions';

const router = Router();

// Apply authentication and page-based permission loading to all routes
router.use(authenticateToken);
router.use(loadUserPagePermissions);

// Page-based permission routes for roles
// Page name: 'settings'

/**
 * @route GET /api/roles
 * @desc Get all roles for the organization
 * @access Private - Requires settings page view permission
 */
router.get('/', requirePageView('settings'), getRoles);

/**
 * @route GET /api/roles/system
 * @desc Get system roles (organization-independent)
 * @access Private - Requires settings page view permission
 */
router.get('/system', requirePageView('settings'), getSystemRoles);

/**
 * @route GET /api/roles/permissions
 * @desc Get available permissions
 * @access Private - Requires settings page view permission
 */
router.get('/permissions', requirePageView('settings'), getAvailablePermissions);

/**
 * @route GET /api/roles/:id
 * @desc Get role by ID
 * @access Private - Requires settings page view permission
 */
router.get('/:id', requirePageView('settings'), getRoleById);

/**
 * @route POST /api/roles
 * @desc Create new role
 * @access Private - Requires settings page edit permission
 */
router.post('/', requirePageEdit('settings'), createRole);

/**
 * @route PUT /api/roles/:id
 * @desc Update role
 * @access Private - Requires settings page edit permission
 */
router.put('/:id', requirePageEdit('settings'), updateRole);

/**
 * @route DELETE /api/roles/:id
 * @desc Delete role
 * @access Private - Requires settings page edit permission
 */
router.delete('/:id', requirePageEdit('settings'), deleteRole);

/**
 * @route GET /api/roles/:id/users
 * @desc Get users assigned to a role
 * @access Private - Requires settings page view permission
 */
router.get('/:id/users', requirePageView('settings'), getRoleUsers);

/**
 * @route POST /api/roles/:id/assign
 * @desc Assign role to user
 * @access Private - Requires settings page edit permission
 */
router.post('/:id/assign', requirePageEdit('settings'), assignRoleToUser);

/**
 * @route POST /api/roles/:id/remove
 * @desc Remove role from user
 * @access Private - Requires settings page edit permission
 */
router.post('/:id/remove', requirePageEdit('settings'), removeRoleFromUser);

export default router;
