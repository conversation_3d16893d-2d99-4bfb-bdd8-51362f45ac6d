import { Router } from 'express';
import { DashboardController } from '../controllers/dashboardController';
import { authenticateToken } from '../middleware/auth';
import { 
  loadUserPagePermissions, 
  requirePageView
} from '../middleware/permissions';

const router = Router();

// Apply authentication and page-based permission loading to all dashboard routes
router.use(authenticateToken);
router.use(loadUserPagePermissions);

// Page-based permission routes for dashboard
// Page name: 'dashboard'

// Simple Dashboard Views
router.get('/customer-supplier', requirePageView('dashboard'), DashboardController.getCustomerSupplierView);
router.get('/arrivals', requirePageView('dashboard'), DashboardController.getArrivalView);
router.get('/inventory', requirePageView('dashboard'), DashboardController.getInventoryView);
router.get('/sales-payment', requirePageView('dashboard'), DashboardController.getSalesPaymentView);
router.get('/purchase-payment', requirePageView('dashboard'), DashboardController.getPurchasePaymentView);

// Legacy routes for backward compatibility
router.get('/financial-metrics', requirePageView('dashboard'), DashboardController.getFinancialMetrics);
router.get('/operational-metrics', requirePageView('dashboard'), DashboardController.getOperationalMetrics);
router.get('/customer-insights', requirePageView('dashboard'), DashboardController.getCustomerInsights);
router.get('/alerts', requirePageView('dashboard'), DashboardController.getCriticalAlerts);

export default router;
