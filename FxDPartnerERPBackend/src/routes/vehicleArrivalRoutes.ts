import express from 'express';
const router = express.Router();
import {
  getVehicleArrivals,
  getVehicleArrival,
  createVehicleArrival,
  updateVehicleArrival,
  updateVehicleArrivalStatus,
  deleteVehicleArrival
} from '../controllers/vehicleArrivalController';
import { authenticateToken } from '../middleware/auth';
import { 
  loadUserPagePermissions, 
  requirePageView, 
  requirePageEdit
} from '../middleware/permissions';

// Apply authentication and page-based permission loading to all routes
router.use(authenticateToken);
router.use(loadUserPagePermissions);

// Page-based permission routes for vehicle arrivals
// Page name: 'vehicle_arrival'

// GET /api/procurement/vehicle-arrivals - Get all vehicle arrivals
router.get('/', requirePageView('vehicle_arrival'), getVehicleArrivals);

// GET /api/procurement/vehicle-arrivals/:id - Get single vehicle arrival
router.get('/:id', requirePageView('vehicle_arrival'), getVehicleArrival);

// POST /api/procurement/vehicle-arrivals - Create new vehicle arrival
router.post('/', requirePageEdit('vehicle_arrival'), createVehicleArrival);

// PUT /api/procurement/vehicle-arrivals/:id - Update vehicle arrival
router.put('/:id', requirePageEdit('vehicle_arrival'), updateVehicleArrival);

// PATCH /api/procurement/vehicle-arrivals/:id/status - Update vehicle arrival status
router.patch('/:id/status', requirePageEdit('vehicle_arrival'), updateVehicleArrivalStatus);

// DELETE /api/procurement/vehicle-arrivals/:id - Delete vehicle arrival
router.delete('/:id', requirePageEdit('vehicle_arrival'), deleteVehicleArrival);

export default router;
