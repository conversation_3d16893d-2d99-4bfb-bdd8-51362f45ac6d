import { Router } from 'express';
import {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  updateCustomerBalance,
  getSalesOrdersByCustomerId,
  getPaymentsByCustomerId
} from '../controllers/customerController';
import {
  getSuppliers,
  getSupplier,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  updateSupplierBalance,
  getPurchaseRecordsBySupplierId,
  getPaymentsBySupplierId
} from '../controllers/supplierController';
import { authenticateToken } from '../middleware/auth';
import { 
  loadUserPagePermissions, 
  requirePageView, 
  requirePageEdit
} from '../middleware/permissions';

const router = Router();

// Apply authentication and page-based permission loading to all routes
router.use(authenticateToken);
router.use(loadUserPagePermissions);

// Page-based permission routes for partners
// Customer routes - Page name: 'customers'
router.get('/customers', requirePageView('customers'), getCustomers);
router.get('/customers/:id', requirePageView('customers'), getCustomer);
router.post('/customers', requirePageEdit('customers'), createCustomer);
router.put('/customers/:id', requirePageEdit('customers'), updateCustomer);
router.delete('/customers/:id', requirePageEdit('customers'), deleteCustomer);
router.patch('/customers/:id/balance', requirePageEdit('customers'), updateCustomerBalance);
router.get('/customers/:id/sales-orders', requirePageView('customers'), getSalesOrdersByCustomerId);
router.get('/customers/:id/payments', requirePageView('customers'), getPaymentsByCustomerId);

// Supplier routes - Page name: 'suppliers'
router.get('/suppliers', requirePageView('suppliers'), getSuppliers);
router.get('/suppliers/:id', requirePageView('suppliers'), getSupplier);
router.post('/suppliers', requirePageEdit('suppliers'), createSupplier);
router.put('/suppliers/:id', requirePageEdit('suppliers'), updateSupplier);
router.delete('/suppliers/:id', requirePageEdit('suppliers'), deleteSupplier);
router.patch('/suppliers/:id/balance', requirePageEdit('suppliers'), updateSupplierBalance);
router.get('/suppliers/:id/purchase-records', requirePageView('suppliers'), getPurchaseRecordsBySupplierId);
router.get('/suppliers/:id/payments', requirePageView('suppliers'), getPaymentsBySupplierId);

export default router;
