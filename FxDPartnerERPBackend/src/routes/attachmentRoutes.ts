import { Router } from 'express';
import { DocumentController, uploadSingle, uploadMultiple } from '../controllers/documentController';
import { authenticateToken, extractOrganization } from '../middleware/auth';

const router = Router();

// Apply authentication to all routes
router.use(authenticateToken);
router.use(extractOrganization);

// Attachment routes (aliases for document routes)
router.post('/upload', uploadSingle, DocumentController.uploadFile);
router.post('/upload/multiple', uploadMultiple, DocumentController.uploadMultipleFiles);

// Document management routes with attachment-style paths
router.post('/:entityType/:entityId', DocumentController.attachDocument);
router.get('/:entityType/:entityId', DocumentController.getEntityDocuments);
router.delete('/:id', DocumentController.deleteDocument);

// Search and organization routes
router.get('/search', DocumentController.searchDocuments);
router.get('/organization/all', DocumentController.getOrganizationDocuments);

// Download route - proxy S3 files with proper download headers
router.get('/download/:id', DocumentController.downloadDocument);

export default router;
