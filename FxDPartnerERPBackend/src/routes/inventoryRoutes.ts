import { Router } from 'express';
import {
  getAllInventory,
  getAvailableInventory,
  getInventoryItem,
  createInventoryItem,
  updateInventoryItem,
  adjustInventory,
  adjustInventoryAsAnotherSKU,
  deleteInventoryItem,
  searchInventory,
  checkInventoryForSalesOrder,
  getInventoryHistory,
  markInventoryAsDump,
  updateSKUDetails,
  manualInventoryAdjustment,
  getInventoryDumps,
  getInventoryAdjustmentReports
} from '../controllers/inventoryController';
import { authenticateToken } from '../middleware/auth';
import { 
  loadUserPagePermissions, 
  requirePageView, 
  requirePageEdit
} from '../middleware/permissions';

const router = Router();

// Apply authentication and page-based permission loading to all routes
router.use(authenticateToken);
router.use(loadUserPagePermissions);

// Page-based permission routes for inventory
// Page name: 'inventory'

// Inventory routes
router.get('/', requirePageView('inventory'), getAllInventory);
router.get('/available', requirePageView('inventory'), getAvailableInventory);
router.get('/search', requirePageView('inventory'), searchInventory);
router.post('/check-for-sales-order', requirePageView('inventory'), checkInventoryForSalesOrder);

// Inventory reports routes (must come before parameterized routes)
router.get('/dumps', requirePageView('inventory'), getInventoryDumps);
router.get('/adjustments/report', requirePageView('inventory'), getInventoryAdjustmentReports);
router.get('/history/:productId/:skuId', requirePageView('inventory'), getInventoryHistory);

// Parameterized routes should come last
router.get('/:id', requirePageView('inventory'), getInventoryItem);
router.post('/', requirePageEdit('inventory'), createInventoryItem);
router.put('/:id', requirePageEdit('inventory'), updateInventoryItem);
router.delete('/:id', requirePageEdit('inventory'), deleteInventoryItem);

// Inventory adjustment routes
router.post('/adjust', requirePageEdit('inventory'), adjustInventory);
router.post('/adjust-as-another-sku', requirePageEdit('inventory'), adjustInventoryAsAnotherSKU);

// New inventory adjustment routes
router.post('/mark-dump', requirePageEdit('inventory'), markInventoryAsDump);
router.post('/update-sku', requirePageEdit('inventory'), updateSKUDetails);
router.post('/manual-adjust', requirePageEdit('inventory'), manualInventoryAdjustment);

export default router;
