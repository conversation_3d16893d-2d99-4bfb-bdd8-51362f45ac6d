import { Router } from 'express';
import {
  getSuppliers,
  getSupplier,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  updateSupplierBalance,
  getPurchaseRecordsBySupplierId,
  getPaymentsBySupplierId,
  getSuppliersWithStats,
  searchSuppliers
} from '../controllers/supplierController';
import { authenticateToken } from '../middleware/auth';
import { 
  loadUserPagePermissions, 
  requirePageView, 
  requirePageEdit
} from '../middleware/permissions';

const router = Router();

// Apply authentication and page-based permission loading to all routes
router.use(authenticateToken);
router.use(loadUserPagePermissions);

// Page-based permission routes for suppliers
// Page name: 'suppliers'

// Supplier routes
router.get('/', requirePageView('suppliers'), getSuppliers);
router.get('/search', requirePageView('suppliers'), searchSuppliers);
router.get('/stats', requirePageView('suppliers'), getSuppliersWithStats);
router.get('/:id', requirePageView('suppliers'), getSupplier);
router.post('/', requirePageEdit('suppliers'), createSupplier);
router.put('/:id', requirePageEdit('suppliers'), updateSupplier);
router.delete('/:id', requirePageEdit('suppliers'), deleteSupplier);

// Supplier balance management
router.put('/:id/balance', requirePageEdit('suppliers'), updateSupplierBalance);
router.patch('/:id/balance', requirePageEdit('suppliers'), updateSupplierBalance);

// Supplier related data
router.get('/:id/purchase-records', requirePageView('suppliers'), getPurchaseRecordsBySupplierId);
router.get('/:id/payments', requirePageView('suppliers'), getPaymentsBySupplierId);

export default router;
