import { Router } from 'express';
import {
  getPurchaseRecords,
  getPurchaseRecord,
  createPurchaseRecord,
  updatePurchaseRecord,
  deletePurchaseRecord,
  updatePurchaseRecordClosureStatus,
  getPurchaseRecordsBySupplierId,
  searchPurchaseRecords,
  getPurchaseRecordStatistics
} from '../controllers/purchaseRecordController';
import { authenticateToken } from '../middleware/auth';
import { 
  loadUserPagePermissions, 
  requirePageView, 
  requirePageEdit
} from '../middleware/permissions';

const router = Router();

// Apply authentication and page-based permission loading to all routes
router.use(authenticateToken);
router.use(loadUserPagePermissions);

// Page-based permission routes for purchase records
// Page name: 'purchase-records'
router.get('/', requirePageView('record_purchase'), getPurchaseRecords);
router.get('/search', requirePageView('record_purchase'), searchPurchaseRecords);
router.get('/statistics', requirePageView('record_purchase'), getPurchaseRecordStatistics);
router.get('/supplier/:supplierId', requirePageView('record_purchase'), getPurchaseRecordsBySupplierId);
router.get('/:id', requirePageView('record_purchase'), getPurchaseRecord);
router.post('/', requirePageEdit('record_purchase'), createPurchaseRecord);
router.put('/:id', requirePageEdit('record_purchase'), updatePurchaseRecord);
router.delete('/:id', requirePageEdit('record_purchase'), deletePurchaseRecord);

// Purchase record closure management
router.patch('/:id/closure-status', requirePageEdit('record_purchase'), updatePurchaseRecordClosureStatus);
router.put('/:id/closure-status', requirePageEdit('record_purchase'), updatePurchaseRecordClosureStatus);

export default router;
