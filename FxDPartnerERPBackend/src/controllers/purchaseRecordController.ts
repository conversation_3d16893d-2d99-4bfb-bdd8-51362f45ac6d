import { Request, Response } from 'express';
import { Transaction, Op } from 'sequelize';
import sequelize from '../config/sequelize';
import { PurchaseRecord, PurchaseRecordItem, PurchaseRecordCost, CreatePurchaseRecordData, UpdatePurchaseRecordData, CreatePurchaseRecordItemData, CreatePurchaseRecordCostData } from '../models/PurchaseRecord';
import { Organization } from '../models/Organization';
import { Supplier } from '../models/Supplier';
import { VehicleArrival } from '../models/VehicleArrival';

// Get all purchase records
export const getPurchaseRecords = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const { startDate, endDate } = req.query;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Build where clause with date filtering
    const whereClause: any = { organization_id: organizationId };
    
    if (startDate && endDate) {
      // Make end date inclusive by adding one day and using less than
      const inclusiveEndDate = new Date(endDate as string);
      inclusiveEndDate.setDate(inclusiveEndDate.getDate() + 1);
      
      whereClause.record_date = {
        [Op.gte]: startDate,
        [Op.lt]: inclusiveEndDate.toISOString().split('T')[0]
      };
    } else if (startDate) {
      whereClause.record_date = {
        [Op.gte]: startDate
      };
    } else if (endDate) {
      // Make end date inclusive by adding one day and using less than
      const inclusiveEndDate = new Date(endDate as string);
      inclusiveEndDate.setDate(inclusiveEndDate.getDate() + 1);
      
      whereClause.record_date = {
        [Op.lt]: inclusiveEndDate.toISOString().split('T')[0]
      };
    }

    const purchaseRecords = await PurchaseRecord.findAll({
      where: whereClause,
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        },
        {
          model: Supplier,
          as: 'supplierDetails',
          attributes: ['id', 'company_name', 'contact_person']
        },
        {
          model: VehicleArrival,
          attributes: ['id', 'vehicle_number', 'arrival_time']
        },
        {
          model: PurchaseRecordItem,
          as: 'items'
        },
        {
          model: PurchaseRecordCost,
          as: 'costs'
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json(purchaseRecords);
  } catch (error) {
    console.error('Error fetching purchase records:', error);
    res.status(500).json({ error: 'Failed to fetch purchase records' });
  }
};

// Get single purchase record
export const getPurchaseRecord = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const purchaseRecord = await PurchaseRecord.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        },
        {
          model: Supplier,
          attributes: ['id', 'company_name', 'contact_person', 'phone', 'email']
        },
        {
          model: VehicleArrival,
          attributes: ['id', 'vehicle_number', 'arrival_time', 'driver_name']
        },
        {
          model: PurchaseRecordItem,
          as: 'items'
        },
        {
          model: PurchaseRecordCost,
          as: 'costs'
        }
      ]
    });

    if (!purchaseRecord) {
      return res.status(404).json({ error: 'Purchase record not found' });
    }

    res.json(purchaseRecord);
  } catch (error) {
    console.error('Error fetching purchase record:', error);
    res.status(500).json({ error: 'Failed to fetch purchase record' });
  }
};

// Create purchase record
export const createPurchaseRecord = async (req: Request, res: Response) => {
  console.log('=== CREATE PURCHASE RECORD REQUEST RECEIVED ===');
  console.log('Request method:', req.method);
  console.log('Request URL:', req.url);
  console.log('Request path:', req.path);
  
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    console.log('Headers received:', req.headers);
    console.log('Organization ID from header:', organizationId);
    console.log('Organization ID type:', typeof organizationId);
    console.log('Organization ID length:', organizationId?.length);
    
    if (!organizationId || organizationId.trim() === '') {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const { recordData, itemsData, additionalCostsData } = req.body;
    console.log("Record data received:", recordData);
    console.log("Organization ID to use:", organizationId);
    const finalRecordData: CreatePurchaseRecordData = {
      ...recordData,
      organization_id: organizationId
    };

    // Auto-generate record number if not provided
    if (!finalRecordData.record_number || finalRecordData.record_number.trim() === '') {
      const currentYear = new Date().getFullYear();
      const randomNumber = Math.floor(100000 + Math.random() * 900000); // 6-digit random number
      finalRecordData.record_number = `PR-${currentYear}-${randomNumber}`;
    }

    // Map pricing model values to match database enum
    if (finalRecordData.pricing_model === 'fixed') {
      finalRecordData.pricing_model = 'fixed_price';
    }
    if (finalRecordData.pricing_model === 'commission') {
      finalRecordData.pricing_model = 'commission_based';
    }

    // Set arrival_timestamp if not provided but arrival_timestamp is in the data
    if (!finalRecordData.arrival_timestamp && recordData.arrival_timestamp) {
      finalRecordData.arrival_timestamp = new Date(recordData.arrival_timestamp);
    }

    // Validate required fields
    if (!finalRecordData.record_date) {
      return res.status(400).json({ 
        error: 'Record date is required' 
      });
    }

    // Validate organization exists
    const organization = await Organization.findByPk(organizationId);
    if (!organization) {
      return res.status(400).json({ error: 'Invalid organization ID' });
    }

    // Validate supplier if provided
    if (finalRecordData.supplier_id) {
      const supplier = await Supplier.findOne({
        where: { 
          id: finalRecordData.supplier_id,
          organization_id: organizationId 
        }
      });
      if (!supplier) {
        return res.status(400).json({ error: 'Invalid supplier ID' });
      }
    }

    // Validate vehicle arrival if provided
    if (finalRecordData.vehicle_arrival_id) {
      const vehicleArrival = await VehicleArrival.findOne({
        where: { 
          id: finalRecordData.vehicle_arrival_id,
          organization_id: organizationId 
        }
      });
      if (!vehicleArrival) {
        return res.status(400).json({ error: 'Invalid vehicle arrival ID' });
      }
    }

    // Create purchase record
    console.log('Creating purchase record with data:', JSON.stringify(finalRecordData, null, 2));
    console.log('Final organization_id being used:', finalRecordData.organization_id);
    
    const purchaseRecord = await PurchaseRecord.create(finalRecordData as any, { transaction });
    console.log('Purchase record created successfully:', purchaseRecord.id);

    // Create items if provided
    if (itemsData && Array.isArray(itemsData) && itemsData.length > 0) {
      console.log('Creating purchase record items...');
      const itemsToCreate = itemsData.map((item: CreatePurchaseRecordItemData) => ({
        ...item,
        purchase_record_id: purchaseRecord.id,
        organization_id: organizationId
      }));

      console.log('Items to create:', JSON.stringify(itemsToCreate, null, 2));
      console.log('Organization ID for items:', organizationId);
      
      try {
        await PurchaseRecordItem.bulkCreate(itemsToCreate, { transaction });
        console.log('Purchase record items created successfully');
      } catch (itemError) {
        console.error('Error creating purchase record items:', itemError);
        throw itemError;
      }
    }

    // Create costs if provided
    if (additionalCostsData && Array.isArray(additionalCostsData) && additionalCostsData.length > 0) {
      const costsToCreate = additionalCostsData.map((cost: CreatePurchaseRecordCostData) => ({
        ...cost,
        purchase_record_id: purchaseRecord.id,
        organization_id: organizationId
      }));

      await PurchaseRecordCost.bulkCreate(costsToCreate, { transaction });
    }

    // Update supplier balance if supplier_id and total_amount are provided
    if (finalRecordData.supplier_id && finalRecordData.total_amount) {
      await Supplier.increment('current_balance', {
        by: finalRecordData.total_amount,
        where: { id: finalRecordData.supplier_id },
        transaction
      });
    }

    // Update vehicle arrival status if vehicle_arrival_id is provided
    if (finalRecordData.vehicle_arrival_id) {
      await VehicleArrival.update(
        { 
          status: 'po_created',
          updated_at: new Date()
        },
        { 
          where: { id: finalRecordData.vehicle_arrival_id },
          transaction 
        }
      );
    }

    await transaction.commit();

    // Fetch the created record with associations
    const createdRecord = await PurchaseRecord.findByPk(purchaseRecord.id, {
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        },
        {
          model: Supplier,
          attributes: ['id', 'company_name', 'contact_person']
        },
        {
          model: VehicleArrival,
          attributes: ['id', 'vehicle_number', 'arrival_time']
        },
        {
          model: PurchaseRecordItem,
          as: 'items'
        },
        {
          model: PurchaseRecordCost,
          as: 'costs'
        }
      ]
    });

    res.status(201).json(createdRecord);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating purchase record:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      requestBody: req.body
    });
    res.status(500).json({ 
      error: 'Failed to create purchase record',
      details: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : 'Unknown error') : undefined
    });
  }
};

// Update purchase record
export const updatePurchaseRecord = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const { recordData, itemsData, additionalCostsData } = req.body;

    // Find purchase record
    const purchaseRecord = await PurchaseRecord.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      transaction
    });

    if (!purchaseRecord) {
      return res.status(404).json({ error: 'Purchase record not found' });
    }

    const updateData: UpdatePurchaseRecordData = recordData;

    // Handle supplier balance updates for amount changes
    if (updateData.total_amount !== undefined && updateData.supplier_id) {
      const currentAmount = purchaseRecord.total_amount || 0;
      const amountDifference = updateData.total_amount - currentAmount;
      
      if (amountDifference !== 0) {
        if (amountDifference > 0) {
          await Supplier.increment('current_balance', {
            by: amountDifference,
            where: { id: updateData.supplier_id },
            transaction
          });
        } else {
          await Supplier.decrement('current_balance', {
            by: Math.abs(amountDifference),
            where: { id: updateData.supplier_id },
            transaction
          });
        }
      }
    }

    // Update purchase record
    await purchaseRecord.update(updateData, { transaction });

    // Update items if provided
    if (itemsData && Array.isArray(itemsData)) {
      // Delete existing items
      await PurchaseRecordItem.destroy({
        where: { purchase_record_id: id },
        transaction
      });

      // Create new items
      if (itemsData.length > 0) {
        const itemsToCreate = itemsData.map((item: CreatePurchaseRecordItemData) => ({
          ...item,
          purchase_record_id: id,
          organization_id: organizationId
        }));

        await PurchaseRecordItem.bulkCreate(itemsToCreate, { transaction });
      }
    }

    // Update costs if provided
    if (additionalCostsData && Array.isArray(additionalCostsData)) {
      // Delete existing costs
      await PurchaseRecordCost.destroy({
        where: { purchase_record_id: id },
        transaction
      });

      // Create new costs
      if (additionalCostsData.length > 0) {
        const costsToCreate = additionalCostsData.map((cost: CreatePurchaseRecordCostData) => ({
          ...cost,
          purchase_record_id: id,
          organization_id: organizationId
        }));

        await PurchaseRecordCost.bulkCreate(costsToCreate, { transaction });
      }
    }

    await transaction.commit();

    // Fetch the updated record with associations
    const updatedRecord = await PurchaseRecord.findByPk(id, {
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        },
        {
          model: Supplier,
          attributes: ['id', 'company_name', 'contact_person']
        },
        {
          model: VehicleArrival,
          attributes: ['id', 'vehicle_number', 'arrival_time']
        },
        {
          model: PurchaseRecordItem,
          as: 'items'
        },
        {
          model: PurchaseRecordCost,
          as: 'costs'
        }
      ]
    });

    res.json(updatedRecord);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating purchase record:', error);
    res.status(500).json({ error: 'Failed to update purchase record' });
  }
};

// Delete purchase record
export const deletePurchaseRecord = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Find purchase record
    const purchaseRecord = await PurchaseRecord.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      transaction
    });

    if (!purchaseRecord) {
      return res.status(404).json({ error: 'Purchase record not found' });
    }

    // Update supplier balance by subtracting the record amount
    if (purchaseRecord.supplier_id && purchaseRecord.total_amount) {
      await Supplier.decrement('current_balance', {
        by: purchaseRecord.total_amount,
        where: { id: purchaseRecord.supplier_id },
        transaction
      });
    }

    // Delete associated items and costs (cascade should handle this, but being explicit)
    await PurchaseRecordItem.destroy({
      where: { purchase_record_id: id },
      transaction
    });

    await PurchaseRecordCost.destroy({
      where: { purchase_record_id: id },
      transaction
    });

    // Delete purchase record
    await purchaseRecord.destroy({ transaction });

    await transaction.commit();

    res.json({ message: 'Purchase record deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting purchase record:', error);
    res.status(500).json({ error: 'Failed to delete purchase record' });
  }
};

// Update purchase record closure status
export const updatePurchaseRecordClosureStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const { status, closureNotes, notes } = req.body;
    const finalNotes = closureNotes || notes;

    // Validate status
    if (!status || !['partial_closure', 'full_closure'].includes(status)) {
      return res.status(400).json({ 
        error: 'Valid status (partial_closure or full_closure) is required' 
      });
    }

    // Find purchase record
    const purchaseRecord = await PurchaseRecord.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!purchaseRecord) {
      return res.status(404).json({ error: 'Purchase record not found' });
    }

    // Check if record can be updated based on current status
    if (purchaseRecord.status === 'full_closure') {
      return res.status(400).json({ error: 'Cannot update a fully closed purchase record' });
    }

    if (purchaseRecord.status === 'cancelled') {
      return res.status(400).json({ error: 'Cannot update a cancelled purchase record' });
    }

    const updateData: any = {
      status,
      updated_at: new Date()
    };

    // Set closure fields for full closure
    if (status === 'full_closure') {
      updateData.closure_date = new Date();
      if (finalNotes) {
        updateData.closure_notes = finalNotes;
      }
    }

    // Update purchase record
    await purchaseRecord.update(updateData);

    // Fetch the updated record with associations
    const updatedRecord = await PurchaseRecord.findByPk(id, {
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        },
        {
          model: Supplier,
          attributes: ['id', 'company_name', 'contact_person']
        },
        {
          model: VehicleArrival,
          attributes: ['id', 'vehicle_number', 'arrival_time']
        },
        {
          model: PurchaseRecordItem,
          as: 'items'
        },
        {
          model: PurchaseRecordCost,
          as: 'costs'
        }
      ]
    });

    res.json(updatedRecord);
  } catch (error) {
    console.error('Error updating purchase record closure status:', error);
    res.status(500).json({ error: 'Failed to update purchase record closure status' });
  }
};

// Get purchase records by supplier ID
export const getPurchaseRecordsBySupplierId = async (req: Request, res: Response) => {
  try {
    const { supplierId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Verify supplier belongs to organization
    const supplier = await Supplier.findOne({
      where: { 
        id: supplierId,
        organization_id: organizationId 
      }
    });

    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    const purchaseRecords = await PurchaseRecord.findAll({
      where: { 
        supplier_id: supplierId,
        organization_id: organizationId 
      },
      include: [
        {
          model: PurchaseRecordItem,
          as: 'items'
        },
        {
          model: PurchaseRecordCost,
          as: 'costs'
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json(purchaseRecords);
  } catch (error) {
    console.error('Error fetching purchase records by supplier ID:', error);
    res.status(500).json({ error: 'Failed to fetch purchase records' });
  }
};

// Search purchase records
export const searchPurchaseRecords = async (req: Request, res: Response) => {
  try {
    const { q } = req.query;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!q || typeof q !== 'string') {
      return res.status(400).json({ error: 'Search query is required' });
    }

    const purchaseRecords = await PurchaseRecord.findAll({
      where: {
        organization_id: organizationId,
        [Op.or]: [
          { record_number: { [Op.like]: `%${q}%` } },
          { notes: { [Op.like]: `%${q}%` } }
        ]
      },
      include: [
        {
          model: Supplier,
          attributes: ['id', 'company_name', 'contact_person'],
          where: {
            [Op.or]: [
              { company_name: { [Op.like]: `%${q}%` } },
              { contact_person: { [Op.like]: `%${q}%` } }
            ]
          },
          required: false
        }
      ],
      attributes: ['id', 'record_number', 'record_date', 'total_amount', 'status'],
      limit: 20,
      order: [['created_at', 'DESC']]
    });

    res.json(purchaseRecords);
  } catch (error) {
    console.error('Error searching purchase records:', error);
    res.status(500).json({ error: 'Failed to search purchase records' });
  }
};

// Get purchase record statistics
export const getPurchaseRecordStatistics = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const { startDate, endDate } = req.query;

    let whereClause: any = { organization_id: organizationId };

    if (startDate && endDate) {
      whereClause.record_date = {
        [Op.between]: [startDate, endDate]
      };
    }

    const [totalAmount, totalCount, partialClosureCount, fullClosureCount, cancelledCount] = await Promise.all([
      PurchaseRecord.sum('total_amount', {
        where: whereClause
      }),
      PurchaseRecord.count({
        where: whereClause
      }),
      PurchaseRecord.count({
        where: { ...whereClause, status: 'partial_closure' }
      }),
      PurchaseRecord.count({
        where: { ...whereClause, status: 'full_closure' }
      }),
      PurchaseRecord.count({
        where: { ...whereClause, status: 'cancelled' }
      })
    ]);

    res.json({
      total_amount: totalAmount || 0,
      total_records: totalCount,
      partial_closure_count: partialClosureCount,
      full_closure_count: fullClosureCount,
      cancelled_count: cancelledCount
    });
  } catch (error) {
    console.error('Error fetching purchase record statistics:', error);
    res.status(500).json({ error: 'Failed to fetch purchase record statistics' });
  }
};
