import { Request, Response } from 'express';
import { Op, Sequelize } from 'sequelize';
import { 
  SalesOrder, 
  Payment, 
  Customer, 
  Supplier, 
  Product, 
  CurrentInventory, 
  PurchaseRecord,
  VehicleArrival 
} from '../models';
import { sendSuccess, sendError } from '../utils/response';

interface AuthenticatedRequest extends Request {
  user?: any;
  userId?: string;
  organizationId?: string;
}

export class DashboardController {
  // Customer and Supplier View
  static async getCustomerSupplierView(req: AuthenticatedRequest, res: Response) {
    try {
      const { organizationId } = req as any;
      const { startDate, endDate } = req.query;

      // Customer metrics - Always get total count without date filter
      const totalCustomers = await Customer.count({
        where: { 
          organization_id: organizationId
        }
      });

      const activeCustomers = await Customer.count({
        where: { 
          organization_id: organizationId,
          status: 'active'
        }
      });

      // Only apply date filter to new customers
      const newCustomers = await Customer.count({
        where: { 
          organization_id: organizationId,
          ...(startDate && endDate ? {
            created_at: { [Op.between]: [startDate, endDate] }
          } : {
            created_at: { [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
          })
        }
      });

      // Supplier metrics - Always get total count without date filter
      const totalSuppliers = await Supplier.count({
        where: { 
          organization_id: organizationId
        }
      });

      const activeSuppliers = await Supplier.count({
        where: { 
          organization_id: organizationId,
          status: 'active'
        }
      });

      // Only apply date filter to new suppliers
      const newSuppliers = await Supplier.count({
        where: { 
          organization_id: organizationId,
          ...(startDate && endDate ? {
            created_at: { [Op.between]: [startDate, endDate] }
          } : {
            created_at: { [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
          })
        }
      });

      const data = {
        customers: {
          total: totalCustomers,
          active: activeCustomers,
          new: newCustomers,
          inactive: totalCustomers - activeCustomers
        },
        suppliers: {
          total: totalSuppliers,
          active: activeSuppliers,
          new: newSuppliers,
          inactive: totalSuppliers - activeSuppliers
        }
      };

      return sendSuccess(res, 'Customer and supplier data retrieved successfully', data);
    } catch (error) {
      console.error('Error fetching customer supplier data:', error);
      return sendError(res, 'Failed to fetch customer supplier data', error instanceof Error ? error.message : 'Unknown error', 500);
    }
  }

  // Arrival View
  static async getArrivalView(req: AuthenticatedRequest, res: Response) {
    try {
      const { organizationId } = req as any;
      const { startDate, endDate } = req.query;

      console.log('Dashboard getArrivalView called with:', { organizationId, startDate, endDate });

      const whereClause = {
        organization_id: organizationId,
        ...(startDate && endDate ? {
          arrival_time: { [Op.between]: [startDate, endDate] }
        } : {})
      };

      console.log('Vehicle arrival where clause:', whereClause);

      const totalArrivals = await VehicleArrival.count({ where: whereClause });
      console.log('Total arrivals found:', totalArrivals);

      const unloaded = await VehicleArrival.count({
        where: { ...whereClause, status: 'pending' }
      });

      const partialClosed = await VehicleArrival.count({
        where: { ...whereClause, status: 'in_progress' }
      });

      const fullClosed = await VehicleArrival.count({
        where: { ...whereClause, status: 'completed' }
      });

      const poCreationPending = await VehicleArrival.count({
        where: { ...whereClause, status: 'completed' }
      });

      // Enhanced metrics for new Vehicle Arrivals Card
      const unloadingPending = await VehicleArrival.count({
        where: { ...whereClause, status: 'pending' }
      });

      const unloadingComplete = await VehicleArrival.count({
        where: { ...whereClause, status: 'completed' }
      });

      // Get all vehicle arrivals in the date range to check for purchase records
      const allVehicles = await VehicleArrival.findAll({
        where: whereClause,
        attributes: ['id']
      });

      const vehicleIds = allVehicles.map(v => v.id);
      console.log('Vehicle IDs found:', vehicleIds);

      let pattiPending = 0;
      let partialPatti = 0;
      let fullPatti = 0;

      if (vehicleIds.length > 0) {
        // Count vehicles with no purchase records (Patti Pending)
        const vehiclesWithPurchaseRecords = await PurchaseRecord.findAll({
          where: {
            organization_id: organizationId,
            vehicle_arrival_id: { [Op.in]: vehicleIds }
          },
          attributes: ['vehicle_arrival_id', 'status'],
          group: ['vehicle_arrival_id', 'status']
        });

        const vehicleIdsWithRecords = new Set(vehiclesWithPurchaseRecords.map(pr => pr.vehicle_arrival_id));
        pattiPending = totalArrivals - vehicleIdsWithRecords.size;

        // Count partial and full patti
        partialPatti = await PurchaseRecord.count({
          where: {
            organization_id: organizationId,
            vehicle_arrival_id: { [Op.in]: vehicleIds },
            status: 'partial_closure'
          }
        });

        fullPatti = await PurchaseRecord.count({
          where: {
            organization_id: organizationId,
            vehicle_arrival_id: { [Op.in]: vehicleIds },
            status: 'full_closure'
          }
        });
      } else {
        pattiPending = totalArrivals;
      }

      const data = {
        totalArrivals,
        unloaded,
        partialClosed,
        fullClosed,
        poCreationPending,
        // Enhanced fields
        unloadingPending,
        unloadingComplete,
        pattiPending,
        partialPatti,
        fullPatti
      };

      console.log('Final arrival data:', data);
      return sendSuccess(res, 'Arrival data retrieved successfully', data);
    } catch (error) {
      console.error('Error fetching arrival data:', error);
      return sendError(res, 'Failed to fetch arrival data', error instanceof Error ? error.message : 'Unknown error', 500);
    }
  }

  // Inventory View
  static async getInventoryView(req: AuthenticatedRequest, res: Response) {
    try {
      const { organizationId } = req as any;
      const { startDate, endDate } = req.query;

      // Current inventory total
      const currentInventory = await CurrentInventory.findAll({
        where: { organization_id: organizationId },
        attributes: [
          [Sequelize.fn('SUM', Sequelize.col('available_quantity')), 'totalQuantity'],
          [Sequelize.fn('SUM', Sequelize.col('total_weight')), 'totalWeight']
        ]
      });

      // Count new arrivals in the period (simplified)
      const newArrivalsCount = await VehicleArrival.count({
        where: {
          organization_id: organizationId,
          status: 'completed',
          ...(startDate && endDate ? {
            arrival_time: { [Op.between]: [startDate, endDate] }
          } : {})
        }
      });

      // Count total dispatched orders in the period (simplified)
      const totalDispatchedCount = await SalesOrder.count({
        where: {
          organization_id: organizationId,
          status: { [Op.in]: ['dispatched', 'delivered', 'completed'] },
          ...(startDate && endDate ? {
            order_date: { [Op.between]: [startDate, endDate] }
          } : {})
        }
      });

      // Get current totals
      const currentQty = parseInt(currentInventory[0]?.getDataValue('totalQuantity') || '0');
      const currentWeight = parseFloat(currentInventory[0]?.getDataValue('totalWeight') || '0');
      
      // Simplified calculation - assume opening stock is current + some estimate
      const estimatedOpeningStock = Math.max(0, currentQty + totalDispatchedCount - newArrivalsCount);

      const data = {
        openingStock: estimatedOpeningStock,
        newArrivals: newArrivalsCount,
        totalDispatched: totalDispatchedCount,
        currentInventory: currentQty,
        totalWeight: currentWeight,
        newArrivalWeight: 0 // Simplified for now
      };

      return sendSuccess(res, 'Inventory data retrieved successfully', data);
    } catch (error) {
      console.error('Error fetching inventory data:', error);
      return sendError(res, 'Failed to fetch inventory data', error instanceof Error ? error.message : 'Unknown error', 500);
    }
  }

  // Sales Payment View
  static async getSalesPaymentView(req: AuthenticatedRequest, res: Response) {
    try {
      const { organizationId } = req as any;
      const { startDate, endDate } = req.query;

      const whereClause = {
        organization_id: organizationId,
        ...(startDate && endDate ? {
          order_date: { [Op.between]: [startDate, endDate] }
        } : {})
      };

      // Total sales
      const totalSales = await SalesOrder.sum('total_amount', {
        where: { 
          ...whereClause,
          status: { [Op.in]: ['confirmed', 'dispatched', 'delivered', 'completed'] }
        }
      }) || 0;

      // Total payments collected
      const totalPaymentsCollected = await Payment.sum('amount', {
        where: {
          organization_id: organizationId,
          type: 'received',
          status: 'confirmed',
          ...(startDate && endDate ? {
            payment_date: { [Op.between]: [startDate, endDate] }
          } : {})
        }
      }) || 0;

      // Pending payments
      const pendingPayments = await Payment.sum('amount', {
        where: {
          organization_id: organizationId,
          type: 'received',
          status: 'pending',
          ...(startDate && endDate ? {
            payment_date: { [Op.between]: [startDate, endDate] }
          } : {})
        }
      }) || 0;

      const data = {
        totalSales,
        totalPaymentsCollected,
        pendingPayments,
        collectionRate: totalSales > 0 ? ((totalPaymentsCollected / totalSales) * 100).toFixed(1) : '0'
      };

      return sendSuccess(res, 'Sales payment data retrieved successfully', data);
    } catch (error) {
      console.error('Error fetching sales payment data:', error);
      return sendError(res, 'Failed to fetch sales payment data', error instanceof Error ? error.message : 'Unknown error', 500);
    }
  }

  // Purchase Payment View
  static async getPurchasePaymentView(req: AuthenticatedRequest, res: Response) {
    try {
      const { organizationId } = req as any;
      const { startDate, endDate } = req.query;

      const whereClause = {
        organization_id: organizationId,
        ...(startDate && endDate ? {
          record_date: { [Op.between]: [startDate, endDate] }
        } : {})
      };

      // Total purchases
      const totalPurchases = await PurchaseRecord.sum('total_amount', {
        where: { 
          ...whereClause,
          status: { [Op.in]: ['confirmed', 'completed'] }
        }
      }) || 0;

      // Total payments made
      const totalPaymentsMade = await Payment.sum('amount', {
        where: {
          organization_id: organizationId,
          type: 'paid',
          status: 'confirmed',
          ...(startDate && endDate ? {
            payment_date: { [Op.between]: [startDate, endDate] }
          } : {})
        }
      }) || 0;

      // Pending payments
      const pendingPayments = await Payment.sum('amount', {
        where: {
          organization_id: organizationId,
          type: 'paid',
          status: 'pending',
          ...(startDate && endDate ? {
            payment_date: { [Op.between]: [startDate, endDate] }
          } : {})
        }
      }) || 0;

      const data = {
        totalPurchases,
        totalPaymentsMade,
        pendingPayments,
        paymentRate: totalPurchases > 0 ? ((totalPaymentsMade / totalPurchases) * 100).toFixed(1) : '0'
      };

      return sendSuccess(res, 'Purchase payment data retrieved successfully', data);
    } catch (error) {
      console.error('Error fetching purchase payment data:', error);
      return sendError(res, 'Failed to fetch purchase payment data', error instanceof Error ? error.message : 'Unknown error', 500);
    }
  }

  // Financial Metrics
  static async getFinancialMetrics(req: AuthenticatedRequest, res: Response) {
    try {
      const { organizationId } = req as any;
      const { period = '30' } = req.query;
      
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(period as string));
      
      // Revenue Trends (Daily for last 30 days)
      const revenueTrends = await SalesOrder.findAll({
        where: {
          organization_id: organizationId,
          order_date: { [Op.gte]: startDate },
          status: { [Op.in]: ['confirmed', 'dispatched', 'delivered', 'shipped'] }
        },
        attributes: [
          [Sequelize.fn('DATE', Sequelize.col('order_date')), 'date'],
          [Sequelize.fn('SUM', Sequelize.col('total_amount')), 'revenue'],
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'orders']
        ],
        group: [Sequelize.fn('DATE', Sequelize.col('order_date'))],
        order: [[Sequelize.fn('DATE', Sequelize.col('order_date')), 'ASC']]
      });

      // Profit Margins (Revenue vs Costs)
      const totalRevenue = await SalesOrder.sum('total_amount', {
        where: {
          organization_id: organizationId,
          order_date: { [Op.gte]: startDate },
          status: { [Op.in]: ['confirmed', 'dispatched', 'delivered', 'shipped'] }
        }
      });

      const totalCosts = await PurchaseRecord.sum('total_amount', {
        where: {
          organization_id: organizationId,
          record_date: { [Op.gte]: startDate },
          status: { [Op.in]: ['confirmed', 'completed'] }
        }
      });

      const grossProfit = (totalRevenue || 0) - (totalCosts || 0);
      const grossProfitMargin = totalRevenue ? (grossProfit / totalRevenue) * 100 : 0;

      // Payment Status Distribution
      const paymentStatus = await Payment.findAll({
        where: {
          organization_id: organizationId,
          payment_date: { [Op.gte]: startDate }
        },
        attributes: [
          'status',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count'],
          [Sequelize.fn('SUM', Sequelize.col('amount')), 'amount']
        ],
        group: ['status']
      });

      const financialMetrics = {
        revenueTrends: revenueTrends.map(item => ({
          date: item.getDataValue('date'),
          revenue: parseFloat(item.getDataValue('revenue') || '0'),
          orders: parseInt(item.getDataValue('orders') || '0')
        })),
        profitability: {
          totalRevenue: totalRevenue || 0,
          totalCosts: totalCosts || 0,
          grossProfit,
          grossProfitMargin: Math.round(grossProfitMargin * 100) / 100
        },
        paymentStatus: paymentStatus.map(item => ({
          status: item.getDataValue('status'),
          count: parseInt(item.getDataValue('count') || '0'),
          amount: parseFloat(item.getDataValue('amount') || '0')
        }))
      };

      return sendSuccess(res, 'Financial metrics retrieved successfully', financialMetrics);
    } catch (error) {
      console.error('Error fetching financial metrics:', error);
      return sendError(res, 'Failed to fetch financial metrics', error instanceof Error ? error.message : 'Unknown error', 500);
    }
  }

  // Operational Metrics
  static async getOperationalMetrics(req: AuthenticatedRequest, res: Response) {
    try {
      const { organizationId } = req as any;
      const { period = '30' } = req.query;
      
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(period as string));

      // Order Fulfillment Metrics
      const orderMetrics = await SalesOrder.findAll({
        where: {
          organization_id: organizationId,
          order_date: { [Op.gte]: startDate }
        },
        attributes: [
          'status',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        group: ['status']
      });

      // Inventory Turnover
      const inventoryData = await CurrentInventory.findAll({
        where: { organization_id: organizationId },
        attributes: [
          [Sequelize.fn('SUM', Sequelize.col('available_quantity')), 'quantity'],
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'products']
        ],
        group: ['product_id']
      });

      // Vehicle Arrival Performance
      const vehicleMetrics = await VehicleArrival.findAll({
        where: {
          organization_id: organizationId,
          arrival_time: { [Op.gte]: startDate }
        },
        attributes: [
          'status',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        group: ['status']
      });

      // Top Performing Products
      const topProducts = await SalesOrder.findAll({
        where: {
          organization_id: organizationId,
          order_date: { [Op.gte]: startDate },
          status: { [Op.in]: ['confirmed', 'dispatched', 'delivered', 'shipped'] }
        },
        include: [{
          model: Product,
          attributes: ['name']
        }],
        attributes: [
          [Sequelize.fn('SUM', Sequelize.col('total_amount')), 'revenue'],
          [Sequelize.fn('COUNT', Sequelize.col('SalesOrder.id')), 'orders']
        ],
        group: ['Product.id', 'Product.name'],
        order: [[Sequelize.fn('SUM', Sequelize.col('total_amount')), 'DESC']],
        limit: 10
      });

      const operationalMetrics = {
        orderFulfillment: orderMetrics.map(item => ({
          status: item.getDataValue('status'),
          count: parseInt(item.getDataValue('count') || '0')
        })),
        inventory: inventoryData.map(item => ({
          quantity: parseInt(item.getDataValue('quantity') || '0'),
          products: parseInt(item.getDataValue('products') || '0')
        })),
        vehicleArrivals: vehicleMetrics.map(item => ({
          status: item.getDataValue('status'),
          count: parseInt(item.getDataValue('count') || '0')
        })),
        topProducts: topProducts.map(item => ({
          name: (item as any).Product?.name || 'Unknown',
          revenue: parseFloat(item.getDataValue('revenue') || '0'),
          orders: parseInt(item.getDataValue('orders') || '0')
        }))
      };

      return sendSuccess(res, 'Operational metrics retrieved successfully', operationalMetrics);
    } catch (error) {
      console.error('Error fetching operational metrics:', error);
      return sendError(res, 'Failed to fetch operational metrics', error instanceof Error ? error.message : 'Unknown error', 500);
    }
  }

  // Customer Insights
  static async getCustomerInsights(req: AuthenticatedRequest, res: Response) {
    try {
      const { organizationId } = req as any;
      const { period = '30' } = req.query;
      
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(period as string));

      // Top Customers by Revenue
      const topCustomers = await SalesOrder.findAll({
        where: {
          organization_id: organizationId,
          order_date: { [Op.gte]: startDate },
          status: { [Op.in]: ['confirmed', 'dispatched', 'delivered', 'shipped'] }
        },
        include: [{
          model: Customer,
          attributes: ['name', 'customer_type', 'status']
        }],
        attributes: [
          [Sequelize.fn('SUM', Sequelize.col('total_amount')), 'totalRevenue'],
          [Sequelize.fn('COUNT', Sequelize.col('SalesOrder.id')), 'totalOrders'],
          [Sequelize.fn('AVG', Sequelize.col('total_amount')), 'avgOrderValue']
        ],
        group: ['customer_id', 'Customer.name', 'Customer.customer_type', 'Customer.status'],
        order: [[Sequelize.fn('SUM', Sequelize.col('total_amount')), 'DESC']],
        limit: 10
      });

      // Customer Type Distribution
      const customerTypes = await Customer.findAll({
        where: { organization_id: organizationId },
        attributes: [
          'customer_type',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        group: ['customer_type']
      });

      // New vs Returning Customers
      const newCustomers = await Customer.findAll({
        where: {
          organization_id: organizationId,
          created_at: { [Op.gte]: startDate }
        },
        attributes: [[Sequelize.fn('COUNT', Sequelize.col('id')), 'newCustomers']]
      });

      const customerInsights = {
        topCustomers: topCustomers.map(item => ({
          name: (item as any).Customer?.name || 'Unknown',
          type: (item as any).Customer?.customer_type || 'Unknown',
          status: (item as any).Customer?.status || 'Unknown',
          totalRevenue: parseFloat(item.getDataValue('totalRevenue') || '0'),
          totalOrders: parseInt(item.getDataValue('totalOrders') || '0'),
          avgOrderValue: parseFloat(item.getDataValue('avgOrderValue') || '0')
        })),
        customerTypes: customerTypes.map(item => ({
          type: item.getDataValue('customer_type'),
          count: parseInt(item.getDataValue('count') || '0')
        })),
        newCustomers: parseInt(newCustomers[0]?.getDataValue('newCustomers') || '0')
      };

      return sendSuccess(res, 'Customer insights retrieved successfully', customerInsights);
    } catch (error) {
      console.error('Error fetching customer insights:', error);
      return sendError(res, 'Failed to fetch customer insights', error instanceof Error ? error.message : 'Unknown error', 500);
    }
  }

  // Critical Alerts
  static async getCriticalAlerts(req: AuthenticatedRequest, res: Response) {
    try {
      const { organizationId } = req as any;
      const alerts = [];

      // Low Stock Alerts
      const lowStockItems = await CurrentInventory.findAll({
        where: {
          organization_id: organizationId,
          available_quantity: { [Op.lt]: 10 } // Less than 10 units
        },
        limit: 5,
        order: [['available_quantity', 'ASC']]
      });

      lowStockItems.forEach(item => {
        alerts.push({
          type: 'low_stock',
          severity: 'high',
          title: 'Low Stock Alert',
          message: `${item.product_name} (${item.sku_code}) has only ${item.available_quantity} units left`,
          timestamp: new Date()
        });
      });

      // Overdue Payments
      const overduePayments = await Payment.findAll({
        where: {
          organization_id: organizationId,
          status: 'pending',
          payment_date: { [Op.lt]: new Date() }
        },
        limit: 5,
        order: [['payment_date', 'ASC']]
      });

      overduePayments.forEach(payment => {
        alerts.push({
          type: 'overdue_payment',
          severity: 'medium',
          title: 'Overdue Payment',
          message: `Payment of ₹${payment.amount} from ${payment.party_name} is overdue`,
          timestamp: payment.payment_date
        });
      });

      // Pending Orders
      const pendingOrders = await SalesOrder.count({
        where: {
          organization_id: organizationId,
          status: 'confirmed'
        }
      });

      if (pendingOrders > 10) {
        alerts.push({
          type: 'pending_orders',
          severity: 'medium',
          title: 'High Pending Orders',
          message: `${pendingOrders} orders are pending dispatch`,
          timestamp: new Date()
        });
      }

      return sendSuccess(res, 'Critical alerts retrieved successfully', alerts);
    } catch (error) {
      console.error('Error fetching critical alerts:', error);
      return sendError(res, 'Failed to fetch critical alerts', error instanceof Error ? error.message : 'Unknown error', 500);
    }
  }
}
