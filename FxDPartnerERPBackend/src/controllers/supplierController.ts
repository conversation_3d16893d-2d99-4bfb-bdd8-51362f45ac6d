import { Request, Response } from 'express';
import { Transaction, Op } from 'sequelize';
import sequelize from '../config/sequelize';
import { Supplier, CreateSupplierData, UpdateSupplierData } from '../models/Supplier';
import { Organization } from '../models/Organization';
import { Payment } from '../models/Payment';
import { PurchaseRecord, PurchaseRecordItem, PurchaseRecordCost } from '../models/PurchaseRecord';

// Get all suppliers for an organization
export const getSuppliers = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const suppliers = await Supplier.findAll({
      where: { organization_id: organizationId },
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json(suppliers);
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    res.status(500).json({ error: 'Failed to fetch suppliers' });
  }
};

// Get a single supplier
export const getSupplier = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const supplier = await Supplier.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    res.json(supplier);
  } catch (error) {
    console.error('Error fetching supplier:', error);
    res.status(500).json({ error: 'Failed to fetch supplier' });
  }
};

// Create a new supplier
export const createSupplier = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const supplierData: CreateSupplierData = {
      ...req.body,
      organization_id: organizationId
    };

    // Validate required fields - company_name is required
    if (!supplierData.company_name || !supplierData.company_name.trim()) {
      return res.status(400).json({ 
        error: 'Company name is required' 
      });
    }

    // Validate email format if provided
    if (supplierData.email && supplierData.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(supplierData.email.trim())) {
      return res.status(400).json({ 
        error: 'Please enter a valid email address' 
      });
    }

    // Ensure required fields have default values if not provided
    if (!supplierData.contact_person) supplierData.contact_person = '';
    if (!supplierData.phone) supplierData.phone = '';
    if (!supplierData.email) supplierData.email = '';
    if (!supplierData.address) supplierData.address = '';
    
    // Clean up empty strings to undefined for truly optional fields
    if (supplierData.gst_number === '') supplierData.gst_number = undefined;
    if (supplierData.pan_number === '') supplierData.pan_number = undefined;
    if (supplierData.bank_name === '') supplierData.bank_name = undefined;
    if (supplierData.account_number === '') supplierData.account_number = undefined;
    if (supplierData.ifsc_code === '') supplierData.ifsc_code = undefined;
    if (supplierData.notes === '') supplierData.notes = undefined;

    // Validate organization exists
    const organization = await Organization.findByPk(organizationId);
    if (!organization) {
      return res.status(400).json({ error: 'Invalid organization ID' });
    }

    const supplier = await Supplier.create(supplierData as any);

    // Fetch the created supplier with associations
    const createdSupplier = await Supplier.findByPk(supplier.id, {
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    res.status(201).json(createdSupplier);
  } catch (error) {
    console.error('Error creating supplier:', error);
    
    // Handle duplicate constraint errors
    if ((error as any).name === 'SequelizeUniqueConstraintError') {
      const constraintError = error as any;
      if (constraintError.message.includes('email')) {
        return res.status(400).json({ error: 'Supplier with this email already exists' });
      } else {
        return res.status(400).json({ error: 'Supplier with these details already exists' });
      }
    }
    
    res.status(500).json({ error: 'Failed to create supplier' });
  }
};

// Update a supplier
export const updateSupplier = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const updateData: UpdateSupplierData = req.body;

    // Validate company name if provided
    if (updateData.company_name !== undefined && (!updateData.company_name || !updateData.company_name.trim())) {
      return res.status(400).json({ 
        error: 'Company name cannot be empty' 
      });
    }

    // Validate email format if provided
    if (updateData.email && updateData.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(updateData.email.trim())) {
      return res.status(400).json({ 
        error: 'Please enter a valid email address' 
      });
    }

    // Clean up empty strings to undefined for optional fields, but ensure required fields have defaults
    if (updateData.contact_person === '') updateData.contact_person = '';
    if (updateData.phone === '') updateData.phone = '';
    if (updateData.email === '') updateData.email = '';
    if (updateData.address === '') updateData.address = '';
    if (updateData.gst_number === '') updateData.gst_number = undefined;
    if (updateData.pan_number === '') updateData.pan_number = undefined;
    if (updateData.bank_name === '') updateData.bank_name = undefined;
    if (updateData.account_number === '') updateData.account_number = undefined;
    if (updateData.ifsc_code === '') updateData.ifsc_code = undefined;
    if (updateData.notes === '') updateData.notes = undefined;

    // Ensure numeric fields are properly handled
    if (updateData.credit_limit !== undefined) {
      updateData.credit_limit = Number(updateData.credit_limit);
      if (isNaN(updateData.credit_limit) || updateData.credit_limit < 0) {
        return res.status(400).json({ error: 'Credit limit must be a valid non-negative number' });
      }
    }

    if (updateData.payment_terms !== undefined) {
      updateData.payment_terms = Number(updateData.payment_terms);
      if (isNaN(updateData.payment_terms) || updateData.payment_terms <= 0) {
        return res.status(400).json({ error: 'Payment terms must be a valid positive number' });
      }
    }

    if (updateData.current_balance !== undefined) {
      updateData.current_balance = Number(updateData.current_balance);
      if (isNaN(updateData.current_balance)) {
        return res.status(400).json({ error: 'Current balance must be a valid number' });
      }
    }

    // Find supplier
    const supplier = await Supplier.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    // Update supplier
    await supplier.update(updateData);

    // Fetch the updated supplier with associations
    const updatedSupplier = await Supplier.findByPk(id, {
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    res.json(updatedSupplier);
  } catch (error) {
    console.error('Error updating supplier:', error);
    
    // Handle duplicate constraint errors
    if ((error as any).name === 'SequelizeUniqueConstraintError') {
      const constraintError = error as any;
      if (constraintError.message.includes('email')) {
        return res.status(400).json({ error: 'Supplier with this email already exists' });
      } else {
        return res.status(400).json({ error: 'Supplier with these details already exists' });
      }
    }
    
    res.status(500).json({ error: 'Failed to update supplier' });
  }
};

// Delete a supplier
export const deleteSupplier = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Find supplier
    const supplier = await Supplier.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    await supplier.destroy();

    res.json({ message: 'Supplier deleted successfully' });
  } catch (error) {
    console.error('Error deleting supplier:', error);
    res.status(500).json({ error: 'Failed to delete supplier' });
  }
};

// Update supplier balance
export const updateSupplierBalance = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const { amount, operation } = req.body;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!amount || !operation || !['add', 'subtract'].includes(operation)) {
      return res.status(400).json({ 
        error: 'Amount and valid operation (add/subtract) are required' 
      });
    }

    // Find supplier
    const supplier = await Supplier.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      transaction
    });

    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    // Update balance
    if (operation === 'add') {
      await supplier.increment('current_balance', { by: amount, transaction });
    } else {
      await supplier.decrement('current_balance', { by: amount, transaction });
    }

    await transaction.commit();

    // Fetch updated supplier
    const updatedSupplier = await Supplier.findByPk(id, {
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    res.json(updatedSupplier);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating supplier balance:', error);
    res.status(500).json({ error: 'Failed to update supplier balance' });
  }
};

// Get purchase records by supplier ID
export const getPurchaseRecordsBySupplierId = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // First verify supplier exists and belongs to organization
    const supplier = await Supplier.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    // Fetch purchase records for this supplier
    const purchaseRecords = await PurchaseRecord.findAll({
      where: { 
        supplier_id: id,
        organization_id: organizationId 
      },
      include: [
        {
          model: PurchaseRecordItem
        },
        {
          model: PurchaseRecordCost
        },
        {
          model: Supplier,
          attributes: ['id', 'company_name', 'contact_person']
        }
      ],
      order: [['record_date', 'DESC']]
    });

    res.json(purchaseRecords);
  } catch (error) {
    console.error('Error fetching purchase records by supplier ID:', error);
    res.status(500).json({ error: 'Failed to fetch purchase records' });
  }
};

// Get payments by supplier ID
export const getPaymentsBySupplierId = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // First verify supplier exists and belongs to organization
    const supplier = await Supplier.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    // Fetch payments for this supplier
    const payments = await Payment.findAll({
      where: { 
        party_id: id,
        party_type: 'supplier',
        organization_id: organizationId 
      },
      order: [['payment_date', 'DESC']]
    });

    res.json(payments);
  } catch (error) {
    console.error('Error fetching payments by supplier ID:', error);
    res.status(500).json({ error: 'Failed to fetch payments' });
  }
};

// Get suppliers with statistics
export const getSuppliersWithStats = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const suppliers = await Supplier.findAll({
      where: { organization_id: organizationId },
      attributes: [
        'id',
        'company_name',
        'contact_person',
        'phone',
        'email',
        'current_balance',
        'credit_limit',
        'status',
        'created_at'
      ],
      order: [['created_at', 'DESC']]
    });

    // Fetch purchase records statistics for each supplier
    const suppliersWithStats = await Promise.all(
      suppliers.map(async (supplier) => {
        const purchaseRecords = await PurchaseRecord.findAll({
          where: {
            supplier_id: supplier.id,
            organization_id: organizationId
          },
          attributes: ['total_amount', 'record_date'],
          order: [['record_date', 'DESC']]
        });

        const purchase_records_count = purchaseRecords.length;
        const total_purchase_amount = purchaseRecords.reduce(
          (sum, record) => sum + (Number(record.total_amount) || 0), 
          0
        );
        const last_purchase_date = purchaseRecords.length > 0 ? purchaseRecords[0].record_date : null;

        return {
          ...supplier.toJSON(),
          purchase_records_count,
          total_purchase_amount,
          last_purchase_date
        };
      })
    );

    res.json(suppliersWithStats);
  } catch (error) {
    console.error('Error fetching suppliers with stats:', error);
    res.status(500).json({ error: 'Failed to fetch suppliers with statistics' });
  }
};

// Search suppliers
export const searchSuppliers = async (req: Request, res: Response) => {
  try {
    const { q } = req.query;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!q || typeof q !== 'string') {
      return res.status(400).json({ error: 'Search query is required' });
    }

    const suppliers = await Supplier.findAll({
      where: {
        organization_id: organizationId,
        [Op.or]: [
          { company_name: { [Op.like]: `%${q}%` } },
          { contact_person: { [Op.like]: `%${q}%` } },
          { phone: { [Op.like]: `%${q}%` } },
          { email: { [Op.like]: `%${q}%` } }
        ]
      },
      attributes: ['id', 'company_name', 'contact_person', 'phone', 'email', 'status'],
      limit: 20,
      order: [['company_name', 'ASC']]
    });

    res.json(suppliers);
  } catch (error) {
    console.error('Error searching suppliers:', error);
    res.status(500).json({ error: 'Failed to search suppliers' });
  }
};
