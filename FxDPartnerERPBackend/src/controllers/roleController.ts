import { Response } from 'express';
import { Role, CreateRoleData, UpdateRoleData, PERMISSIONS } from '../models/Role';
import { UserRole } from '../models/UserRole';
import { User } from '../models/User';
import { Organization } from '../models/Organization';
import { AuthenticatedRequest } from '../middleware/permissions';
import { sendSuccess, sendError } from '../utils/response';

/**
 * Get all roles for the organization
 */
export const getRoles = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { organizationId } = req;
    
    if (!organizationId) {
      return sendError(res, 'Organization ID is required', undefined, 400);
    }

    const roles = await Role.findAll({
      where: {
        organization_id: organizationId
      },
      include: [
        {
          model: Organization,
          as: 'organization'
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return sendSuccess(res, 'Roles retrieved successfully', { roles });
  } catch (error) {
    console.error('Error getting roles:', error);
    return sendError(res, 'Failed to retrieve roles', undefined, 500);
  }
};

/**
 * Get system roles (organization-independent)
 */
export const getSystemRoles = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const roles = await Role.findAll({
      where: {
        is_system_role: true,
        organization_id: null
      },
      order: [['created_at', 'DESC']]
    });

    return sendSuccess(res, 'System roles retrieved successfully', { roles });
  } catch (error) {
    console.error('Error getting system roles:', error);
    return sendError(res, 'Failed to retrieve system roles', undefined, 500);
  }
};

/**
 * Get role by ID
 */
export const getRoleById = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { organizationId } = req;

    const role = await Role.findOne({
      where: {
        id,
        ...(organizationId && { organization_id: organizationId })
      },
      include: [
        {
          model: Organization,
          as: 'organization'
        }
      ]
    });

    if (!role) {
      return sendError(res, 'Role not found', undefined, 404);
    }

    return sendSuccess(res, 'Role retrieved successfully', { role });
  } catch (error) {
    console.error('Error getting role:', error);
    return sendError(res, 'Failed to retrieve role', undefined, 500);
  }
};

/**
 * Create new role
 */
export const createRole = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { organizationId } = req;
    const roleData: CreateRoleData = req.body;

    // Validate required fields
    if (!roleData.name || !roleData.display_name || !roleData.permissions) {
      return sendError(res, 'Name, display name, and permissions are required', undefined, 400);
    }

    // Check if role name already exists in the organization
    const existingRole = await Role.findOne({
      where: {
        name: roleData.name,
        organization_id: organizationId
      }
    });

    if (existingRole) {
      return sendError(res, 'Role with this name already exists', undefined, 409);
    }

    // Validate permissions
    const validPermissions = Object.values(PERMISSIONS);
    const invalidPermissions = roleData.permissions.filter(
      permission => !validPermissions.includes(permission as any)
    );

    if (invalidPermissions.length > 0) {
      return sendError(res, `Invalid permissions: ${invalidPermissions.join(', ')}`, undefined, 400);
    }

    // Create role
    const role = await Role.create({
      ...roleData,
      organization_id: organizationId,
      is_system_role: false
    });

    return sendSuccess(res, 'Role created successfully', { role }, 201);
  } catch (error) {
    console.error('Error creating role:', error);
    return sendError(res, 'Failed to create role', undefined, 500);
  }
};

/**
 * Update role
 */
export const updateRole = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { organizationId } = req;
    const updateData: UpdateRoleData = req.body;

    // Find role
    const role = await Role.findOne({
      where: {
        id,
        organization_id: organizationId
      }
    });

    if (!role) {
      return sendError(res, 'Role not found', undefined, 404);
    }

    // Check if it's a system role
    if (role.is_system_role) {
      return sendError(res, 'Cannot modify system roles', undefined, 403);
    }

    // Validate permissions if provided
    if (updateData.permissions) {
      const validPermissions = Object.values(PERMISSIONS);
      const invalidPermissions = updateData.permissions.filter(
        permission => !validPermissions.includes(permission as any)
      );

      if (invalidPermissions.length > 0) {
        return sendError(res, `Invalid permissions: ${invalidPermissions.join(', ')}`, undefined, 400);
      }
    }

    // Check if name is being changed and if it conflicts
    if (updateData.name && updateData.name !== role.name) {
      const existingRole = await Role.findOne({
        where: {
          name: updateData.name,
          organization_id: organizationId,
          id: { $ne: id } as any
        }
      });

      if (existingRole) {
        return sendError(res, 'Role with this name already exists', undefined, 409);
      }
    }

    // Update role
    await role.update(updateData);

    return sendSuccess(res, 'Role updated successfully', { role });
  } catch (error) {
    console.error('Error updating role:', error);
    return sendError(res, 'Failed to update role', undefined, 500);
  }
};

/**
 * Delete role
 */
export const deleteRole = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { organizationId } = req;

    // Find role
    const role = await Role.findOne({
      where: {
        id,
        organization_id: organizationId
      }
    });

    if (!role) {
      return sendError(res, 'Role not found', undefined, 404);
    }

    // Check if it's a system role
    if (role.is_system_role) {
      return sendError(res, 'Cannot delete system roles', undefined, 403);
    }

    // Check if role is assigned to any users
    const userRoleCount = await UserRole.count({
      where: {
        role_id: id,
        status: 'active'
      }
    });

    if (userRoleCount > 0) {
      return sendError(res, 'Cannot delete role that is assigned to users', undefined, 409);
    }

    // Delete role
    await role.destroy();

    return sendSuccess(res, 'Role deleted successfully');
  } catch (error) {
    console.error('Error deleting role:', error);
    return sendError(res, 'Failed to delete role', undefined, 500);
  }
};

/**
 * Get users assigned to a role
 */
export const getRoleUsers = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { organizationId } = req;

    // Check if role exists
    const role = await Role.findOne({
      where: {
        id,
        ...(organizationId && { organization_id: organizationId })
      }
    });

    if (!role) {
      return sendError(res, 'Role not found', undefined, 404);
    }

    // Get users with this role
    const userRoles = await UserRole.findAll({
      where: {
        role_id: id,
        organization_id: organizationId,
        status: 'active'
      },
      include: [
        {
          model: User,
          attributes: ['id', 'name', 'email', 'status']
        }
      ]
    });

    const users = userRoles.map(userRole => ({
      ...userRole.user.toJSON(),
      assigned_at: userRole.assigned_at,
      is_primary: userRole.is_primary
    }));

    return sendSuccess(res, 'Role users retrieved successfully', { users });
  } catch (error) {
    console.error('Error getting role users:', error);
    return sendError(res, 'Failed to retrieve role users', undefined, 500);
  }
};

/**
 * Assign role to user
 */
export const assignRoleToUser = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params; // role id
    const { userId, isPrimary = false } = req.body;
    const { organizationId, userId: assignedBy } = req;

    if (!userId) {
      return sendError(res, 'User ID is required', undefined, 400);
    }

    // Check if role exists
    const role = await Role.findOne({
      where: {
        id,
        ...(organizationId && { organization_id: organizationId })
      }
    });

    if (!role) {
      return sendError(res, 'Role not found', undefined, 404);
    }

    // Check if user exists
    const user = await User.findByPk(userId);
    if (!user) {
      return sendError(res, 'User not found', undefined, 404);
    }

    // Check if user already has this role in this organization
    const existingUserRole = await UserRole.findOne({
      where: {
        user_id: userId,
        role_id: id,
        organization_id: organizationId
      }
    });

    if (existingUserRole) {
      if (existingUserRole.status === 'active') {
        return sendError(res, 'User already has this role', undefined, 409);
      } else {
        // Reactivate the role
        await existingUserRole.update({
          status: 'active',
          is_primary: isPrimary,
          assigned_at: new Date(),
          assigned_by: assignedBy
        });

        return sendSuccess(res, 'Role assigned successfully', { userRole: existingUserRole });
      }
    }

    // Create new user role assignment
    const userRole = await UserRole.create({
      user_id: userId,
      role_id: id,
      organization_id: organizationId!,
      is_primary: isPrimary,
      status: 'active',
      assigned_at: new Date(),
      assigned_by: assignedBy
    });

    return sendSuccess(res, 'Role assigned successfully', { userRole }, 201);
  } catch (error) {
    console.error('Error assigning role:', error);
    return sendError(res, 'Failed to assign role', undefined, 500);
  }
};

/**
 * Remove role from user
 */
export const removeRoleFromUser = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params; // role id
    const { userId } = req.body;
    const { organizationId } = req;

    if (!userId) {
      return sendError(res, 'User ID is required', undefined, 400);
    }

    // Find user role assignment
    const userRole = await UserRole.findOne({
      where: {
        user_id: userId,
        role_id: id,
        organization_id: organizationId,
        status: 'active'
      }
    });

    if (!userRole) {
      return sendError(res, 'User role assignment not found', undefined, 404);
    }

    // Deactivate the role assignment
    await userRole.update({ status: 'inactive' });

    return sendSuccess(res, 'Role removed successfully');
  } catch (error) {
    console.error('Error removing role:', error);
    return sendError(res, 'Failed to remove role', undefined, 500);
  }
};

/**
 * Get available permissions
 */
export const getAvailablePermissions = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const permissions = Object.entries(PERMISSIONS).map(([key, value]) => ({
      key,
      value,
      category: value.split(':')[0],
      action: value.split(':')[1]
    }));

    // Group permissions by category
    const groupedPermissions = permissions.reduce((acc, permission) => {
      if (!acc[permission.category]) {
        acc[permission.category] = [];
      }
      acc[permission.category].push(permission);
      return acc;
    }, {} as Record<string, typeof permissions>);

    return sendSuccess(res, 'Permissions retrieved successfully', {
      permissions,
      groupedPermissions
    });
  } catch (error) {
    console.error('Error getting permissions:', error);
    return sendError(res, 'Failed to retrieve permissions', undefined, 500);
  }
};
