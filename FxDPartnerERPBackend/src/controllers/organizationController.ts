import { Request, Response } from 'express';
import { Organization, UpdateOrganizationData } from '../models/Organization';

export const getCurrentOrganization = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const organization = await Organization.findByPk(organizationId);

    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    res.json(organization);
  } catch (error) {
    console.error('Error fetching current organization:', error);
    res.status(500).json({ error: 'Failed to fetch organization' });
  }
};

export const updateCurrentOrganization = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const organization = await Organization.findByPk(organizationId);

    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const updateData: UpdateOrganizationData = req.body;

    // Validate commission percentage if provided
    if (updateData.default_commission_percentage !== undefined) {
      const commission = updateData.default_commission_percentage;
      if (commission < 0 || commission > 100) {
        return res.status(400).json({ 
          error: 'Commission percentage must be between 0 and 100' 
        });
      }
    }

    // Update organization
    await organization.update(updateData);

    // Fetch updated organization
    const updatedOrganization = await Organization.findByPk(organizationId);
    res.json(updatedOrganization);
  } catch (error) {
    console.error('Error updating organization:', error);
    res.status(500).json({ error: 'Failed to update organization' });
  }
};
