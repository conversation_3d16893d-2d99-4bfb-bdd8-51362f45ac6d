import { Transaction } from 'sequelize';
import { 
  InventoryTransaction, 
  CreateInventoryTransactionData, 
  InventoryTransactionFilters,
  CurrentInventory,
  Product,
  SKU,
  User
} from '../models';
import { Op } from 'sequelize';

export class InventoryTransactionService {
  /**
   * Create an inventory transaction and update the current inventory
   */
  static async createInventoryTransaction(
    data: CreateInventoryTransactionData,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    console.log('Creating inventory transaction with data:', data);
    
    try {
      // Create the inventory transaction record
      console.log('inventoryTransaction:', data);
      const inventoryTransaction = await InventoryTransaction.create(data as any, { transaction }); 
      console.log('Inventory transaction created:', inventoryTransaction.id);
      
      // Continue with inventory update
      return await this.updateInventoryAfterTransaction(inventoryTransaction, data, transaction);
    } catch (error: any) {
      // Handle unique constraint violation by creating a unique reference ID
      if (error.name === 'SequelizeUniqueConstraintError' || error.code === 'ER_DUP_ENTRY') {
        console.log('🔄 Duplicate reference ID detected, creating unique ID...');
        
        // Create a much shorter unique reference ID
        const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
        const productIdShort = data.product_id.slice(-4); // Last 4 chars
        const skuIdShort = data.sku_id.slice(-4); // Last 4 chars
        const uniqueReferenceId = `VA_${productIdShort}_${skuIdShort}_${timestamp}`;
        
        // Retry with unique reference ID
        const modifiedData = { ...data, reference_id: uniqueReferenceId };
        console.log('🔄 Retrying with unique reference ID:', uniqueReferenceId);
        
        const inventoryTransaction = await InventoryTransaction.create(modifiedData as any, { transaction });
        console.log('Inventory transaction created with unique ID:', inventoryTransaction.id);
        
        // Continue with inventory update
        return await this.updateInventoryAfterTransaction(inventoryTransaction, data, transaction);
      }
      
      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Update inventory after transaction creation
   */
  private static async updateInventoryAfterTransaction(
    inventoryTransaction: InventoryTransaction,
    data: CreateInventoryTransactionData,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {

    // Get unit_type - ALWAYS required for proper inventory targeting
    let unitType = data.unit_type;
    if (!unitType) {
      const sku = await SKU.findByPk(data.sku_id, { transaction });
      if (sku) {
        unitType = sku.unit_type as 'box' | 'loose';
      }
    }

    // unit_type is mandatory for current stock calculations
    if (!unitType) {
      throw new Error(`unit_type is required for inventory transactions. SKU ${data.sku_id} not found or has no unit_type`);
    }

    // Build where clause - ALWAYS include unit_type to ensure proper inventory targeting
    const whereClause: any = {
      product_id: data.product_id,
      sku_id: data.sku_id,
      organization_id: data.organization_id,
      unit_type: unitType // ALWAYS filter by unit_type
    };

    // Check if inventory record exists
    const existingInventory = await CurrentInventory.findOne({
      where: whereClause,
      transaction
    });
    
    console.log('Existing inventory record:', existingInventory ? existingInventory.id : 'NOT FOUND');
    console.log('Current available_quantity:', existingInventory ? existingInventory.available_quantity : 'N/A');
    console.log('Quantity change:', data.quantity_change);
    console.log('Unit type:', unitType);

    if (!existingInventory) {
      console.error('No inventory record found for product_id:', data.product_id, 'sku_id:', data.sku_id, 'unit_type:', unitType);
      throw new Error(`No inventory record found for product ${data.product_id}, SKU ${data.sku_id}, and unit_type ${unitType}`);
    }

    // Update the current inventory based on the quantity change - ALWAYS include unit_type
    const updateWhereClause = {
      product_id: data.product_id,
      sku_id: data.sku_id,
      organization_id: data.organization_id,
      unit_type: unitType // ALWAYS filter by unit_type
    };
    
    const [affectedRows] = await CurrentInventory.increment('available_quantity', {
      by: data.quantity_change,
      where: updateWhereClause,
      transaction
    });
    
    console.log('Inventory increment affected rows:', affectedRows, 'for unit_type:', unitType);

    // Update weight if provided
    if (data.weight_change && data.weight_change !== 0) {
      await CurrentInventory.increment('total_weight', {
        by: data.weight_change,
        where: updateWhereClause,
        transaction
      });
    }

    // Update last_updated_at timestamp
    await CurrentInventory.update(
      { last_updated_at: new Date() },
      {
        where: updateWhereClause,
        transaction
      }
    );

    return inventoryTransaction;
  }

  /**
   * Get inventory transaction history for a specific product/SKU with unit_type filtering
   */
  static async getInventoryTransactionHistory(
    organizationId: string,
    productId: string,
    skuId: string,
    unitType?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ transactions: InventoryTransaction[]; total: number }> {
    const whereClause: any = {
      organization_id: organizationId,
      product_id: productId,
      sku_id: skuId
    };

    // Filter by unit_type if provided
    if (unitType) {
      whereClause.unit_type = unitType;
    }

    const { rows: transactions, count: total } = await InventoryTransaction.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Product,
          attributes: ['name']
        },
        {
          model: SKU,
          attributes: ['sku_code', 'unit_type']
        },
        {
          model: User,
          as: 'performer',
          attributes: ['name', 'email']
        }
      ],
      order: [['created_at', 'DESC']],
      limit,
      offset
    });

    return { transactions, total };
  }

  /**
   * Get transactions by reference (e.g., all transactions for a GRN request)
   */
  static async getTransactionsByReference(
    organizationId: string,
    referenceType: string,
    referenceId: string
  ): Promise<InventoryTransaction[]> {
    return await InventoryTransaction.findAll({
      where: {
        organization_id: organizationId,
        reference_type: referenceType,
        reference_id: referenceId
      },
      include: [
        {
          model: Product,
          attributes: ['name']
        },
        {
          model: SKU,
          attributes: ['sku_code', 'unit_type']
        },
        {
          model: User,
          as: 'performer',
          attributes: ['name', 'email']
        }
      ],
      order: [['created_at', 'DESC']]
    });
  }

  /**
   * Get filtered inventory transactions
   */
  static async getFilteredTransactions(
    filters: InventoryTransactionFilters,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ transactions: InventoryTransaction[]; total: number }> {
    const whereClause: any = {
      organization_id: filters.organization_id
    };

    // Add optional filters
    if (filters.product_id) {
      whereClause.product_id = filters.product_id;
    }

    if (filters.sku_id) {
      whereClause.sku_id = filters.sku_id;
    }

    if (filters.transaction_type) {
      whereClause.transaction_type = filters.transaction_type;
    }

    if (filters.reference_type) {
      whereClause.reference_type = filters.reference_type;
    }

    if (filters.reference_id) {
      whereClause.reference_id = filters.reference_id;
    }

    if (filters.performed_by) {
      whereClause.performed_by = filters.performed_by;
    }

    if (filters.date_from || filters.date_to) {
      whereClause.created_at = {};
      if (filters.date_from) {
        whereClause.created_at[Op.gte] = filters.date_from;
      }
      if (filters.date_to) {
        whereClause.created_at[Op.lte] = filters.date_to;
      }
    }

    const { rows: transactions, count: total } = await InventoryTransaction.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Product,
          attributes: ['name']
        },
        {
          model: SKU,
          attributes: ['sku_code', 'unit_type']
        },
        {
          model: User,
          as: 'performer',
          attributes: ['name', 'email']
        }
      ],
      order: [['created_at', 'DESC']],
      limit,
      offset
    });

    return { transactions, total };
  }

  /**
   * Create a return to inventory transaction for GRN
   */
  static async createGRNReturnTransaction(
    organizationId: string,
    productId: string,
    skuId: string,
    returnQuantity: number,
    grnRequestId: string,
    performedBy?: string,
    notes?: string,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    const transactionData: CreateInventoryTransactionData = {
      organization_id: organizationId,
      product_id: productId,
      sku_id: skuId,
      transaction_type: 'return_to_inventory',
      quantity_change: returnQuantity, // Positive value for returns
      reference_type: 'grn_return',
      reference_id: grnRequestId,
      performed_by: performedBy,
      reason: 'GRN Return - Items returned to inventory',
      notes: notes
    };

    return await this.createInventoryTransaction(transactionData, transaction);
  }

  /**
   * Create a sale transaction (negative quantity change)
   */
  static async createSaleTransaction(
    organizationId: string,
    productId: string,
    skuId: string,
    saleQuantity: number,
    salesOrderId: string,
    performedBy?: string,
    notes?: string,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    const transactionData: CreateInventoryTransactionData = {
      organization_id: organizationId,
      product_id: productId,
      sku_id: skuId,
      transaction_type: 'sale',
      quantity_change: -Math.abs(saleQuantity), // Negative value for sales
      reference_type: 'sales_order',
      reference_id: salesOrderId,
      performed_by: performedBy,
      reason: 'Sale - Items sold to customer',
      notes: notes
    };

    return await this.createInventoryTransaction(transactionData, transaction);
  }

  /**
   * Create a sale transaction with unit type (negative quantity change)
   */
  static async createSaleTransactionWithUnitType(
    organizationId: string,
    productId: string,
    skuId: string,
    saleQuantity: number,
    unitType: string,
    salesOrderId: string,
    performedBy?: string,
    notes?: string,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    const transactionData: CreateInventoryTransactionData = {
      organization_id: organizationId,
      product_id: productId,
      sku_id: skuId,
      transaction_type: 'sale',
      quantity_change: -Math.abs(saleQuantity), // Negative value for sales
      reference_type: 'sales_order',
      reference_id: salesOrderId,
      performed_by: performedBy,
      reason: 'Sale - Items sold to customer',
      notes: notes,
      unit_type: unitType as 'box' | 'loose' // Include unit_type to ensure correct inventory targeting
    };

    return await this.createInventoryTransaction(transactionData, transaction);
  }

  /**
   * Create a purchase transaction (positive quantity change)
   */
  static async createPurchaseTransaction(
    organizationId: string,
    productId: string,
    skuId: string,
    purchaseQuantity: number,
    purchaseRecordId: string,
    performedBy?: string,
    notes?: string,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    const transactionData: CreateInventoryTransactionData = {
      organization_id: organizationId,
      product_id: productId,
      sku_id: skuId,
      transaction_type: 'purchase',
      quantity_change: Math.abs(purchaseQuantity), // Positive value for purchases
      reference_type: 'purchase_record',
      reference_id: purchaseRecordId,
      performed_by: performedBy,
      reason: 'Purchase - Items added from supplier',
      notes: notes
    };

    return await this.createInventoryTransaction(transactionData, transaction);
  }

  /**
   * Create a vehicle arrival transaction (positive quantity change)
   */
  static async createVehicleArrivalTransaction(
    organizationId: string,
    productId: string,
    skuId: string,
    arrivalQuantity: number,
    vehicleArrivalId: string,
    performedBy?: string,
    notes?: string,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    const transactionData: CreateInventoryTransactionData = {
      organization_id: organizationId,
      product_id: productId,
      sku_id: skuId,
      transaction_type: 'vehicle_arrival',
      quantity_change: Math.abs(arrivalQuantity), // Positive value for arrivals
      reference_type: 'vehicle_arrival',
      reference_id: vehicleArrivalId,
      performed_by: performedBy,
      reason: 'Vehicle Arrival - Items received from vehicle',
      notes: notes
    };

    return await this.createInventoryTransaction(transactionData, transaction);
  }

  /**
   * Create a vehicle arrival transaction with unit type (positive quantity change)
   */
  static async createVehicleArrivalTransactionWithUnitType(
    organizationId: string,
    productId: string,
    skuId: string,
    arrivalQuantity: number,
    unitType: string,
    vehicleArrivalId: string,
    performedBy?: string,
    notes?: string,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    console.log('Creating vehicle arrival transaction with unit type:', unitType);
    const transactionData: CreateInventoryTransactionData = {
      organization_id: organizationId,
      product_id: productId,
      sku_id: skuId,
      transaction_type: 'vehicle_arrival',
      quantity_change: Math.abs(arrivalQuantity), // Positive value for arrivals
      reference_type: 'vehicle_arrival',
      reference_id: vehicleArrivalId,
      performed_by: performedBy,
      reason: 'Vehicle Arrival - Items received from vehicle',
      notes: notes,
      unit_type: unitType as 'box' | 'loose' // Include unit_type to ensure correct inventory targeting
    };

    return await this.createInventoryTransaction(transactionData, transaction);
  }

  /**
   * Create a manual adjustment transaction
   */
  static async createAdjustmentTransaction(
    organizationId: string,
    productId: string,
    skuId: string,
    adjustmentQuantity: number,
    reason: string,
    performedBy?: string,
    notes?: string,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    const transactionData: CreateInventoryTransactionData = {
      organization_id: organizationId,
      product_id: productId,
      sku_id: skuId,
      transaction_type: 'adjustment',
      quantity_change: adjustmentQuantity, // Can be positive or negative
      reference_type: 'manual_adjustment',
      reference_id: `adj_${Date.now()}`, // Generate a unique reference ID
      performed_by: performedBy,
      reason: reason,
      notes: notes
    };

    return await this.createInventoryTransaction(transactionData, transaction);
  }
}
