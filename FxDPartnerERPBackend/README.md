# FxD Partner Backend

A comprehensive Node.js, Express, and TypeScript backend for the FxD Partner system with MySQL database integration, featuring multi-tenant architecture and role-based access control.

## Table of Contents
- [Overview](#overview)
- [Key Features](#key-features)
- [Tech Stack](#tech-stack)
- [Database Architecture](#database-architecture)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Database Setup](#database-setup)
- [Migration System](#migration-system)
- [API Documentation](#api-documentation)
- [Authentication & Authorization](#authentication--authorization)
- [Role-Based Permission System](#role-based-permission-system)
- [Project Structure](#project-structure)
- [Environment Configuration](#environment-configuration)
- [Available Scripts](#available-scripts)
- [Development Guidelines](#development-guidelines)
- [Deployment](#deployment)
- [Troubleshooting](#troubleshooting)

## Overview

The FxD Partner Backend is a robust, scalable API server that provides:
- **Multi-tenant Architecture**: Organization-based data isolation
- **Role-Based Access Control**: Granular permission system
- **Comprehensive Business Logic**: Complete ERP functionality
- **RESTful API Design**: Standard HTTP methods and status codes
- **Type-Safe Development**: Full TypeScript implementation
- **Database Migrations**: Version-controlled schema evolution

## Key Features

### Core Architecture
- **Multi-tenant Support**: Complete data isolation per organization
- **JWT Authentication**: Secure token-based authentication system
- **Role-Based Permissions**: JSON-based granular permission control
- **Audit Trails**: Comprehensive logging and tracking
- **Data Validation**: Input validation and sanitization
- **Error Handling**: Standardized error responses

### Business Modules
- **Organization Management**: Multi-tenant organization support
- **User Management**: User accounts with role assignments
- **Partner Management**: Customer and supplier relationship management
- **Product Catalog**: Hierarchical product and SKU management
- **Procurement**: Vehicle arrivals and purchase record management
- **Sales Management**: Order processing and customer management
- **Inventory Control**: Real-time inventory tracking with negative inventory handling
- **Financial Management**: Payment processing and credit management

### Advanced Features
- **File Upload Support**: Document and attachment handling
- **Real-time Inventory**: Automatic inventory updates
- **Credit Management**: Customer credit limits and extensions
- **Negative Inventory Tracking**: Stock shortage management
- **Flexible Payment Terms**: Configurable payment and credit terms

## Tech Stack

### Backend Technologies
- **Node.js** - JavaScript runtime environment
- **Express.js** - Fast, unopinionated web framework
- **TypeScript** - Type-safe JavaScript development
- **Sequelize** - Promise-based ORM for MySQL
- **MySQL2** - MySQL client for Node.js

### Security & Authentication
- **JWT (jsonwebtoken)** - Token-based authentication
- **bcrypt** - Password hashing and verification
- **Helmet** - Security headers middleware
- **CORS** - Cross-origin resource sharing configuration

### Development Tools
- **Nodemon** - Auto-restart during development
- **Morgan** - HTTP request logging
- **Multer** - File upload handling
- **dotenv** - Environment variable management
- **Reflect Metadata** - Decorator metadata support

## Database Architecture

### Multi-Tenant Design
The system uses organization-based multi-tenancy where each organization's data is completely isolated:

```mermaid
erDiagram
    organizations ||--o{ users : "has many"
    organizations ||--o{ products : "has many"
    organizations ||--o{ customers : "has many"
    organizations ||--o{ suppliers : "has many"
    organizations ||--o{ sales_orders : "has many"
    organizations ||--o{ purchase_records : "has many"
    organizations ||--o{ current_inventory : "has many"
    organizations ||--o{ payments : "has many"
    organizations ||--o{ roles : "has many"
    
    users ||--o{ user_organizations : "belongs to many"
    users ||--o{ user_roles : "has many"
    roles ||--o{ user_roles : "has many"
    
    products ||--o{ skus : "has many"
    products ||--o{ current_inventory : "has many"
    skus ||--o{ current_inventory : "has many"
    
    customers ||--o{ sales_orders : "has many"
    suppliers ||--o{ purchase_records : "has many"
    
    sales_orders ||--o{ sales_order_items : "has many"
    sales_orders ||--o{ sales_order_payments : "has many"
    
    purchase_records ||--o{ purchase_record_items : "has many"
    purchase_records ||--o{ purchase_record_costs : "has many"
    
    vehicle_arrivals ||--o{ vehicle_arrival_items : "has many"
    vehicle_arrivals ||--o{ vehicle_arrival_attachments : "has many"
```

### Core Tables

#### Organizations & Users
- **organizations**: Multi-tenant organization management
- **users**: User accounts with organization relationships
- **user_organizations**: Many-to-many user-organization relationships
- **user_roles**: User role assignments per organization
- **user_details**: Extended user profile information
- **roles**: Role definitions with JSON permissions

#### Business Entities
- **products**: Product catalog with categories
- **skus**: Stock Keeping Units with variants
- **customers**: Customer relationship management
- **suppliers**: Supplier relationship management

#### Operational Tables
- **vehicle_arrivals**: Incoming vehicle tracking
- **vehicle_arrival_items**: Items in vehicle arrivals
- **vehicle_arrival_attachments**: Document attachments
- **purchase_records**: Purchase order management
- **purchase_record_items**: Line items in purchase records
- **purchase_record_costs**: Additional costs and fees
- **sales_orders**: Sales order processing
- **sales_order_items**: Line items in sales orders
- **sales_order_payments**: Payment tracking for orders

#### Inventory & Finance
- **current_inventory**: Real-time inventory levels
- **negative_inventory**: Stock shortage tracking
- **payments**: Financial transaction management
- **customer_credit_extensions**: Credit limit management

## Prerequisites

- **Node.js** (v16 or higher)
- **MySQL** (v8.0 or higher)
- **npm** or **yarn**
- **Git**

## Installation

### 1. Clone and Navigate
```bash
git clone https://github.com/Vegrow-Tech/FxDPartnerERP.git
cd FxDPartnerERP/FxDPartnerERPBackend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Setup
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=fxd_partner_erp

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
```

## Database Setup

### 1. Create Database
```sql
CREATE DATABASE fxd_partner_erp;
CREATE USER 'erp_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON fxd_partner_erp.* TO 'erp_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. Run Migrations
```bash
npm run migrate
```

This will execute all 23+ migration files in sequence, creating:
- Core organization and user tables
- Business entity tables
- Operational workflow tables
- Role and permission system
- Junction tables for relationships

### 3. Verify Setup
```bash
npm run dev
```

Check the console for successful database connection and table creation.

## Migration System

### Migration Files Overview
The system includes 23+ migration files that build the complete schema:

1. **001_create_organizations_and_users.sql** - Core multi-tenant structure
2. **002_create_business_tables.sql** - Business operational tables
3. **003_create_additional_tables.sql** - Extended functionality
4. **004-012** - Schema refinements and status enum fixes
5. **013_create_roles_table.sql** - Role-based permission system
6. **014-015** - Payment system enhancements
7. **016-017** - Page-based permission system
8. **018** - User-organization and user-role junction tables
9. **019-023** - Permission system refinements

### Creating New Migrations
```bash
# Create a new migration file
touch migrations/024_your_migration_name.sql

# Add your SQL commands
# Run migration
npm run migrate
```

### Migration Best Practices
- Always use `IF NOT EXISTS` for table creation
- Use `INSERT IGNORE` for default data
- Include proper foreign key constraints
- Add appropriate indexes for performance
- Test migrations on development database first

## API Documentation

### Base URL
```
http://localhost:3001/api
```

### Authentication Endpoints

#### POST /auth/login
Login with email and password
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

Response:
```json
{
  "success": true,
  "data": {
    "token": "jwt_token_here",
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "firstName": "Admin",
      "lastName": "User",
      "role": "super_admin",
      "organizationId": "default-org-id"
    }
  }
}
```

### Business Entity Endpoints

#### Customers
- `GET /customers` - List all customers (organization-filtered)
- `POST /customers` - Create new customer
- `GET /customers/:id` - Get customer details
- `PUT /customers/:id` - Update customer
- `DELETE /customers/:id` - Delete customer

#### Suppliers
- `GET /suppliers` - List all suppliers
- `POST /suppliers` - Create new supplier
- `GET /suppliers/:id` - Get supplier details
- `PUT /suppliers/:id` - Update supplier
- `DELETE /suppliers/:id` - Delete supplier

#### Products & SKUs
- `GET /products` - List all products
- `POST /products` - Create new product
- `GET /products/:id/skus` - Get product SKUs
- `POST /skus` - Create new SKU

#### Sales Orders
- `GET /sales-orders` - List sales orders
- `POST /sales-orders` - Create new sales order
- `GET /sales-orders/:id` - Get order details
- `PUT /sales-orders/:id/status` - Update order status
- `POST /sales-orders/:id/payments` - Add payment

#### Purchase Records
- `GET /purchase-records` - List purchase records
- `POST /purchase-records` - Create purchase record
- `GET /purchase-records/:id` - Get record details
- `PUT /purchase-records/:id/close` - Close purchase record

#### Inventory
- `GET /inventory` - Current inventory levels
- `POST /inventory/adjust` - Adjust inventory
- `GET /inventory/negative` - Negative inventory items

#### Payments
- `GET /payments` - List all payments
- `POST /payments` - Record new payment
- `GET /payments/summary` - Payment summary

### Request/Response Format

All API responses follow this structure:
```json
{
  "success": true|false,
  "data": {}, // Response data
  "message": "Success message",
  "error": "Error message (if success: false)"
}
```

### Error Handling
- **400 Bad Request**: Invalid input data
- **401 Unauthorized**: Missing or invalid JWT token
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server error

## Authentication & Authorization

### JWT Token Structure
```json
{
  "userId": "user_id",
  "organizationId": "org_id",
  "role": "admin",
  "permissions": ["user:read", "customer:create", ...],
  "iat": 1234567890,
  "exp": 1234567890
}
```

### Authentication Flow
```mermaid
sequenceDiagram
    participant C as Client
    participant A as Auth Middleware
    participant D as Database
    participant R as Route Handler
    
    C->>A: Request with JWT Token
    A->>A: Verify JWT Signature
    A->>D: Get User & Organization
    D-->>A: User Data
    A->>A: Check Permissions
    A->>R: Authorized Request
    R->>D: Query with Org Filter
    D-->>R: Filtered Data
    R-->>C: Response
```

### Organization-Based Data Filtering
All database queries automatically include organization filtering:
```sql
SELECT * FROM customers WHERE organization_id = ? AND status = 'active'
```

## Role-Based Permission System

### Permission Structure
Permissions are stored as JSON arrays in the roles table:
```json
[
  "user:create",
  "user:read", 
  "user:update",
  "customer:create",
  "inventory:adjust",
  "reports:export"
]
```

### Default System Roles

#### Administrator
- Full access to all features
- User and role management
- Organization settings
- All CRUD operations

#### Manager
- Approval permissions
- Most CRUD operations
- Reports and analytics
- Limited user management

#### Sales Executive
- Customer management
- Sales order creation
- Payment tracking
- Sales reports

#### Purchase Executive
- Supplier management
- Purchase record creation
- Inventory viewing
- Purchase reports

#### Inventory Manager
- Product and SKU management
- Inventory adjustments
- Stock reports
- Negative inventory management

#### Viewer
- Read-only access
- Basic reports
- No modification permissions

### Permission Checking
```typescript
// Middleware checks permissions
const hasPermission = (requiredPermission: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const userPermissions = req.user.permissions;
    if (userPermissions.includes(requiredPermission)) {
      next();
    } else {
      res.status(403).json({ success: false, error: 'Insufficient permissions' });
    }
  };
};

// Usage in routes
router.post('/customers', authenticate, hasPermission('customer:create'), createCustomer);
```

## Project Structure

```
FxDPartnerERPBackend/
├── src/
│   ├── admin/                 # Admin module
│   │   ├── controllers/       # Admin controllers
│   │   ├── middleware/        # Admin middleware
│   │   ├── routes/           # Admin routes
│   │   └── services/         # Admin services
│   ├── config/               # Configuration files
│   │   ├── database.ts       # Database connection
│   │   ├── environment.ts    # Environment variables
│   │   └── sequelize.ts      # Sequelize configuration
│   ├── controllers/          # Business logic controllers
│   │   ├── customerController.ts
│   │   ├── inventoryController.ts
│   │   ├── paymentController.ts
│   │   ├── productController.ts
│   │   ├── purchaseRecordController.ts
│   │   ├── salesController.ts
│   │   ├── supplierController.ts
│   │   └── vehicleArrivalController.ts
│   ├── models/               # Sequelize models
│   │   ├── index.ts          # Model associations
│   │   ├── Customer.ts
│   │   ├── Inventory.ts
│   │   ├── Organization.ts
│   │   ├── Payment.ts
│   │   ├── Product.ts
│   │   ├── PurchaseRecord.ts
│   │   ├── Role.ts
│   │   ├── SalesOrder.ts
│   │   ├── Supplier.ts
│   │   ├── User.ts
│   │   ├── UserDetails.ts
│   │   ├── UserOrganization.ts
│   │   ├── UserRole.ts
│   │   └── VehicleArrival.ts
│   ├── routes/               # API route definitions
│   │   ├── customerRoutes.ts
│   │   ├── inventoryRoutes.ts
│   │   ├── partners.ts
│   │   ├── paymentRoutes.ts
│   │   ├── procurementRoutes.ts
│   │   ├── productRoutes.ts
│   │   ├── purchaseRecordRoutes.ts
│   │   ├── salesRoutes.ts
│   │   ├── supplierRoutes.ts
│   │   └── vehicleArrivalRoutes.ts
│   ├── scripts/              # Utility scripts
│   │   ├── migrate.ts        # Migration runner
│   │   └── seed-pages.ts     # Data seeding
│   ├── types/                # TypeScript type definitions
│   │   └── common.ts
│   ├── utils/                # Utility functions
│   │   ├── migration.ts      # Migration utilities
│   │   └── response.ts       # Response formatting
│   └── index.ts              # Main server file
├── migrations/               # Database migration files
│   ├── 001_create_organizations_and_users.sql
│   ├── 002_create_business_tables.sql
│   ├── ...
│   └── 023_drop_assigned_by_foreign_key.sql
├── scripts/                  # Build and utility scripts
│   └── verify-migration.js
├── .env.example              # Environment template
├── package.json              # Dependencies and scripts
├── tsconfig.json             # TypeScript configuration
├── nodemon.json              # Nodemon configuration
└── README.md                 # This file
```

## Environment Configuration

### Development Environment (.env)
```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=fxd_partner_erp

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE=./logs/app.log

# Email Configuration (if needed)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

### Production Environment
```env
NODE_ENV=production
PORT=3001
DB_HOST=your_production_db_host
DB_NAME=fxd_partner_erp_prod
JWT_SECRET=your_very_secure_jwt_secret
CORS_ORIGIN=https://your-frontend-domain.com
LOG_LEVEL=error
```

## Available Scripts

### Development Scripts
```bash
npm run dev          # Start development server with auto-restart
npm run build        # Build TypeScript to JavaScript
npm start            # Start production server
npm run migrate      # Run database migrations
```

### Database Scripts
```bash
npm run migrate      # Run all pending migrations
npm run seed         # Seed database with default data
npm run db:reset     # Reset database (development only)
```

### Utility Scripts
```bash
npm test             # Run tests (when implemented)
npm run lint         # Code linting (when configured)
npm run format       # Code formatting (when configured)
```

## Development Guidelines

### Code Style
- Use TypeScript for all new code
- Follow ESLint configuration
- Use async/await for asynchronous operations
- Implement proper error handling
- Add JSDoc comments for complex functions

### Database Guidelines
- Always include organization_id in queries
- Use transactions for multi-table operations
- Implement proper indexing for performance
- Follow naming conventions (snake_case for columns)
- Add foreign key constraints

### API Guidelines
- Use RESTful conventions
- Implement proper HTTP status codes
- Validate all input data
- Return consistent response format
- Include proper error messages

### Security Guidelines
- Validate and sanitize all inputs
- Use parameterized queries
- Implement rate limiting
- Log security events
- Keep dependencies updated

## Deployment

### Production Setup
1. **Server Requirements**:
   - Node.js v16+
   - MySQL 8.0+
   - SSL certificate for HTTPS
   - Process manager (PM2 recommended)

2. **Database Setup**:
   ```bash
   # Create production database
   mysql -u root -p
   CREATE DATABASE fxd_partner_erp_prod;
   
   # Run migrations
   NODE_ENV=production npm run migrate
   ```

3. **Application Deployment**:
   ```bash
   # Build application
   npm run build
   
   # Start with PM2
   pm2 start dist/index.js --name "fxd-erp-backend"
   pm2 save
   pm2 startup
   ```

### Environment Variables for Production
- Set secure JWT_SECRET
- Configure production database credentials
- Set appropriate CORS_ORIGIN
- Configure logging levels
- Set up SSL certificates

### Monitoring and Logging
- Use PM2 for process management
- Implement application logging
- Set up database monitoring
- Configure error tracking
- Monitor API performance

## Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check MySQL service
sudo systemctl status mysql

# Test connection
mysql -u your_username -p -h localhost

# Check environment variables
echo $DB_HOST $DB_USER $DB_NAME
```

#### Migration Issues
```bash
# Check migration status
npm run migrate

# Reset database (development only)
DROP DATABASE fxd_partner_erp;
CREATE DATABASE fxd_partner_erp;
npm run migrate
```

#### Authentication Issues
- Verify JWT_SECRET is set
- Check token expiration
- Validate user permissions
- Check organization relationships

#### Performance Issues
- Add database indexes
- Optimize queries
- Implement caching
- Monitor slow queries

### Debugging
```bash
# Enable debug logging
NODE_ENV=development LOG_LEVEL=debug npm run dev

# Check application logs
tail -f logs/app.log

# Monitor database queries
# Enable MySQL query logging
```

### Getting Help
- Check the frontend README for integration details
- Review migration files for schema understanding
- Check controller files for API implementation
- Review model files for data relationships

---

## API Testing

### Using curl
```bash
# Login
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Get customers (with token)
curl -X GET http://localhost:3001/api/customers \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Using Postman
Import the API collection (if available) or create requests manually with:
- Base URL: `http://localhost:3001/api`
- Authorization: Bearer Token
- Content-Type: application/json

---

Built with ❤️ by the Vegrow Tech Team

**Related Documentation**:
- [Frontend Documentation](../FxDPartnerERP/README.md)
- [Database Schema Details](./SCHEMA.md)
- [API Reference](./API.md)
- [Deployment Guide](./DEPLOYMENT.md)
