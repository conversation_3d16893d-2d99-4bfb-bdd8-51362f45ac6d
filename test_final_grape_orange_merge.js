const axios = require('axios');

const headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'x-organization-id': 'default-org-id',
  'Content-Type': 'application/json'
};

async function testFinalMerge() {
  try {
    console.log('=== Final Grape-Orange Merge Test ===\n');
    
    // First, let's see what we have
    const inventoryResponse = await axios.get('http://localhost:9000/api/inventory', { headers });
    
    console.log('Current inventory:');
    inventoryResponse.data.forEach(item => {
      console.log(`- ${item.product_name} (${item.sku_code}): ${item.available_quantity}`);
    });
    
    // Let's create a fresh grape product with the original setup
    console.log('\nCreating fresh grape product...');
    
    // Create grape product
    const productData = {
      name: 'FreshGrape',
      description: 'Fresh Grape product for testing',
      status: 'active'
    };
    
    const productResponse = await axios.post('http://localhost:9000/api/products', productData, { headers });
    const grapeProduct = productResponse.data;
    console.log('Created product:', grapeProduct.name);
    
    // Create SKU
    const skuData = {
      product_id: grapeProduct.id,
      code: 'fresh-gi',
      unit_type: 'box',
      unit_weight: 1,
      status: 'active'
    };
    
    const skuResponse = await axios.post('http://localhost:9000/api/products/skus', skuData, { headers });
    const grapeSku = skuResponse.data;
    console.log('Created SKU:', grapeSku.code);
    
    // Create inventory with -1000
    const inventoryData = {
      product_id: grapeProduct.id,
      sku_id: grapeSku.id,
      product_name: grapeProduct.name,
      sku_code: grapeSku.code,
      unit_type: grapeSku.unit_type,
      available_quantity: -1000,
      total_weight: 0
    };
    
    await axios.post('http://localhost:9000/api/inventory', inventoryData, { headers });
    console.log('Created inventory with -1000 quantity');
    
    // Get Orange current quantity
    const orangeBefore = inventoryResponse.data.find(item => item.product_name === 'Orange' && item.sku_code === 'apple');
    const orangeQuantityBefore = orangeBefore ? orangeBefore.available_quantity : 0;
    console.log(`\nOrange quantity before merge: ${orangeQuantityBefore}`);
    console.log(`FreshGrape quantity: -1000`);
    console.log(`Expected after merge: ${orangeQuantityBefore + (-1000)}`);
    
    // Now perform the merge with both product name and SKU code change
    console.log('\nPerforming merge (changing both product name to "Orange" and SKU code to "apple")...');
    const mergeData = {
      current_product_id: grapeProduct.id,
      current_sku_id: grapeSku.id,
      reason: "Complete merge test",
      new_product_name: "Orange",
      new_sku_code: "apple"
    };
    
    try {
      const mergeResponse = await axios.post('http://localhost:9000/api/inventory/update-sku', mergeData, { headers });
      console.log('✅ Merge successful!');
      console.log('Response:', mergeResponse.data.message);
      
      // Check final inventory
      const finalInventoryResponse = await axios.get('http://localhost:9000/api/inventory', { headers });
      const orangeAfter = finalInventoryResponse.data.find(item => item.product_name === 'Orange' && item.sku_code === 'apple');
      
      console.log(`\n=== Final Result ===`);
      console.log(`Orange quantity after merge: ${orangeAfter?.available_quantity}`);
      console.log(`Expected: ${orangeQuantityBefore + (-1000)}`);
      
      if (orangeAfter && orangeAfter.available_quantity === (orangeQuantityBefore + (-1000))) {
        console.log('✅ SUCCESS: Merge worked correctly!');
      } else {
        console.log('❌ FAIL: Merge quantities do not match!');
        
        // Show all inventory for debugging
        console.log('\nAll inventory after merge:');
        finalInventoryResponse.data.forEach(item => {
          console.log(`- ${item.product_name} (${item.sku_code}): ${item.available_quantity}`);
        });
      }
      
    } catch (error) {
      console.error('❌ Merge failed:', error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

testFinalMerge();
