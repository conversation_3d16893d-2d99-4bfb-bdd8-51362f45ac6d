const axios = require('axios');

const headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'x-organization-id': 'default-org-id',
  'Content-Type': 'application/json'
};

async function fixPomoInventory() {
  try {
    // Get the Pomo product
    const productsResponse = await axios.get('http://localhost:9000/api/products', { headers });
    const pomoProduct = productsResponse.data.find(p => p.name === 'Pomo');
    
    if (!pomoProduct) {
      console.log('Pomo product not found!');
      return;
    }
    
    console.log('Found Pomo product:', pomoProduct.id);
    
    // Create a SKU for Pomo
    console.log('\nCreating SKU "ab" for Pomo product...');
    
    const skuData = {
      product_id: pomoProduct.id,
      code: 'ab',
      unit_type: 'box',
      unit_weight: 1,
      status: 'active'
    };
    
    try {
      const skuResponse = await axios.post('http://localhost:9000/api/products/skus', skuData, { headers });
      console.log('SKU created successfully:', skuResponse.data);
      
      const newSku = skuResponse.data;
      
      // Now create inventory for this SKU
      console.log('\nCreating inventory for the new SKU...');
      
      const inventoryData = {
        product_id: pomoProduct.id,
        sku_id: newSku.id,
        product_name: pomoProduct.name,
        sku_code: newSku.code,
        unit_type: newSku.unit_type,
        available_quantity: 9765,  // 10000 - 235 (from the merge)
        total_weight: 9765
      };
      
      const inventoryResponse = await axios.post('http://localhost:9000/api/inventory', inventoryData, { headers });
      console.log('Inventory created successfully!');
      console.log('New inventory:', inventoryResponse.data);
      
    } catch (error) {
      if (error.response?.data?.error?.includes('already exists')) {
        console.log('SKU "ab" already exists for Pomo. Let\'s check the inventory directly.');
        
        // Get all inventory and check
        const inventoryResponse = await axios.get('http://localhost:9000/api/inventory', { headers });
        const allInventory = inventoryResponse.data;
        
        console.log('\n=== All Inventory ===');
        allInventory.forEach(item => {
          console.log(`${item.product_name} - ${item.sku_code}: ${item.available_quantity}`);
        });
        
        // Check if there's a mismatch between product and inventory
        const pomoInventory = allInventory.filter(item => item.product_name === 'Pomo' || item.product_id === pomoProduct.id);
        console.log('\n=== Pomo-related inventory ===');
        if (pomoInventory.length > 0) {
          pomoInventory.forEach(item => {
            console.log(`Product ID: ${item.product_id}, SKU: ${item.sku_code}, Quantity: ${item.available_quantity}`);
          });
        } else {
          console.log('No inventory found for Pomo product');
        }
      } else {
        console.error('Error creating SKU:', error.response?.data || error.message);
      }
    }
    
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

fixPomoInventory();
