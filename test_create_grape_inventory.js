const axios = require('axios');

const headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'x-organization-id': 'default-org-id',
  'Content-Type': 'application/json'
};

async function createGrapeInventory() {
  try {
    // First, create the grape product
    console.log('=== Creating Grape Product ===');
    const productData = {
      name: 'grape',
      description: 'Grape product',
      status: 'active'
    };
    
    const productResponse = await axios.post('http://localhost:9000/api/products', productData, { headers });
    console.log('Product created:', productResponse.data);
    
    const grapeProduct = productResponse.data;
    
    // Create SKU for grape
    console.log('\n=== Creating SKU "gi" for Grape ===');
    const skuData = {
      product_id: grapeProduct.id,
      code: 'gi',
      unit_type: 'box',
      unit_weight: 1,
      status: 'active'
    };
    
    const skuResponse = await axios.post('http://localhost:9000/api/products/skus', skuData, { headers });
    console.log('SKU created:', skuResponse.data);
    
    const grapeSku = skuResponse.data;
    
    // Create inventory with negative quantity
    console.log('\n=== Creating Inventory with -1000 quantity ===');
    const inventoryData = {
      product_id: grapeProduct.id,
      sku_id: grapeSku.id,
      product_name: grapeProduct.name,
      sku_code: grapeSku.code,
      unit_type: grapeSku.unit_type,
      available_quantity: -1000,
      total_weight: 0
    };
    
    const inventoryResponse = await axios.post('http://localhost:9000/api/inventory', inventoryData, { headers });
    console.log('Inventory created:', inventoryResponse.data);
    
    // Verify current inventory
    console.log('\n=== Current Inventory ===');
    const allInventory = await axios.get('http://localhost:9000/api/inventory', { headers });
    allInventory.data.forEach(item => {
      console.log(`${item.product_name} - ${item.sku_code}: ${item.available_quantity}`);
    });
    
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

createGrapeInventory();
