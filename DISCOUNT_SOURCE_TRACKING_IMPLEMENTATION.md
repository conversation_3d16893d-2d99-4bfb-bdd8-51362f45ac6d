# Discount Source Tracking Implementation Summary

## Overview
This implementation adds comprehensive discount source tracking to the ERP system, specifically addressing the requirement to show discount sources and PDD return quantities for sales orders.

## Implementation Details

### 1. Database Schema Changes

#### New Fields in `sales_orders` table:
- `discount_source` - ENUM('manual', 'pdd', 'promotional', 'customer_credit', 'bulk_discount', 'damaged_goods')
- `discount_reason` - TEXT field for additional context

#### New Table: `sales_order_discounts`
Detailed discount tracking table with the following fields:
- `id` - Primary key
- `organization_id` - Foreign key to organizations
- `sales_order_id` - Foreign key to sales_orders
- `sales_order_item_id` - Optional foreign key for item-level discounts
- `discount_type` - Type of discount (same enum as discount_source)
- `discount_amount` - Discount amount
- `discount_percentage` - Optional percentage
- `reason` - Reason for discount
- `return_quantity` - For PDD discounts - quantity returned
- `pdd_request_id` - Link to PDD request if applicable
- `applied_by` - User who applied the discount
- `applied_at` - When discount was applied
- `status` - ENUM('active', 'reversed', 'expired')
- `notes` - Additional notes

### 2. Backend Model Updates

#### Updated Models:
- **SalesOrder.ts**: Added new discount fields and relationship to SalesOrderDiscount
- **SalesOrderDiscount.ts**: New model for detailed discount tracking
- **Updated index.ts**: Exported new model

#### Migration File:
- **032_add_discount_tracking.sql**: Database migration with proper constraints and indexes

### 3. Backend API Enhancements

#### Updated `getSalesOrder` function:
- Includes new discount fields in response
- Fetches PDD return quantity information when discount source is 'pdd'
- Calculates total return quantities from approved PDD requests
- Provides detailed breakdown of returned items with:
  - Product names and SKU codes
  - Original vs returned quantities
  - PDD percentages and amounts
  - Request timestamps

#### New Response Fields:
- `discount_source` - Source of the discount
- `discount_reason` - Reason for the discount
- `pdd_return_quantity` - Total quantity returned for PDD discounts
- `pdd_return_details` - Detailed breakdown of PDD returns

### 4. Frontend Enhancements

#### Updated ViewSale.tsx:
- Enhanced discount display in Order Summary section
- Shows discount source with clear labeling:
  - PDD → "PDD"
  - customer_credit → "Customer Credit"
  - bulk_discount → "Bulk Discount"
  - promotional → "Promotional"
  - damaged_goods → "Damaged Goods"
  - manual → "Manual"

#### PDD-Specific Information:
- Displays return quantity when discount is from PDD
- Shows format: "X items returned" below discount amount
- Includes discount reason when available

### 5. Key Features Implemented

#### Discount Source Visibility:
✅ **Requirement**: "When we are showing discount show that discount is from where"
- Discount source is clearly displayed next to discount amount
- Color-coded labels for easy identification
- Supports all major discount types

#### PDD Return Quantity Display:
✅ **Requirement**: "If it is from PDD should also show how much quantity is returned for sales"
- Shows total return quantity for PDD discounts
- Displays "X items returned" below discount information
- Links to detailed PDD request data

#### Comprehensive Tracking:
- Full audit trail of discount applications
- Support for both order-level and item-level discounts
- Integration with existing PDD system
- Backward compatibility with existing discounts

### 6. Database Migration Strategy

#### Migration 032_add_discount_tracking.sql includes:
- Safe addition of new columns with NULL defaults
- Creation of new table with proper constraints
- Indexes for performance optimization
- Update of existing discounts with default values
- Foreign key relationships with proper cascade rules

#### Backward Compatibility:
- Existing discounts are marked as 'manual' source
- No breaking changes to existing functionality
- Graceful handling of NULL values in frontend

### 7. Business Logic Integration

#### PDD Integration:
- Automatic linking of approved PDD requests to discount tracking
- Calculation of total return quantities across multiple PDD requests
- Item-level breakdown showing original vs returned quantities
- Support for partial returns and multiple PDD requests per order

#### Audit Trail:
- Track who applied discounts and when
- Support for discount reversal and expiration
- Complete history of discount modifications
- Integration with user management system

### 8. Performance Considerations

#### Database Optimization:
- Indexed foreign key relationships
- Efficient queries for PDD data retrieval
- Minimal impact on existing sales order queries
- Optional loading of detailed discount information

#### Frontend Optimization:
- Conditional rendering of PDD information
- Efficient data structure for return details
- Minimal additional API calls

### 9. Future Enhancements Ready

#### Extensible Design:
- Support for additional discount types
- Ready for item-level discount tracking
- Prepared for discount approval workflows
- Foundation for discount analytics and reporting

#### Integration Points:
- Ready for integration with promotional systems
- Support for customer-specific discount rules
- Foundation for automated discount application
- Prepared for discount limit enforcement

## Usage Examples

### Viewing Discount Information:
When viewing a sales order, users will see:
- **Manual Discount**: "Discount (Manual): -₹500.00"
- **PDD Discount**: "Discount (PDD): -₹300.00" with "5 items returned" below
- **Promotional Discount**: "Discount (Promotional): -₹200.00"

### PDD Return Details:
For PDD discounts, additional information includes:
- Total return quantity across all PDD requests
- Breakdown by product and SKU
- Original quantities vs returned quantities
- PDD percentages and amounts per item

## Technical Implementation Notes

### Model Relationships:
- SalesOrder → SalesOrderDiscount (One-to-Many)
- SalesOrderDiscount → SalesOrderItem (Many-to-One, optional)
- SalesOrderDiscount → GRNReturnPDDRequest (Many-to-One, optional)
- SalesOrderDiscount → User (Many-to-One for applied_by)

### API Response Structure:
```json
{
  "id": "order-id",
  "discount_amount": 500.00,
  "discount_source": "pdd",
  "discount_reason": "Damaged goods return",
  "pdd_return_quantity": 5,
  "pdd_return_details": [
    {
      "request_id": "pdd-request-id",
      "requested_at": "2025-01-08T10:00:00Z",
      "approved_at": "2025-01-08T11:00:00Z",
      "items": [
        {
          "product_name": "Product A",
          "sku_code": "SKU001",
          "unit_type": "box",
          "original_quantity": 10,
          "return_quantity": 3,
          "pdd_percentage": 15.0,
          "pdd_amount": 150.00
        }
      ]
    }
  ]
}
```

This implementation provides complete visibility into discount sources and PDD return quantities, fulfilling all requirements while maintaining system performance and extensibility.
