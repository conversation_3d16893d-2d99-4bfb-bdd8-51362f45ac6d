// Test script to verify vehicle arrival inventory update fix
// This script simulates the exact issue: marking a vehicle arrival as completed directly

const axios = require('axios');

const BASE_URL = 'http://localhost:9000';
const ORG_ID = 'your-organization-id'; // Replace with actual organization ID
const AUTH_TOKEN = 'your-auth-token'; // Replace with actual auth token

// Test configuration
const testConfig = {
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    'x-organization-id': ORG_ID
  }
};

async function testVehicleArrivalInventoryUpdate() {
  try {
    console.log('🧪 Testing Vehicle Arrival Inventory Update Fix...\n');

    // Step 1: Get all vehicle arrivals to find one to test with
    console.log('1. Fetching vehicle arrivals...');
    const vehicleArrivalsResponse = await axios.get(`${BASE_URL}/api/procurement/vehicle-arrivals`, testConfig);
    const vehicleArrivals = vehicleArrivalsResponse.data;
    
    if (vehicleArrivals.length === 0) {
      console.log('❌ No vehicle arrivals found to test with');
      return;
    }

    // Find a vehicle arrival that is not completed
    const testVehicleArrival = vehicleArrivals.find(va => va.status !== 'completed');
    
    if (!testVehicleArrival) {
      console.log('❌ No non-completed vehicle arrivals found to test with');
      return;
    }

    console.log(`✅ Found test vehicle arrival: ${testVehicleArrival.id}`);
    console.log(`   Current status: ${testVehicleArrival.status}`);
    console.log(`   Items count: ${testVehicleArrival.items?.length || 0}`);

    // Step 2: Get current inventory before update
    console.log('\n2. Getting current inventory state...');
    const inventoryBeforeResponse = await axios.get(`${BASE_URL}/api/inventory`, testConfig);
    const inventoryBefore = inventoryBeforeResponse.data;
    console.log(`✅ Current inventory has ${inventoryBefore.length} items`);

    // Step 3: Mark vehicle arrival as completed using PUT endpoint (the one that was broken)
    console.log('\n3. Marking vehicle arrival as completed using PUT endpoint...');
    const updateResponse = await axios.put(
      `${BASE_URL}/api/procurement/vehicle-arrivals/${testVehicleArrival.id}`,
      {
        status: 'completed',
        notes: 'Test update - marking as completed to verify inventory update'
      },
      testConfig
    );

    console.log(`✅ Vehicle arrival updated successfully`);
    console.log(`   New status: ${updateResponse.data.status}`);

    // Step 4: Get inventory after update to verify items were added
    console.log('\n4. Checking inventory after update...');
    const inventoryAfterResponse = await axios.get(`${BASE_URL}/api/inventory`, testConfig);
    const inventoryAfter = inventoryAfterResponse.data;
    console.log(`✅ Inventory after update has ${inventoryAfter.length} items`);

    // Step 5: Analyze the changes
    console.log('\n5. Analyzing inventory changes...');
    
    if (inventoryAfter.length > inventoryBefore.length) {
      console.log('✅ SUCCESS: New inventory items were created!');
      const newItems = inventoryAfter.length - inventoryBefore.length;
      console.log(`   ${newItems} new inventory items added`);
    } else {
      // Check if existing items were updated
      let updatedItems = 0;
      for (const afterItem of inventoryAfter) {
        const beforeItem = inventoryBefore.find(item => 
          item.product_id === afterItem.product_id && 
          item.sku_id === afterItem.sku_id
        );
        
        if (beforeItem && beforeItem.available_quantity !== afterItem.available_quantity) {
          updatedItems++;
          console.log(`✅ Updated: ${afterItem.product_name} - ${afterItem.sku_code}`);
          console.log(`   Quantity: ${beforeItem.available_quantity} → ${afterItem.available_quantity}`);
        }
      }
      
      if (updatedItems > 0) {
        console.log(`✅ SUCCESS: ${updatedItems} existing inventory items were updated!`);
      } else {
        console.log('❌ ISSUE: No inventory changes detected');
      }
    }

    // Step 6: Show vehicle arrival items for reference
    console.log('\n6. Vehicle arrival items that should have been added to inventory:');
    if (testVehicleArrival.items && testVehicleArrival.items.length > 0) {
      testVehicleArrival.items.forEach((item, index) => {
        console.log(`   Item ${index + 1}: ${item.product?.name} - ${item.sku?.code}`);
        console.log(`     Quantity: ${item.final_quantity || item.quantity}`);
        console.log(`     Weight: ${item.final_total_weight || item.total_weight}`);
      });
    }

    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n💡 Tip: Make sure to update the AUTH_TOKEN and ORG_ID in this script');
    }
  }
}

// Instructions for running the test
console.log(`
📋 INSTRUCTIONS:
1. Update the AUTH_TOKEN and ORG_ID variables at the top of this script
2. Make sure the backend is running on port 9000
3. Run this script with: node test_inventory_update.js

🔍 This test will:
- Find a non-completed vehicle arrival
- Mark it as completed using the PUT endpoint (the one that was broken)
- Check if inventory was properly updated
- Show before/after inventory comparison

⚠️  Note: This test will actually modify data in your database
`);

// Uncomment the line below to run the test
// testVehicleArrivalInventoryUpdate();
