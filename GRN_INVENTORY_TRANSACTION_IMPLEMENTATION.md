# GRN Inventory Transaction System Implementation

## Overview
This implementation replaces the direct inventory adjustments in the GRN (Goods Receipt Note) return process with a comprehensive inventory transaction system that provides proper audit trails and record-keeping.

## Problem Solved
Previously, when processing returns via GRN, the system directly adjusted inventory quantities using `CurrentInventory.increment()`. This approach lacked:
- Proper audit trails
- Transaction history
- Traceability of inventory movements
- Compliance with accounting standards

## Solution Architecture

### 1. New Database Table: `inventory_transactions`
**Location**: `FxDPartnerERPBackend/migrations/031_create_inventory_transactions_table.sql`

**Key Fields**:
- `transaction_type`: 'return_to_inventory', 'sale', 'purchase', 'adjustment', 'vehicle_arrival'
- `quantity_change`: Positive for additions, negative for subtractions
- `reference_type`: 'grn_return', 'sales_order', 'purchase_record', 'vehicle_arrival', 'manual_adjustment'
- `reference_id`: Links to the source document (GRN request, sales order, etc.)
- `performed_by`: User who performed the transaction
- `reason`: Description of why the transaction occurred
- `notes`: Additional details

### 2. New Model: `InventoryTransaction`
**Location**: `FxDPartnerERPBackend/src/models/InventoryTransaction.ts`

Sequelize model with proper relationships to:
- Organization
- Product
- SKU
- User (performer)

### 3. Inventory Transaction Service
**Location**: `FxDPartnerERPBackend/src/services/inventoryTransactionService.ts`

**Key Methods**:
- `createInventoryTransaction()`: Creates transaction and updates inventory
- `createGRNReturnTransaction()`: Specialized method for GRN returns
- `getInventoryTransactionHistory()`: Get transaction history for product/SKU
- `getTransactionsByReference()`: Get all transactions for a specific reference
- `createSaleTransaction()`, `createPurchaseTransaction()`, etc.: Methods for other transaction types

### 4. Updated GRN Controller
**Location**: `FxDPartnerERPBackend/src/controllers/grnController.ts`

**Changes Made**:
- **Return Creation**: Replaced `CurrentInventory.increment()` with `InventoryTransactionService.createGRNReturnTransaction()`
- **Return Rejection**: Replaced `CurrentInventory.decrement()` with `InventoryTransactionService.createAdjustmentTransaction()` (negative quantity)
- **New Endpoints**: Added endpoints to view inventory transactions

**New API Endpoints**:
- `GET /api/grn/requests/:grnRequestId/inventory-transactions` - Get inventory transactions for a GRN request
- `GET /api/grn/inventory-transactions/:productId/:skuId` - Get transaction history for a product/SKU

### 5. Updated Routes
**Location**: `FxDPartnerERPBackend/src/routes/grnRoutes.ts`

Added new routes for inventory transaction viewing with proper permissions.

## Key Benefits

### 1. Complete Audit Trail
Every inventory movement is now recorded with:
- Who performed the action
- When it was performed
- Why it was performed
- What document triggered it
- Detailed notes

### 2. Traceability
- Can trace any inventory change back to its source document
- Can see all inventory movements for a specific GRN request
- Can view complete transaction history for any product/SKU

### 3. Proper Return Handling
- Returns are recorded as "return_to_inventory" transactions
- Each return references the original GRN request
- Rejections create adjustment transactions to revert returns

### 4. Extensibility
The service can handle other transaction types:
- Sales (negative quantity)
- Purchases (positive quantity)
- Vehicle arrivals (positive quantity)
- Manual adjustments (positive or negative)

### 5. Data Integrity
- All inventory changes go through the transaction service
- Inventory updates and transaction creation happen in the same database transaction
- Consistent timestamp updates

## Usage Examples

### Creating a GRN Return
```typescript
await InventoryTransactionService.createGRNReturnTransaction(
  organizationId,
  productId,
  skuId,
  returnQuantity,
  grnRequestId,
  userId,
  "Return from sales order SO-123",
  transaction
);
```

### Viewing Transaction History
```typescript
const history = await InventoryTransactionService.getInventoryTransactionHistory(
  organizationId,
  productId,
  skuId,
  50, // limit
  0   // offset
);
```

### Getting GRN-specific Transactions
```typescript
const grnTransactions = await InventoryTransactionService.getTransactionsByReference(
  organizationId,
  'grn_return',
  grnRequestId
);
```

## Database Migration
To apply the changes, run the migration:
```sql
-- Execute: FxDPartnerERPBackend/migrations/031_create_inventory_transactions_table.sql
```

**Note**: The migration has been updated to use MySQL-compatible syntax:
- Uses `CHAR(36)` instead of `UUID` data type
- Uses `UUID()` function for default values
- Uses proper MySQL foreign key constraint syntax
- Uses `TIMESTAMP` with `ON UPDATE CURRENT_TIMESTAMP` instead of PostgreSQL-specific syntax

## API Endpoints

### Existing Endpoints (Updated)
- `POST /api/grn/return-pdd-requests` - Now creates inventory transactions
- `PATCH /api/grn/requests/:id/reject` - Now creates reversal transactions

### New Endpoints
- `GET /api/grn/requests/:grnRequestId/inventory-transactions` - View transactions for GRN request
- `GET /api/grn/inventory-transactions/:productId/:skuId` - View transaction history for product

## Future Enhancements

1. **User Integration**: Replace `undefined` with actual user IDs from authentication
2. **Weight Tracking**: Add weight changes to transactions
3. **Batch Operations**: Support for bulk transaction creation
4. **Reporting**: Build reports based on transaction data
5. **Integration**: Apply the same pattern to sales, purchases, and vehicle arrivals

## Backward Compatibility

- Existing GRN functionality remains unchanged from user perspective
- All existing API endpoints work the same way
- Only the internal implementation changed from direct inventory updates to transaction-based updates
- The `GRNReturnPDDRequest` and `GRNReturnPDDItem` tables remain the source of truth for business logic

## Testing

The system maintains the same external behavior while adding comprehensive tracking. Test cases should verify:
1. Returns still update inventory correctly
2. Rejections still revert inventory correctly
3. Transaction records are created properly
4. New API endpoints return correct data
5. Proper error handling and rollbacks
