const axios = require('axios');

const headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'x-organization-id': 'default-org-id',
  'Content-Type': 'application/json'
};

async function testGrapeOrangeMerge() {
  try {
    // First, check current inventory
    console.log('=== Current Inventory Before Merge ===');
    const inventoryBefore = await axios.get('http://localhost:9000/api/inventory', { headers });
    
    inventoryBefore.data.forEach(item => {
      console.log(`${item.product_name} - ${item.sku_code}: ${item.available_quantity} (Product ID: ${item.product_id})`);
    });
    
    // Find grape product details
    const grapeInventory = inventoryBefore.data.find(item => item.product_name === 'grape');
    const orangeInventory = inventoryBefore.data.find(item => item.product_name === 'Orange');
    
    if (!grapeInventory) {
      console.log('\nGrape product not found in inventory!');
      return;
    }
    
    console.log('\n=== Merge Details ===');
    console.log(`Grape inventory: ${grapeInventory.available_quantity} (SKU: ${grapeInventory.sku_code})`);
    console.log(`Orange inventory: ${orangeInventory ? orangeInventory.available_quantity : 'N/A'} (SKU: ${orangeInventory ? orangeInventory.sku_code : 'N/A'})`);
    console.log(`Expected result after merge: ${(orangeInventory ? orangeInventory.available_quantity : 0) + grapeInventory.available_quantity}`);
    
    // Perform the merge
    console.log('\n=== Performing Merge ===');
    const mergeData = {
      current_product_id: grapeInventory.product_id,
      current_sku_id: grapeInventory.sku_id,
      reason: "SKU code correction",
      new_product_name: "Orange",
      new_sku_code: "apple"
    };
    
    console.log('Merge request:', JSON.stringify(mergeData, null, 2));
    
    try {
      const mergeResponse = await axios.post('http://localhost:9000/api/inventory/update-sku', mergeData, { headers });
      
      console.log('\n✅ Merge successful!');
      console.log('Response:', mergeResponse.data.message);
      
      if (mergeResponse.data.merge_details) {
        console.log('\nMerge details:');
        console.log('- Inventory merged:', JSON.stringify(mergeResponse.data.merge_details.inventory_merged, null, 2));
      }
      
    } catch (error) {
      console.error('\n❌ Merge failed:', error.response?.data || error.message);
    }
    
    // Check inventory after merge
    console.log('\n=== Inventory After Merge ===');
    const inventoryAfter = await axios.get('http://localhost:9000/api/inventory', { headers });
    
    inventoryAfter.data.forEach(item => {
      console.log(`${item.product_name} - ${item.sku_code}: ${item.available_quantity}`);
    });
    
    // Verify the merge result
    const orangeAfterMerge = inventoryAfter.data.find(item => item.product_name === 'Orange' && item.sku_code === 'apple');
    if (orangeAfterMerge) {
      console.log(`\n=== Verification ===`);
      console.log(`Orange quantity after merge: ${orangeAfterMerge.available_quantity}`);
      console.log(`Expected: ${(orangeInventory ? orangeInventory.available_quantity : 0) + grapeInventory.available_quantity}`);
      
      if (orangeAfterMerge.available_quantity === ((orangeInventory ? orangeInventory.available_quantity : 0) + grapeInventory.available_quantity)) {
        console.log('✅ Merge quantities are correct!');
      } else {
        console.log('❌ Merge quantities are incorrect!');
      }
    }
    
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

testGrapeOrangeMerge();
