const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:9000';
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************.-QNjy7om2KXfGTpT8-ZdAQnQ9BZRFsn8Qe5LRPpUfiI';
const ORGANIZATION_ID = '82570ff7-be23-48f4-b826-771350961cff';

const headers = {
  'Authorization': `Bearer ${AUTH_TOKEN}`,
  'Content-Type': 'application/json',
  'x-organization-id': ORGANIZATION_ID
};

async function testCreateCompletedVehicleArrival() {
  console.log('🧪 Testing: Create Vehicle Arrival with Completed Status');
  console.log('=' .repeat(60));

  try {
    // Step 1: Get current inventory before creating vehicle arrival
    console.log('\n1️⃣ Getting current inventory before vehicle arrival...');
    const inventoryBefore = await axios.get(`${BASE_URL}/api/inventory`, { headers });
    console.log(`Current inventory items: ${inventoryBefore.data.length}`);
    
    // Find existing inventory for the test product/SKU
    const testProductId = 'fc32a8af-28eb-454f-9338-a4fa83b670f5';
    const testSkuId = '1377434a-a3ad-4be3-9410-5848157d159c';
    const existingInventory = inventoryBefore.data.find(item => 
      item.product_id === testProductId && item.sku_id === testSkuId
    );
    
    if (existingInventory) {
      console.log(`📦 Existing inventory for test product:`);
      console.log(`   - Product ID: ${existingInventory.product_id}`);
      console.log(`   - SKU ID: ${existingInventory.sku_id}`);
      console.log(`   - Current quantity: ${existingInventory.available_quantity}`);
      console.log(`   - Current weight: ${existingInventory.total_weight}`);
    } else {
      console.log(`📦 No existing inventory found for test product`);
    }

    // Step 2: Create vehicle arrival with completed status
    console.log('\n2️⃣ Creating vehicle arrival with completed status...');
    const vehicleArrivalData = {
      vehicle_number: null,
      supplier: "Vegrow",
      driver_name: null,
      driver_contact: null,
      arrival_time: "2025-07-23T17:55",
      status: "completed",
      notes: null,
      items: [
        {
          product_id: testProductId,
          sku_id: testSkuId,
          unit_type: "box",
          unit_weight: 1,
          quantity: 1,
          total_weight: 1,
          final_quantity: 1,
          final_total_weight: 1
        }
      ],
      attachments: []
    };

    const createResponse = await axios.post(
      `${BASE_URL}/api/procurement/vehicle-arrivals`,
      vehicleArrivalData,
      { headers }
    );

    console.log(`✅ Vehicle arrival created successfully!`);
    console.log(`   - ID: ${createResponse.data.id}`);
    console.log(`   - Status: ${createResponse.data.status}`);
    console.log(`   - Items count: ${createResponse.data.items?.length || 0}`);

    // Step 3: Get inventory after creating vehicle arrival
    console.log('\n3️⃣ Getting inventory after vehicle arrival creation...');
    const inventoryAfter = await axios.get(`${BASE_URL}/api/inventory`, { headers });
    console.log(`Inventory items after: ${inventoryAfter.data.length}`);
    
    const updatedInventory = inventoryAfter.data.find(item => 
      item.product_id === testProductId && item.sku_id === testSkuId
    );
    
    if (updatedInventory) {
      console.log(`📦 Updated inventory for test product:`);
      console.log(`   - Product ID: ${updatedInventory.product_id}`);
      console.log(`   - SKU ID: ${updatedInventory.sku_id}`);
      console.log(`   - New quantity: ${updatedInventory.available_quantity}`);
      console.log(`   - New weight: ${updatedInventory.total_weight}`);
      console.log(`   - Last updated: ${updatedInventory.last_updated_at}`);
    } else {
      console.log(`❌ No inventory found for test product after creation`);
    }

    // Step 4: Verify inventory changes
    console.log('\n4️⃣ Verifying inventory changes...');
    const expectedQuantityIncrease = 1; // from final_quantity in the request
    const expectedWeightIncrease = 1; // from final_total_weight in the request
    
    if (existingInventory && updatedInventory) {
      const quantityIncrease = updatedInventory.available_quantity - existingInventory.available_quantity;
      const weightIncrease = updatedInventory.total_weight - existingInventory.total_weight;
      
      console.log(`📊 Inventory Changes:`);
      console.log(`   - Quantity increase: ${quantityIncrease} (expected: ${expectedQuantityIncrease})`);
      console.log(`   - Weight increase: ${weightIncrease} (expected: ${expectedWeightIncrease})`);
      
      if (quantityIncrease === expectedQuantityIncrease && weightIncrease === expectedWeightIncrease) {
        console.log(`✅ INVENTORY UPDATE SUCCESSFUL! Changes match expected values.`);
      } else {
        console.log(`❌ INVENTORY UPDATE FAILED! Changes don't match expected values.`);
      }
    } else if (!existingInventory && updatedInventory) {
      console.log(`📊 New Inventory Record Created:`);
      console.log(`   - Quantity: ${updatedInventory.available_quantity} (expected: ${expectedQuantityIncrease})`);
      console.log(`   - Weight: ${updatedInventory.total_weight} (expected: ${expectedWeightIncrease})`);
      
      if (updatedInventory.available_quantity === expectedQuantityIncrease && 
          updatedInventory.total_weight === expectedWeightIncrease) {
        console.log(`✅ NEW INVENTORY RECORD SUCCESSFUL! Values match expected.`);
      } else {
        console.log(`❌ NEW INVENTORY RECORD FAILED! Values don't match expected.`);
      }
    } else {
      console.log(`❌ INVENTORY UPDATE FAILED! No inventory record found after creation.`);
    }

    console.log('\n🎉 Test completed!');
    console.log('=' .repeat(60));

  } catch (error) {
    console.error('\n❌ Test failed with error:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data:`, error.response.data);
    } else {
      console.error(error.message);
    }
  }
}

// Run the test
testCreateCompletedVehicleArrival();
