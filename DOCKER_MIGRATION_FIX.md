# Docker Migration Fix Documentation

## Problem Summary

The issue was that migrations were not running properly in the Docker environment, causing tables like `negative_inventory` to not be created and the `migrations` table to not be updated. This worked locally but failed in remote/Docker deployments.

## Root Causes Identified

1. **Database Timing Issues**: Backend container started before MySQL was fully ready
2. **Conflicting Migration Approaches**: Both MySQL init directory and custom migration system
3. **Environment Variable Mismatches**: Different credentials between local and Docker
4. **Silent Migration Failures**: Poor error handling and logging
5. **Missing Page Seeding**: Pages table remained empty after migrations

## Solutions Implemented

### 1. Database Wait Script (`scripts/wait-for-db.sh`)
- Waits for MySQL to be fully ready before proceeding
- Tests actual database connectivity, not just container startup
- 5-minute timeout with proper error handling

### 2. Enhanced Migration System (`src/utils/migration.ts`)
- Added comprehensive logging for each migration step
- Better error handling with detailed error messages
- Statement-by-statement execution tracking
- Clear indication of which migrations are being executed

### 3. Comprehensive Setup Script (`scripts/setup-database.sh`)
- Combines database waiting, migration execution, and page seeding
- Checks if pages exist before attempting to seed
- Graceful error handling for each step

### 4. Docker Configuration Fixes

#### Updated Dockerfile:
- Added MySQL client for connectivity checks
- Made all scripts executable
- Uses comprehensive setup script instead of individual commands

#### Updated docker-compose.yml:
- Added database health checks
- Removed conflicting migration volume mount
- Uses dedicated Docker environment file

#### New Docker Environment File (`.env.docker`):
- Correct database credentials matching docker-compose
- Proper host configuration for Docker networking

## Files Modified

1. **FxDPartnerERPBackend/scripts/wait-for-db.sh** - New database wait script
2. **FxDPartnerERPBackend/scripts/setup-database.sh** - New comprehensive setup script
3. **FxDPartnerERPBackend/src/utils/migration.ts** - Enhanced with detailed logging
4. **FxDPartnerERPBackend/Dockerfile** - Updated to use new scripts
5. **FxDPartnerERPBackend/package.json** - Added seed-pages and ts-node scripts
6. **FxDPartnerERPBackend/.env.docker** - New Docker-specific environment file
7. **docker-compose.yml** - Fixed health checks and environment configuration

## How to Deploy

### 1. Clean Deployment (Recommended)
```bash
# Stop and remove existing containers
docker-compose down -v

# Remove old images
docker-compose build --no-cache

# Start fresh
docker-compose up -d
```

### 2. Check Logs
```bash
# Monitor backend startup
docker-compose logs -f backend

# Check database logs
docker-compose logs -f database
```

### 3. Verify Tables
```bash
# Connect to database container
docker exec -it fxd-erp-database mysql -u fxd_user -pfxd_password fxd_erp

# Check if tables exist
SHOW TABLES;

# Check migrations table
SELECT * FROM migrations ORDER BY executed_at;

# Check pages table
SELECT COUNT(*) FROM pages;
```

## Expected Behavior

When you run `docker-compose up`, you should see:

1. **Database startup** with health checks
2. **Backend waiting** for database to be ready
3. **Migration execution** with detailed logging for each step
4. **Page seeding** if pages table is empty
5. **Application startup** after successful setup

## Troubleshooting

### If migrations still fail:
1. Check database connectivity: `docker-compose logs database`
2. Verify environment variables match between `.env.docker` and `docker-compose.yml`
3. Check migration logs: `docker-compose logs backend`

### If pages are still empty:
1. Manually run page seeding: `docker exec -it fxd-erp-backend npm run seed-pages`
2. Check if migration 016 executed: Look for "016_create_pages_table" in migrations table

### If negative_inventory table is missing:
1. Check if migration 003 executed successfully
2. Look for specific error messages in migration logs
3. Manually verify table creation: `DESCRIBE negative_inventory;`

## Key Improvements

1. **Reliability**: Proper database waiting ensures migrations run when DB is ready
2. **Visibility**: Detailed logging shows exactly what's happening during setup
3. **Completeness**: Automatic page seeding ensures UI works immediately
4. **Maintainability**: Separate scripts make debugging and updates easier
5. **Consistency**: Docker-specific configuration prevents environment mismatches

## Testing the Fix

After deployment, test these scenarios:

1. **Page Permissions UI**: Should show all available pages
2. **Negative Inventory**: Should be able to create negative inventory records
3. **Migration Status**: `npm run migrate status` should show all migrations executed
4. **Database Tables**: All expected tables should exist with proper structure

This fix addresses the core issues causing migration failures in Docker environments while maintaining compatibility with local development.
