const { Sequelize } = require('sequelize');
const sequelize = new Sequelize('fxd_partner_erp_backend', 'root', 'rootroot', {
  host: 'localhost',
  dialect: 'mysql',
  logging: false
});

async function checkDates() {
  try {
    // Get date range of customers
    const [customerDates] = await sequelize.query(
      `SELECT 
        MIN(created_at) as oldest_customer,
        MAX(created_at) as newest_customer,
        COUNT(*) as total_customers
       FROM customers 
       WHERE organization_id = 'default-org-id'`
    );
    
    // Get date range of suppliers
    const [supplierDates] = await sequelize.query(
      `SELECT 
        MIN(created_at) as oldest_supplier,
        MAX(created_at) as newest_supplier,
        COUNT(*) as total_suppliers
       FROM suppliers 
       WHERE organization_id = 'default-org-id'`
    );
    
    console.log('Customer Data:');
    console.log('- Total:', customerDates[0].total_customers);
    console.log('- Oldest:', customerDates[0].oldest_customer);
    console.log('- Newest:', customerDates[0].newest_customer);
    console.log('\nSupplier Data:');
    console.log('- Total:', supplierDates[0].total_suppliers);
    console.log('- Oldest:', supplierDates[0].oldest_supplier);
    console.log('- Newest:', supplierDates[0].newest_supplier);
    
    // Get a sample of actual dates
    const [sampleCustomers] = await sequelize.query(
      `SELECT name, created_at 
       FROM customers 
       WHERE organization_id = 'default-org-id' 
       ORDER BY created_at DESC 
       LIMIT 3`
    );
    
    console.log('\nSample Customer Creation Dates:');
    sampleCustomers.forEach(c => {
      console.log(`- ${c.name}: ${c.created_at}`);
    });
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await sequelize.close();
  }
}

checkDates();
