const axios = require('axios');

const headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'x-organization-id': 'default-org-id',
  'Content-Type': 'application/json'
};

async function testFixedMerge() {
  try {
    console.log('=== Testing Fixed SKU Merge with Both Product and SKU Update ===\n');
    
    // Get current inventory
    const inventoryResponse = await axios.get('http://localhost:9000/api/inventory', { headers });
    
    console.log('Current inventory:');
    inventoryResponse.data.forEach(item => {
      console.log(`- ${item.product_name} (${item.sku_code}): ${item.available_quantity}`);
    });
    
    // Get Orange inventory with SKU "hg"
    const orangeInventory = inventoryResponse.data.find(item => item.product_name === 'Orange' && item.sku_code === 'hg');
    const orangeQuantityBefore = orangeInventory ? orangeInventory.available_quantity : 0;
    
    // Create a test product
    console.log('\nCreating test product...');
    
    const productData = {
      name: 'TestProduct',
      description: 'Test product for merge',
      status: 'active'
    };
    
    const productResponse = await axios.post('http://localhost:9000/api/products', productData, { headers });
    const testProduct = productResponse.data;
    console.log('Created product:', testProduct.name);
    
    // Create SKU
    const skuData = {
      product_id: testProduct.id,
      code: 'test-sku',
      unit_type: 'box',
      unit_weight: 1,
      status: 'active'
    };
    
    const skuResponse = await axios.post('http://localhost:9000/api/products/skus', skuData, { headers });
    const testSku = skuResponse.data;
    console.log('Created SKU:', testSku.code);
    
    // Create inventory with quantity 500
    const inventoryData = {
      product_id: testProduct.id,
      sku_id: testSku.id,
      product_name: testProduct.name,
      sku_code: testSku.code,
      unit_type: testSku.unit_type,
      available_quantity: 500,
      total_weight: 500
    };
    
    await axios.post('http://localhost:9000/api/inventory', inventoryData, { headers });
    console.log('Created inventory with 500 quantity');
    
    console.log(`\nOrange (hg) quantity before merge: ${orangeQuantityBefore}`);
    console.log(`TestProduct (test-sku) quantity: 500`);
    console.log(`Expected Orange (hg) after merge: ${orangeQuantityBefore + 500}`);
    
    // Now perform the merge with BOTH product name and SKU code change
    console.log('\nPerforming merge (changing both product name to "Orange" and SKU code to "hg")...');
    const mergeData = {
      current_product_id: testProduct.id,
      current_sku_id: testSku.id,
      reason: "Testing fixed merge logic",
      new_product_name: "Orange",
      new_sku_code: "hg"
    };
    
    try {
      const mergeResponse = await axios.post('http://localhost:9000/api/inventory/update-sku', mergeData, { headers });
      console.log('✅ Merge successful!');
      console.log('Response:', mergeResponse.data.message);
      
      // Check final inventory
      const finalInventoryResponse = await axios.get('http://localhost:9000/api/inventory', { headers });
      const orangeAfter = finalInventoryResponse.data.find(item => item.product_name === 'Orange' && item.sku_code === 'hg');
      
      console.log(`\n=== Final Result ===`);
      console.log(`Orange (hg) quantity after merge: ${orangeAfter?.available_quantity}`);
      console.log(`Expected: ${orangeQuantityBefore + 500}`);
      
      if (orangeAfter && orangeAfter.available_quantity === (orangeQuantityBefore + 500)) {
        console.log('✅ SUCCESS: Merge worked correctly! Quantities were properly added.');
      } else {
        console.log('❌ FAIL: Merge quantities do not match!');
      }
      
      // Show merge details if available
      if (mergeResponse.data.merge_details) {
        console.log('\nMerge details:');
        console.log('Inventory merged:', JSON.stringify(mergeResponse.data.merge_details.inventory_merged, null, 2));
      }
      
    } catch (error) {
      console.error('❌ Merge failed:', error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

// Run the test
testFixedMerge();
