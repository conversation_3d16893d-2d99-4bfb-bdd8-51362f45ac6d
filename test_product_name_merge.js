const axios = require('axios');

// Test data - testing product merge via product name change
const testData = {
  current_product_id: "c5490aa4-6c25-412e-9a73-744a42fc9025", // Apple product
  current_sku_id: "3f329264-b291-4e22-9982-83ab9ce08e83", // ab SKU
  reason: "Unit type change",
  new_product_name: "Pomo" // This will trigger a merge with existing Pomo product
};

const headers = {
  'Accept': '*/*',
  'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'Cache-Control': 'no-cache',
  'Connection': 'keep-alive',
  'Content-Type': 'application/json',
  'Origin': 'http://localhost:5173',
  'Pragma': 'no-cache',
  'Referer': 'http://localhost:5173/',
  'Sec-Fetch-Dest': 'empty',
  'Sec-Fetch-Mode': 'cors',
  'Sec-Fetch-Site': 'same-site',
  'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"macOS"',
  'x-organization-id': 'default-org-id'
};

async function testProductNameMerge() {
  try {
    console.log('Testing product merge via product name change...');
    console.log('This will merge "Apple" product into "Pomo" product\n');
    console.log('Request data:', JSON.stringify(testData, null, 2));
    
    const response = await axios.post(
      'http://localhost:9000/api/inventory/update-sku',
      testData,
      { headers }
    );
    
    console.log('\nResponse status:', response.status);
    console.log('\n✅ Product merge successful!');
    console.log('\nMerge message:', response.data.message);
    
    if (response.data.merge_details) {
      console.log('\nMerge details:');
      console.log('- Source SKUs:', response.data.merge_details.source_skus);
      console.log('- Target SKUs:', response.data.merge_details.target_skus);
      console.log('- Inventory merged:', JSON.stringify(response.data.merge_details.inventory_merged, null, 2));
      console.log('- Sales orders updated:', response.data.merge_details.sales_orders_updated);
      console.log('- Purchase records updated:', response.data.merge_details.purchase_records_updated);
      console.log('- Vehicle arrivals updated:', response.data.merge_details.vehicle_arrivals_updated);
      console.log('- Inventory transactions updated:', response.data.merge_details.inventory_transactions_updated);
    }
    
    console.log('\nUpdated inventory for target product:');
    response.data.updated_inventory.forEach(item => {
      console.log(`- SKU: ${item.sku_code}, Quantity: ${item.available_quantity}`);
    });
    
  } catch (error) {
    console.error('\n❌ Error:', error.response ? error.response.data : error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
    }
  }
}

// Check inventory before merge
async function checkInventoryBefore() {
  try {
    console.log('=== Checking inventory before merge ===\n');
    
    const response = await axios.get(
      'http://localhost:9000/api/inventory',
      { headers }
    );
    
    const appleInventory = response.data.filter(item => item.product_name === 'Apple');
    const pomoInventory = response.data.filter(item => item.product_name === 'Pomo');
    
    console.log('Apple product inventory:');
    appleInventory.forEach(item => {
      console.log(`- SKU: ${item.sku_code}, Quantity: ${item.available_quantity}`);
    });
    
    console.log('\nPomo product inventory:');
    pomoInventory.forEach(item => {
      console.log(`- SKU: ${item.sku_code}, Quantity: ${item.available_quantity}`);
    });
    
    // Check for SKU conflicts
    console.log('\nChecking for potential SKU conflicts:');
    const appleSkus = appleInventory.map(item => item.sku_code);
    const pomoSkus = pomoInventory.map(item => item.sku_code);
    
    const conflicts = appleSkus.filter(sku => pomoSkus.includes(sku));
    if (conflicts.length > 0) {
      console.log(`- Found conflicting SKUs: ${conflicts.join(', ')}`);
      console.log('  These SKUs will be merged or skipped during the product merge');
    } else {
      console.log('- No SKU conflicts found');
    }
    
    return response.data;
  } catch (error) {
    console.error('Error fetching inventory:', error.response ? error.response.data : error.message);
    return [];
  }
}

// Main execution
async function main() {
  console.log('=== Product Name Change Merge Test ===\n');
  
  // Check inventory before merge
  await checkInventoryBefore();
  
  // Wait a moment before testing
  console.log('\n\nPress Ctrl+C to cancel, or wait 3 seconds to proceed with the merge test...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  console.log('\n=== Performing product merge via name change ===\n');
  
  // Run the test
  await testProductNameMerge();
}

main();
