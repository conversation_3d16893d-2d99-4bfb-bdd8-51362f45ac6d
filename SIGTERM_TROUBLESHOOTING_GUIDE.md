# SIGTERM Error Troubleshooting Guide 🔧

## Current Status
- ✅ bcrypt → bcryptjs migration completed
- ✅ Database schema issues fixed (InventoryTransaction model)
- ✅ TypeScript build successful
- ❌ Still experiencing SIGTERM errors

## Potential Root Causes & Solutions

### 1. Environment Variables Missing 🔧
**Issue:** Missing or incorrect environment variables can cause crashes during startup.

**Check your `.env` file in the backend:**
```bash
cd FxDPartnerERPBackend
cat .env
```

**Required variables:**
```env
# Database
DB_HOST=database
DB_PORT=3306
DB_NAME=fxd_partner_erp
DB_USER=root
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your_jwt_secret_here

# Node Environment
NODE_ENV=production

# CORS
CORS_ORIGIN=http://localhost:3000
```

### 2. Database Connection Issues 🔧
**Issue:** If the database isn't ready when the app starts, it might crash.

**Solution:** Check docker-compose.yml for proper service dependencies:
```yaml
backend:
  depends_on:
    - database
  environment:
    - DB_HOST=database
```

### 3. Memory Issues 🔧
**Issue:** Node.js might be running out of memory in the container.

**Solution:** Add memory limits to docker-compose.yml:
```yaml
backend:
  mem_limit: 1g
  environment:
    - NODE_OPTIONS=--max-old-space-size=1024
```

### 4. Port Conflicts 🔧
**Issue:** Port 3001 might already be in use.

**Check for port conflicts:**
```bash
# Check if port is in use
lsof -i :3001
netstat -an | grep 3001
```

**Solution:** Change port in docker-compose.yml:
```yaml
backend:
  ports:
    - "3002:3001"  # Use different external port
```

### 5. Docker Build Cache Issues 🔧
**Issue:** Old cached layers might contain the problematic bcrypt.

**Solution:** Force rebuild without cache:
```bash
docker-compose build --no-cache backend
docker-compose down --volumes
docker-compose up --build
```

### 6. Node.js Version Compatibility 🔧
**Issue:** Node.js version in Docker might be incompatible.

**Check your Dockerfile:**
```dockerfile
FROM node:18-alpine  # Try different versions: 16, 18, 20
```

### 7. Missing Dependencies 🔧
**Issue:** Some dependencies might not be properly installed in Docker.

**Solution:** Clear node_modules and reinstall:
```bash
# In your local backend directory
rm -rf node_modules package-lock.json
npm install

# Then rebuild Docker
docker-compose build --no-cache backend
```

### 8. Database Schema Conflicts 🔧
**Issue:** Existing database tables might have incompatible schemas.

**Solution:** Reset database:
```bash
docker-compose down --volumes
docker-compose up database  # Start only database first
# Wait for database to be ready, then:
docker-compose up backend
```

## Debugging Steps

### Step 1: Check Docker Logs
```bash
docker-compose logs backend
docker-compose logs -f backend  # Follow logs
```

### Step 2: Test Local Build
```bash
cd FxDPartnerERPBackend
npm run build
npm start
```

### Step 3: Check Database Connectivity
```bash
# Test database connection
docker-compose exec database mysql -u root -p -e "SHOW DATABASES;"
```

### Step 4: Inspect Container
```bash
# Get into the container to debug
docker-compose run --rm backend sh
# Inside container:
node dist/index.js
```

### Step 5: Check Resource Usage
```bash
docker stats
```

## Quick Fix Attempts

### Fix 1: Minimal Docker Restart
```bash
docker-compose down
docker-compose up --build
```

### Fix 2: Complete Reset
```bash
docker-compose down --volumes --remove-orphans
docker system prune -f
docker-compose build --no-cache
docker-compose up
```

### Fix 3: Database First Approach
```bash
docker-compose down
docker-compose up database
# Wait 30 seconds for database to be ready
docker-compose up backend
```

### Fix 4: Environment Variable Check
```bash
# Check if all required env vars are set
docker-compose config
```

## Expected Success Output
When everything works, you should see:
```
🔄 Starting server initialization...
🔄 Testing database connections...
✅ Database connections established
🔄 Synchronizing Sequelize models...
✅ Sequelize models synchronized
🔄 Starting HTTP server...
🚀 Server is running on port 3001
📊 Environment: production
🌐 CORS Origin: http://localhost:3000
💾 Database: database:3306
🔗 ORM: Sequelize with TypeScript models
✅ Server startup completed successfully
```

## If All Else Fails

### Last Resort: Simplified Dockerfile
Create a new Dockerfile with minimal dependencies:
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy built application
COPY dist/ ./dist/

# Expose port
EXPOSE 3001

# Start application
CMD ["node", "dist/index.js"]
```

### Alternative: Use Ubuntu Base Image
```dockerfile
FROM node:18-bullseye  # Instead of Alpine
```

## Next Steps
1. Try the debugging steps above
2. Share the complete Docker logs
3. Check environment variables
4. Try the quick fixes in order

The bcrypt issue is definitely fixed, so the SIGTERM is likely caused by one of these other factors.
