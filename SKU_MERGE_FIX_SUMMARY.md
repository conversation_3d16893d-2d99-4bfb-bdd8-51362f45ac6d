# SKU Merge Fix Summary

## Issue
When updating both product name and SKU code simultaneously, the merging was happening but the quantity was not being updated correctly. The inventory items were being merged into the target product, but the quantities were not being added together.

## Root Cause
The issue was in the `updateSKUDetails` function in `inventoryController.ts`. When both `new_product_name` and `new_sku_code` were provided, the code was checking for SKU conflicts first before checking if we're trying to merge into a specific product. This caused the logic to follow the wrong path.

## Solution
The fix involved restructuring the logic to:

1. **First check if both product name and SKU code are being changed together**
   - If yes, look for the target product by name
   - If the target product exists and is different from the current product, perform a merge

2. **Properly handle the SKU code change during merge**
   - When merging inventory items, check if the current item being processed is the one whose SKU code is being changed
   - Use the new SKU code for that specific item when looking for or creating inventory in the target product

3. **Ensure quantities are properly added**
   - Use `parseFloat()` to handle decimal weight values correctly
   - Properly add quantities when merging into existing inventory

## Key Code Changes

### Before:
```typescript
// Check if new SKU code already exists (if provided)
if (new_sku_code && new_sku_code !== currentSku.code) {
  // ... SKU conflict checking logic
}

// Check if product name change triggers a merge
if (new_product_name && new_product_name !== currentProduct.name) {
  // ... product merge logic
}
```

### After:
```typescript
// Check if we're changing both product name and SKU code
if (new_product_name && new_sku_code && 
    (new_product_name !== currentProduct.name || new_sku_code !== currentSku.code)) {
  
  // Look for the target product by name
  const targetProduct = await Product.findOne({
    where: {
      name: new_product_name,
      organization_id: organizationId
    },
    transaction
  });

  if (targetProduct && targetProduct.id !== current_product_id) {
    // Perform merge with proper SKU code handling
    // ...
  }
}
```

## Test Results

### Test 1: Positive Quantity Merge
- Source: TestProduct with 250 quantity
- Target: Orange (hg) with -2406 quantity
- Result: Orange (hg) = -2406 + 250 = -2156 ✅

### Test 2: Negative Quantity Merge
- Source: TestProduct with -100 quantity
- Target: Orange (hg) with -2156 quantity
- Result: Orange (hg) = -2156 + (-100) = -2256 ✅

## Impact
This fix ensures that when users update both the product name and SKU code:
1. The system correctly identifies this as a merge operation
2. Quantities are properly added together
3. All related records (sales orders, purchase records, etc.) are updated
4. A merge history record is created for audit purposes

## Files Modified
- `FxDPartnerERPBackend/src/controllers/inventoryController.ts` - Updated the `updateSKUDetails` function
