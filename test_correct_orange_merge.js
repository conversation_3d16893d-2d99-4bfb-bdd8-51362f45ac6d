const axios = require('axios');

const headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'x-organization-id': 'default-org-id',
  'Content-Type': 'application/json'
};

async function testCorrectMerge() {
  try {
    console.log('=== Testing Correct Orange Merge ===\n');
    
    // Get current inventory
    const inventoryResponse = await axios.get('http://localhost:9000/api/inventory', { headers });
    
    console.log('Current inventory:');
    inventoryResponse.data.forEach(item => {
      console.log(`- ${item.product_name} (${item.sku_code}): ${item.available_quantity}`);
    });
    
    // Get Orange inventory with SKU "hg"
    const orangeInventory = inventoryResponse.data.find(item => item.product_name === 'Orange' && item.sku_code === 'hg');
    const orangeQuantityBefore = orangeInventory ? orangeInventory.available_quantity : 0;
    
    // Create a new grape product
    console.log('\nCreating new grape product...');
    
    const productData = {
      name: 'NewGrape',
      description: 'New Grape product for testing',
      status: 'active'
    };
    
    const productResponse = await axios.post('http://localhost:9000/api/products', productData, { headers });
    const grapeProduct = productResponse.data;
    console.log('Created product:', grapeProduct.name);
    
    // Create SKU
    const skuData = {
      product_id: grapeProduct.id,
      code: 'new-gi',
      unit_type: 'box',
      unit_weight: 1,
      status: 'active'
    };
    
    const skuResponse = await axios.post('http://localhost:9000/api/products/skus', skuData, { headers });
    const grapeSku = skuResponse.data;
    console.log('Created SKU:', grapeSku.code);
    
    // Create inventory with -1000
    const inventoryData = {
      product_id: grapeProduct.id,
      sku_id: grapeSku.id,
      product_name: grapeProduct.name,
      sku_code: grapeSku.code,
      unit_type: grapeSku.unit_type,
      available_quantity: -1000,
      total_weight: 0
    };
    
    await axios.post('http://localhost:9000/api/inventory', inventoryData, { headers });
    console.log('Created inventory with -1000 quantity');
    
    console.log(`\nOrange quantity before merge: ${orangeQuantityBefore}`);
    console.log(`NewGrape quantity: -1000`);
    console.log(`Expected after merge: ${orangeQuantityBefore + (-1000)}`);
    
    // Now perform the merge with the CORRECT SKU code "hg"
    console.log('\nPerforming merge (changing both product name to "Orange" and SKU code to "hg")...');
    const mergeData = {
      current_product_id: grapeProduct.id,
      current_sku_id: grapeSku.id,
      reason: "Correct SKU merge test",
      new_product_name: "Orange",
      new_sku_code: "hg"  // Using the correct SKU code!
    };
    
    try {
      const mergeResponse = await axios.post('http://localhost:9000/api/inventory/update-sku', mergeData, { headers });
      console.log('✅ Merge successful!');
      console.log('Response:', mergeResponse.data.message);
      
      // Check final inventory
      const finalInventoryResponse = await axios.get('http://localhost:9000/api/inventory', { headers });
      const orangeAfter = finalInventoryResponse.data.find(item => item.product_name === 'Orange' && item.sku_code === 'hg');
      
      console.log(`\n=== Final Result ===`);
      console.log(`Orange (hg) quantity after merge: ${orangeAfter?.available_quantity}`);
      console.log(`Expected: ${orangeQuantityBefore + (-1000)}`);
      
      if (orangeAfter && orangeAfter.available_quantity === (orangeQuantityBefore + (-1000))) {
        console.log('✅ SUCCESS: Merge worked correctly!');
      } else {
        console.log('❌ FAIL: Merge quantities do not match!');
        
        // Show merge details if available
        if (mergeResponse.data.merge_details) {
          console.log('\nMerge details:');
          console.log(JSON.stringify(mergeResponse.data.merge_details, null, 2));
        }
      }
      
    } catch (error) {
      console.error('❌ Merge failed:', error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

testCorrectMerge();
