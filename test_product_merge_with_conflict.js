const axios = require('axios');

// Test data - testing product merge with SKU conflict
const testData = {
  current_product_id: "962d4579-ecd5-4e76-9e71-c70e60d2a4fc", // grenn product
  current_sku_id: "692ca704-f95e-4c3c-8910-886a201c14e3", // afdafda SKU
  reason: "SKU code correction - testing merge",
  new_sku_code: "ab" // This will trigger a merge with Apple product
};

const headers = {
  'Accept': '*/*',
  'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'Cache-Control': 'no-cache',
  'Connection': 'keep-alive',
  'Content-Type': 'application/json',
  'Origin': 'http://localhost:5173',
  'Pragma': 'no-cache',
  'Referer': 'http://localhost:5173/',
  'Sec-Fetch-Dest': 'empty',
  'Sec-Fetch-Mode': 'cors',
  'Sec-Fetch-Site': 'same-site',
  'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"macOS"',
  'x-organization-id': 'default-org-id'
};

async function testProductMerge() {
  try {
    console.log('Testing product merge with SKU conflict...');
    console.log('This will merge "grenn" product into "Apple" product');
    console.log('Note: SKU "afdafda" already exists in another product, so it will be skipped\n');
    console.log('Request data:', JSON.stringify(testData, null, 2));
    
    const response = await axios.post(
      'http://localhost:9000/api/inventory/update-sku',
      testData,
      { headers }
    );
    
    console.log('\nResponse status:', response.status);
    console.log('\n✅ Product merge successful!');
    console.log('\nMerge message:', response.data.message);
    
    if (response.data.merge_details) {
      console.log('\nMerge details:');
      console.log('- Source SKUs:', response.data.merge_details.source_skus);
      console.log('- Target SKUs:', response.data.merge_details.target_skus);
      console.log('- Inventory merged:', JSON.stringify(response.data.merge_details.inventory_merged, null, 2));
      console.log('- Sales orders updated:', response.data.merge_details.sales_orders_updated);
      console.log('- Purchase records updated:', response.data.merge_details.purchase_records_updated);
      console.log('- Vehicle arrivals updated:', response.data.merge_details.vehicle_arrivals_updated);
      console.log('- Inventory transactions updated:', response.data.merge_details.inventory_transactions_updated);
    }
    
    console.log('\nUpdated inventory for target product:');
    response.data.updated_inventory.forEach(item => {
      console.log(`- SKU: ${item.sku_code}, Quantity: ${item.available_quantity}`);
    });
    
  } catch (error) {
    console.error('\n❌ Error:', error.response ? error.response.data : error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
    }
  }
}

// Check inventory before merge
async function checkInventoryBefore() {
  try {
    console.log('=== Checking inventory before merge ===\n');
    
    const response = await axios.get(
      'http://localhost:9000/api/inventory',
      { headers }
    );
    
    const grennInventory = response.data.filter(item => item.product_name === 'grenn');
    const appleInventory = response.data.filter(item => item.product_name === 'Apple');
    
    console.log('Grenn product inventory:');
    grennInventory.forEach(item => {
      console.log(`- SKU: ${item.sku_code}, Quantity: ${item.available_quantity}`);
    });
    
    console.log('\nApple product inventory:');
    appleInventory.forEach(item => {
      console.log(`- SKU: ${item.sku_code}, Quantity: ${item.available_quantity}`);
    });
    
    // Check for SKU conflicts
    console.log('\nChecking for SKU conflicts:');
    const allSkus = response.data.map(item => ({ 
      product: item.product_name, 
      sku: item.sku_code 
    }));
    
    const skuCounts = {};
    allSkus.forEach(item => {
      if (!skuCounts[item.sku]) {
        skuCounts[item.sku] = [];
      }
      skuCounts[item.sku].push(item.product);
    });
    
    Object.entries(skuCounts).forEach(([sku, products]) => {
      if (products.length > 1) {
        console.log(`- SKU "${sku}" exists in multiple products: ${products.join(', ')}`);
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching inventory:', error.response ? error.response.data : error.message);
    return [];
  }
}

// Main execution
async function main() {
  console.log('=== Product Merge with SKU Conflict Test ===\n');
  
  // Check inventory before merge
  await checkInventoryBefore();
  
  // Wait a moment before testing
  console.log('\n\nPress Ctrl+C to cancel, or wait 3 seconds to proceed with the merge test...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  console.log('\n=== Performing product merge ===\n');
  
  // Run the test
  await testProductMerge();
}

main();
