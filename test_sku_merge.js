const axios = require('axios');

// Test data - you'll need to adjust these IDs based on your actual data
const testData = {
  current_product_id: "c5490aa4-6c25-412e-9a73-744a42fc9025",
  current_sku_id: "e8df41d2-ec52-41c1-b809-198eb137b802",
  reason: "SKU code correction",
  new_sku_code: "ab"
};

const headers = {
  'Accept': '*/*',
  'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'Cache-Control': 'no-cache',
  'Connection': 'keep-alive',
  'Content-Type': 'application/json',
  'Origin': 'http://localhost:5173',
  'Pragma': 'no-cache',
  'Referer': 'http://localhost:5173/',
  'Sec-Fetch-Dest': 'empty',
  'Sec-Fetch-Mode': 'cors',
  'Sec-Fetch-Site': 'same-site',
  'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"macOS"',
  'x-organization-id': 'default-org-id'
};

async function testSKUUpdate() {
  try {
    console.log('Testing SKU update with merge functionality...');
    console.log('Request data:', JSON.stringify(testData, null, 2));
    
    const response = await axios.post(
      'http://localhost:9000/api/inventory/update-sku',
      testData,
      { headers }
    );
    
    console.log('\nResponse status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.merge_details) {
      console.log('\n✅ Product merge successful!');
      console.log('Merge details:', JSON.stringify(response.data.merge_details, null, 2));
    } else {
      console.log('\n✅ SKU update successful (no merge needed)');
    }
  } catch (error) {
    console.error('\n❌ Error:', error.response ? error.response.data : error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
    }
  }
}

// First, let's check what products and SKUs exist
async function checkInventory() {
  try {
    console.log('Fetching current inventory...\n');
    
    const response = await axios.get(
      'http://localhost:9000/api/inventory',
      { headers }
    );
    
    console.log('Current inventory items:');
    response.data.forEach(item => {
      console.log(`- Product: ${item.product_name} (${item.product_id})`);
      console.log(`  SKU: ${item.sku_code} (${item.sku_id})`);
      console.log(`  Quantity: ${item.available_quantity}\n`);
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching inventory:', error.response ? error.response.data : error.message);
    return [];
  }
}

// Main execution
async function main() {
  console.log('=== SKU Merge Test ===\n');
  
  // First check inventory
  const inventory = await checkInventory();
  
  if (inventory.length === 0) {
    console.log('No inventory found. Please create some test data first.');
    return;
  }
  
  // Find if there's already a product with SKU code "ab"
  const existingAB = inventory.find(item => item.sku_code === 'ab');
  if (existingAB) {
    console.log(`\nFound existing product with SKU code "ab":`);
    console.log(`Product: ${existingAB.product_name} (${existingAB.product_id})`);
    console.log(`This will trigger a merge when updating another SKU to "ab"\n`);
  }
  
  // Wait a moment before testing
  console.log('\nPress Ctrl+C to cancel, or wait 3 seconds to proceed with the test...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Run the test
  await testSKUUpdate();
}

main();
