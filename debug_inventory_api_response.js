const mysql = require('mysql2/promise');

async function debugInventoryAPIResponse() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'rootroot',
    database: 'fxd_partner_erp_backend'
  });

  try {
    console.log('🔍 Debugging inventory API response...');
    
    // Check what the backend API should return (simulate the getAvailable query)
    console.log('\n📋 Current inventory records for Iza product:');
    const [currentInventory] = await connection.execute(`
      SELECT 
        id,
        product_id,
        sku_id,
        product_name,
        sku_code,
        unit_type,
        available_quantity,
        total_weight,
        last_updated_at
      FROM current_inventory 
      WHERE product_name LIKE '%Iza%'
      ORDER BY product_name, sku_code, unit_type
    `);
    
    currentInventory.forEach((item, index) => {
      console.log(`${index + 1}. ID: ${item.id}`);
      console.log(`   Product: ${item.product_name}`);
      console.log(`   SKU: ${item.sku_code}`);
      console.log(`   Unit Type: ${item.unit_type}`);
      console.log(`   Available Quantity: ${item.available_quantity}`);
      console.log(`   Total Weight: ${item.total_weight}`);
      console.log(`   Last Updated: ${item.last_updated_at}`);
      console.log('');
    });
    
    // Check if there are any duplicate product_id + sku_id + unit_type combinations
    console.log('\n📊 Checking for duplicate combinations:');
    const [duplicates] = await connection.execute(`
      SELECT 
        product_id,
        sku_id,
        unit_type,
        COUNT(*) as count,
        GROUP_CONCAT(available_quantity) as quantities,
        GROUP_CONCAT(id) as ids
      FROM current_inventory 
      WHERE product_name LIKE '%Iza%'
      GROUP BY product_id, sku_id, unit_type
      HAVING count > 1
    `);
    
    if (duplicates.length > 0) {
      console.log('❌ Found duplicate records:');
      duplicates.forEach(dup => {
        console.log(`- Product ${dup.product_id}, SKU ${dup.sku_id}, Unit ${dup.unit_type}`);
        console.log(`  Count: ${dup.count}, Quantities: ${dup.quantities}, IDs: ${dup.ids}`);
      });
    } else {
      console.log('✅ No duplicate records found');
    }
    
    // Check recent inventory transactions for context
    console.log('\n📊 Recent inventory transactions for Iza (last 10):');
    const [transactions] = await connection.execute(`
      SELECT 
        id,
        product_id,
        sku_id,
        unit_type,
        transaction_type,
        quantity_change,
        reference_type,
        created_at
      FROM inventory_transactions 
      WHERE product_id IN (
        SELECT DISTINCT product_id 
        FROM current_inventory 
        WHERE product_name LIKE '%Iza%'
      )
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    transactions.forEach((txn, index) => {
      console.log(`${index + 1}. ${txn.transaction_type} (${txn.unit_type}): ${txn.quantity_change > 0 ? '+' : ''}${txn.quantity_change}`);
      console.log(`   Created: ${txn.created_at}`);
    });
    
  } catch (error) {
    console.error('❌ Error debugging inventory:', error.message);
  } finally {
    await connection.end();
  }
}

debugInventoryAPIResponse();
