require('dotenv').config({ path: './FxDPartnerERPBackend/.env.development' });
const mysql = require('mysql2/promise');

async function checkSchema() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'fxd_partner_erp'
  });

  try {
    console.log('🔍 Checking inventory_transactions table schema...\n');
    
    // Get table structure
    const [columns] = await connection.execute(`
      SELECT 
        COLUMN_NAME, 
        DATA_TYPE, 
        IS_NULLABLE, 
        COLUMN_DEFAULT,
        COLUMN_TYPE,
        ORDINAL_POSITION
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'inventory_transactions' 
      AND TABLE_SCHEMA = DATABASE()
      ORDER BY ORDINAL_POSITION
    `);

    console.log('Current table structure:');
    console.log('------------------------');
    columns.forEach(col => {
      console.log(`${col.ORDINAL_POSITION}. ${col.COLUMN_NAME} | ${col.COLUMN_TYPE} | ${col.IS_NULLABLE} | Default: ${col.COLUMN_DEFAULT}`);
    });

    // Check if unit_type column exists
    const unitTypeColumn = columns.find(col => col.COLUMN_NAME === 'unit_type');
    if (unitTypeColumn) {
      console.log('\n✅ unit_type column EXISTS in database');
      console.log('Details:', unitTypeColumn);
    } else {
      console.log('\n❌ unit_type column does NOT exist in database');
    }

    // Check recent inventory transactions
    console.log('\n🔍 Checking recent inventory transactions...');
    const [recentTransactions] = await connection.execute(`
      SELECT id, organization_id, product_id, sku_id, unit_type, transaction_type, quantity_change, created_at
      FROM inventory_transactions 
      ORDER BY created_at DESC 
      LIMIT 5
    `);

    if (recentTransactions.length > 0) {
      console.log('Recent transactions:');
      recentTransactions.forEach(tx => {
        console.log(`- ${tx.id} | ${tx.unit_type || 'NULL'} | ${tx.transaction_type} | Qty: ${tx.quantity_change}`);
      });
    } else {
      console.log('No transactions found');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.code === 'ER_NO_SUCH_TABLE') {
      console.log('Table inventory_transactions does not exist!');
    }
  } finally {
    await connection.end();
  }
}

checkSchema().catch(console.error);
