const axios = require('axios');

const headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'x-organization-id': 'default-org-id',
  'Content-Type': 'application/json'
};

async function checkPomoProduct() {
  try {
    // First, get the Pomo product details
    const productsResponse = await axios.get('http://localhost:9000/api/products', { headers });
    const pomoProduct = productsResponse.data.find(p => p.name === 'Pomo');
    
    if (!pomoProduct) {
      console.log('Pomo product not found!');
      return;
    }
    
    console.log('Found Pomo product:', pomoProduct.id);
    
    // Get SKUs for Pomo product
    const skusResponse = await axios.get(`http://localhost:9000/api/products/${pomoProduct.id}/skus`, { headers });
    console.log('\nPomo product SKUs:', skusResponse.data);
    
    // Check current inventory
    const inventoryResponse = await axios.get('http://localhost:9000/api/inventory', { headers });
    const pomoInventory = inventoryResponse.data.filter(item => item.product_id === pomoProduct.id);
    
    console.log('\nCurrent Pomo inventory items:', pomoInventory.length);
    if (pomoInventory.length > 0) {
      pomoInventory.forEach(item => {
        console.log(`- SKU: ${item.sku_code}, Quantity: ${item.available_quantity}`);
      });
    } else {
      console.log('No inventory found for Pomo product');
      
      // If no inventory, let's check if there are any SKUs
      if (skusResponse.data.length > 0) {
        console.log('\nPomo has SKUs but no inventory. This might be the issue.');
        
        // Create inventory for the first SKU
        const firstSku = skusResponse.data[0];
        console.log(`\nCreating inventory for SKU: ${firstSku.code}`);
        
        const createInventoryData = {
          product_id: pomoProduct.id,
          sku_id: firstSku.id,
          product_name: pomoProduct.name,
          sku_code: firstSku.code,
          unit_type: firstSku.unit_type,
          available_quantity: 10000,
          total_weight: 10000
        };
        
        try {
          const createResponse = await axios.post('http://localhost:9000/api/inventory', createInventoryData, { headers });
          console.log('Inventory created successfully:', createResponse.data);
        } catch (error) {
          console.error('Error creating inventory:', error.response?.data || error.message);
        }
      } else {
        console.log('Pomo product has no SKUs. This is why there\'s no inventory.');
      }
    }
    
    // Check inventory again
    const finalInventoryResponse = await axios.get('http://localhost:9000/api/inventory', { headers });
    const finalPomoInventory = finalInventoryResponse.data.filter(item => item.product_id === pomoProduct.id);
    
    console.log('\n=== Final Pomo inventory ===');
    if (finalPomoInventory.length > 0) {
      finalPomoInventory.forEach(item => {
        console.log(`- SKU: ${item.sku_code}, Quantity: ${item.available_quantity}`);
      });
    } else {
      console.log('Still no inventory for Pomo product');
    }
    
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

checkPomoProduct();
