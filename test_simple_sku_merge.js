const axios = require('axios');

const headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'x-organization-id': 'default-org-id',
  'Content-Type': 'application/json'
};

async function testSimpleMerge() {
  try {
    console.log('=== Testing Simple SKU Code Change ===\n');
    
    // Get current inventory
    const inventoryResponse = await axios.get('http://localhost:9000/api/inventory', { headers });
    const grapeInventory = inventoryResponse.data.find(item => item.product_name === 'grape');
    
    if (!grapeInventory) {
      console.log('Grape not found in inventory');
      return;
    }
    
    // First, try just changing the SKU code without changing product
    console.log('Test 1: Change SKU code only (no product change)');
    const simpleUpdate = {
      current_product_id: grapeInventory.product_id,
      current_sku_id: grapeInventory.sku_id,
      reason: "SKU code update test",
      new_sku_code: "grape-new"
    };
    
    try {
      const response = await axios.post('http://localhost:9000/api/inventory/update-sku', simpleUpdate, { headers });
      console.log('✅ Simple SKU update successful:', response.data.message);
    } catch (error) {
      console.log('❌ Simple SKU update failed:', error.response?.data);
    }
    
    // Now test changing product name only
    console.log('\nTest 2: Change product name only (no SKU change)');
    const productUpdate = {
      current_product_id: grapeInventory.product_id,
      current_sku_id: grapeInventory.sku_id,
      reason: "Product name change test",
      new_product_name: "Orange"
    };
    
    try {
      const response = await axios.post('http://localhost:9000/api/inventory/update-sku', productUpdate, { headers });
      console.log('✅ Product name change successful:', response.data.message);
      
      // Check final inventory
      const finalInventory = await axios.get('http://localhost:9000/api/inventory', { headers });
      const orangeInventory = finalInventory.data.find(item => item.product_name === 'Orange');
      console.log('\nOrange inventory after merge:', orangeInventory?.available_quantity);
      
    } catch (error) {
      console.log('❌ Product name change failed:', error.response?.data);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

testSimpleMerge();
