const axios = require('axios');

const headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************.pnG3tmrPVbnMyFDD0qszqqIaufeAWtX03ZPuqLnjAw4',
  'x-organization-id': 'default-org-id',
  'Content-Type': 'application/json'
};

async function testCleanGrapeOrangeMerge() {
  try {
    console.log('=== Testing Clean Grape-Orange Merge ===\n');
    
    // Get current inventory
    const inventoryBefore = await axios.get('http://localhost:9000/api/inventory', { headers });
    
    console.log('Current inventory:');
    inventoryBefore.data.forEach(item => {
      console.log(`- ${item.product_name} (${item.sku_code}): ${item.available_quantity}`);
    });
    
    // Find grape and orange
    const grapeInventory = inventoryBefore.data.find(item => item.product_name === 'grape');
    const orangeInventory = inventoryBefore.data.find(item => item.product_name === 'Orange');
    
    if (!grapeInventory) {
      console.log('\nGrape not found. Creating it...');
      
      // Create grape product
      const productData = {
        name: 'grape',
        description: 'Grape product',
        status: 'active'
      };
      
      const productResponse = await axios.post('http://localhost:9000/api/products', productData, { headers });
      const grapeProduct = productResponse.data;
      console.log('Grape product created:', grapeProduct.id);
      
      // Create SKU for grape
      const skuData = {
        product_id: grapeProduct.id,
        code: 'gi',
        unit_type: 'box',
        unit_weight: 1,
        status: 'active'
      };
      
      const skuResponse = await axios.post('http://localhost:9000/api/products/skus', skuData, { headers });
      const grapeSku = skuResponse.data;
      console.log('Grape SKU created:', grapeSku.code);
      
      // Create inventory
      const inventoryData = {
        product_id: grapeProduct.id,
        sku_id: grapeSku.id,
        product_name: grapeProduct.name,
        sku_code: grapeSku.code,
        unit_type: grapeSku.unit_type,
        available_quantity: -1000,
        total_weight: 0
      };
      
      await axios.post('http://localhost:9000/api/inventory', inventoryData, { headers });
      console.log('Grape inventory created with -1000 quantity');
      
      // Refresh inventory
      const refreshedInventory = await axios.get('http://localhost:9000/api/inventory', { headers });
      const newGrapeInventory = refreshedInventory.data.find(item => item.product_name === 'grape');
      
      if (newGrapeInventory) {
        console.log('\n=== Ready to test merge ===');
        console.log(`Grape: ${newGrapeInventory.available_quantity} (SKU: ${newGrapeInventory.sku_code})`);
        console.log(`Orange: ${orangeInventory.available_quantity} (SKU: ${orangeInventory.sku_code})`);
        console.log(`Expected after merge: ${orangeInventory.available_quantity + newGrapeInventory.available_quantity}`);
        
        // Perform the merge
        console.log('\nPerforming merge...');
        const mergeData = {
          current_product_id: newGrapeInventory.product_id,
          current_sku_id: newGrapeInventory.sku_id,
          reason: "SKU code correction",
          new_product_name: "Orange",
          new_sku_code: "apple"
        };
        
        try {
          const mergeResponse = await axios.post('http://localhost:9000/api/inventory/update-sku', mergeData, { headers });
          console.log('✅ Merge successful!');
          console.log('Response:', mergeResponse.data.message);
          
          // Check final inventory
          const finalInventory = await axios.get('http://localhost:9000/api/inventory', { headers });
          const finalOrange = finalInventory.data.find(item => item.product_name === 'Orange' && item.sku_code === 'apple');
          
          if (finalOrange) {
            console.log(`\n=== Final Result ===`);
            console.log(`Orange quantity: ${finalOrange.available_quantity}`);
            console.log(`Expected: ${orangeInventory.available_quantity + newGrapeInventory.available_quantity}`);
            
            if (finalOrange.available_quantity === (orangeInventory.available_quantity + newGrapeInventory.available_quantity)) {
              console.log('✅ SUCCESS: Quantities match!');
            } else {
              console.log('❌ FAIL: Quantities do not match!');
            }
          }
          
        } catch (error) {
          console.error('❌ Merge failed:', error.response?.data || error.message);
        }
      }
    } else {
      // Grape exists, proceed with merge
      console.log('\n=== Ready to test merge ===');
      console.log(`Grape: ${grapeInventory.available_quantity} (SKU: ${grapeInventory.sku_code})`);
      console.log(`Orange: ${orangeInventory.available_quantity} (SKU: ${orangeInventory.sku_code})`);
      console.log(`Expected after merge: ${orangeInventory.available_quantity + grapeInventory.available_quantity}`);
      
      // Perform the merge
      console.log('\nPerforming merge...');
      const mergeData = {
        current_product_id: grapeInventory.product_id,
        current_sku_id: grapeInventory.sku_id,
        reason: "SKU code correction",
        new_product_name: "Orange",
        new_sku_code: "apple"
      };
      
      try {
        const mergeResponse = await axios.post('http://localhost:9000/api/inventory/update-sku', mergeData, { headers });
        console.log('✅ Merge successful!');
        console.log('Response:', mergeResponse.data.message);
        
        // Check final inventory
        const finalInventory = await axios.get('http://localhost:9000/api/inventory', { headers });
        const finalOrange = finalInventory.data.find(item => item.product_name === 'Orange' && item.sku_code === 'apple');
        
        if (finalOrange) {
          console.log(`\n=== Final Result ===`);
          console.log(`Orange quantity: ${finalOrange.available_quantity}`);
          console.log(`Expected: ${orangeInventory.available_quantity + grapeInventory.available_quantity}`);
          
          if (finalOrange.available_quantity === (orangeInventory.available_quantity + grapeInventory.available_quantity)) {
            console.log('✅ SUCCESS: Quantities match!');
          } else {
            console.log('❌ FAIL: Quantities do not match!');
          }
        }
        
      } catch (error) {
        console.error('❌ Merge failed:', error.response?.data || error.message);
      }
    }
    
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

testCleanGrapeOrangeMerge();
