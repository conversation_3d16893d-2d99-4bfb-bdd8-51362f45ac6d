/**
 * Test script to verify inventory unit type separation is working correctly
 * This script will test that inventory records are properly separated by unit type
 * and that logs show individual entries for box vs loose units
 */

const { Sequelize, DataTypes } = require('sequelize');
const { v4: uuidv4 } = require('uuid');

// Database connection (adjust as needed)
const sequelize = new Sequelize({
  dialect: 'postgres',
  host: 'localhost',
  port: 5432,
  database: 'fxd_partner_erp',
  username: 'postgres',
  password: 'your_password', // Update this
  logging: console.log
});

async function testInventoryUnitTypeSeparation() {
  try {
    console.log('🧪 Starting inventory unit type separation test...\n');
    
    // Test organization ID - use existing one or create test one
    const organizationId = 'your-test-org-id'; // Update this with a real org ID
    
    // Create test product and SKUs
    console.log('1. Creating test product and SKUs...');
    
    const productId = uuidv4();
    const boxSkuId = uuidv4();
    const looseSkuId = uuidv4();
    
    // Insert test product
    await sequelize.query(`
      INSERT INTO products (id, organization_id, name, status, created_at, updated_at)
      VALUES (:productId, :organizationId, 'Test Apples for Unit Separation', 'active', NOW(), NOW())
      ON CONFLICT (id) DO NOTHING
    `, {
      replacements: { productId, organizationId }
    });
    
    // Insert test SKUs - one for box, one for loose
    await sequelize.query(`
      INSERT INTO skus (id, organization_id, product_id, code, unit_type, unit_weight, status, created_at, updated_at)
      VALUES 
        (:boxSkuId, :organizationId, :productId, 'TEST-APPLE-BOX', 'box', 10.0, 'active', NOW(), NOW()),
        (:looseSkuId, :organizationId, :productId, 'TEST-APPLE-LOOSE', 'loose', 1.0, 'active', NOW(), NOW())
      ON CONFLICT (id) DO NOTHING
    `, {
      replacements: { boxSkuId, looseSkuId, organizationId, productId }
    });
    
    console.log(`   ✓ Created product: ${productId}`);
    console.log(`   ✓ Created box SKU: ${boxSkuId}`);
    console.log(`   ✓ Created loose SKU: ${looseSkuId}`);
    
    // Test 2: Create separate inventory records
    console.log('\n2. Creating separate inventory records for each unit type...');
    
    const boxInventoryId = uuidv4();
    const looseInventoryId = uuidv4();
    
    // Insert box inventory
    await sequelize.query(`
      INSERT INTO current_inventory (
        id, organization_id, product_id, sku_id, product_name, sku_code, 
        unit_type, available_quantity, total_weight, last_updated_at, created_at, updated_at
      )
      VALUES (
        :boxInventoryId, :organizationId, :productId, :boxSkuId, 'Test Apples for Unit Separation', 'TEST-APPLE-BOX',
        'box', 50, '500.000', NOW(), NOW(), NOW()
      )
      ON CONFLICT (organization_id, product_id, sku_id, unit_type) 
      DO UPDATE SET available_quantity = current_inventory.available_quantity + EXCLUDED.available_quantity
    `, {
      replacements: { boxInventoryId, organizationId, productId, boxSkuId }
    });
    
    // Insert loose inventory
    await sequelize.query(`
      INSERT INTO current_inventory (
        id, organization_id, product_id, sku_id, product_name, sku_code, 
        unit_type, available_quantity, total_weight, last_updated_at, created_at, updated_at
      )
      VALUES (
        :looseInventoryId, :organizationId, :productId, :looseSkuId, 'Test Apples for Unit Separation', 'TEST-APPLE-LOOSE',
        'loose', 25, '25.000', NOW(), NOW(), NOW()
      )
      ON CONFLICT (organization_id, product_id, sku_id, unit_type) 
      DO UPDATE SET available_quantity = current_inventory.available_quantity + EXCLUDED.available_quantity
    `, {
      replacements: { looseInventoryId, organizationId, productId, looseSkuId }
    });
    
    console.log(`   ✓ Created box inventory: ${boxInventoryId} (50 boxes)`);
    console.log(`   ✓ Created loose inventory: ${looseInventoryId} (25 kg)`);
    
    // Test 3: Verify separate records exist
    console.log('\n3. Verifying separate inventory records...');
    
    const [inventoryRecords] = await sequelize.query(`
      SELECT id, product_name, sku_code, unit_type, available_quantity, total_weight
      FROM current_inventory 
      WHERE organization_id = :organizationId AND product_id = :productId
      ORDER BY unit_type
    `, {
      replacements: { organizationId, productId }
    });
    
    console.log('   Inventory records found:');
    inventoryRecords.forEach((record, index) => {
      console.log(`   ${index + 1}. ${record.product_name} - ${record.sku_code}`);
      console.log(`      Unit Type: ${record.unit_type}`);
      console.log(`      Quantity: ${record.available_quantity}`);
      console.log(`      Weight: ${record.total_weight}`);
      console.log('');
    });
    
    if (inventoryRecords.length !== 2) {
      throw new Error(`Expected 2 inventory records, but found ${inventoryRecords.length}`);
    }
    
    console.log('   ✅ SUCCESS: Found separate inventory records for each unit type');
    
    // Test 4: Create test transactions for each unit type
    console.log('\n4. Creating test transactions...');
    
    const boxTransactionId = uuidv4();
    const looseTransactionId = uuidv4();
    
    // Box transaction
    await sequelize.query(`
      INSERT INTO inventory_transactions (
        id, organization_id, product_id, sku_id, transaction_type, 
        quantity_change, weight_change, reference_type, reference_id, 
        reason, notes, created_at, updated_at
      )
      VALUES (
        :boxTransactionId, :organizationId, :productId, :boxSkuId, 'vehicle_arrival',
        20, 200.0, 'vehicle_arrival', :boxTransactionId,
        'Test vehicle arrival - boxes', 'Unit type separation test', NOW(), NOW()
      )
    `, {
      replacements: { boxTransactionId, organizationId, productId, boxSkuId }
    });
    
    // Loose transaction
    await sequelize.query(`
      INSERT INTO inventory_transactions (
        id, organization_id, product_id, sku_id, transaction_type, 
        quantity_change, weight_change, reference_type, reference_id, 
        reason, notes, created_at, updated_at
      )
      VALUES (
        :looseTransactionId, :organizationId, :productId, :looseSkuId, 'vehicle_arrival',
        15, 15.0, 'vehicle_arrival', :looseTransactionId,
        'Test vehicle arrival - loose', 'Unit type separation test', NOW(), NOW()
      )
    `, {
      replacements: { looseTransactionId, organizationId, productId, looseSkuId }
    });
    
    console.log(`   ✓ Created box transaction: +20 boxes`);
    console.log(`   ✓ Created loose transaction: +15 kg`);
    
    // Test 5: Verify transaction history separation
    console.log('\n5. Verifying transaction history separation...');
    
    // Check box transactions
    const [boxTransactions] = await sequelize.query(`
      SELECT 
        it.transaction_type, it.quantity_change, it.reason, it.created_at,
        s.unit_type, s.code as sku_code
      FROM inventory_transactions it
      JOIN skus s ON s.id = it.sku_id
      WHERE it.organization_id = :organizationId 
        AND it.product_id = :productId 
        AND it.sku_id = :boxSkuId
      ORDER BY it.created_at DESC
      LIMIT 5
    `, {
      replacements: { organizationId, productId, boxSkuId }
    });
    
    // Check loose transactions
    const [looseTransactions] = await sequelize.query(`
      SELECT 
        it.transaction_type, it.quantity_change, it.reason, it.created_at,
        s.unit_type, s.code as sku_code
      FROM inventory_transactions it
      JOIN skus s ON s.id = it.sku_id
      WHERE it.organization_id = :organizationId 
        AND it.product_id = :productId 
        AND it.sku_id = :looseSkuId
      ORDER BY it.created_at DESC
      LIMIT 5
    `, {
      replacements: { organizationId, productId, looseSkuId }
    });
    
    console.log(`   Box unit transactions (${boxTransactions.length}):`);
    boxTransactions.forEach((txn, index) => {
      console.log(`   ${index + 1}. ${txn.transaction_type}: ${txn.quantity_change > 0 ? '+' : ''}${txn.quantity_change} ${txn.unit_type} - ${txn.reason}`);
    });
    
    console.log(`   Loose unit transactions (${looseTransactions.length}):`);
    looseTransactions.forEach((txn, index) => {
      console.log(`   ${index + 1}. ${txn.transaction_type}: ${txn.quantity_change > 0 ? '+' : ''}${txn.quantity_change} ${txn.unit_type} - ${txn.reason}`);
    });
    
    // Test 6: Verify final inventory state
    console.log('\n6. Verifying final inventory state after transactions...');
    
    const [finalInventory] = await sequelize.query(`
      SELECT product_name, sku_code, unit_type, available_quantity, total_weight
      FROM current_inventory 
      WHERE organization_id = :organizationId AND product_id = :productId
      ORDER BY unit_type
    `, {
      replacements: { organizationId, productId }
    });
    
    console.log('   Final inventory state:');
    finalInventory.forEach((record, index) => {
      console.log(`   ${index + 1}. ${record.product_name} - ${record.sku_code} (${record.unit_type})`);
      console.log(`      Quantity: ${record.available_quantity}`);
      console.log(`      Weight: ${record.total_weight}`);
    });
    
    // Validate expected quantities (original + transaction)
    const boxRecord = finalInventory.find(r => r.unit_type === 'box');
    const looseRecord = finalInventory.find(r => r.unit_type === 'loose');
    
    if (!boxRecord || !looseRecord) {
      throw new Error('Missing inventory records after transactions');
    }
    
    // Note: We expect transactions to not affect inventory automatically 
    // since we only created transaction records, not used the service
    console.log(`   Expected: Box quantity should be 50 (original), found: ${boxRecord.available_quantity}`);
    console.log(`   Expected: Loose quantity should be 25 (original), found: ${looseRecord.available_quantity}`);
    
    // Test 7: Test the unique constraint
    console.log('\n7. Testing unique constraint...');
    
    try {
      await sequelize.query(`
        INSERT INTO current_inventory (
          id, organization_id, product_id, sku_id, product_name, sku_code, 
          unit_type, available_quantity, total_weight, last_updated_at, created_at, updated_at
        )
        VALUES (
          :testId, :organizationId, :productId, :boxSkuId, 'Test Apples for Unit Separation', 'TEST-APPLE-BOX',
          'box', 10, '100.000', NOW(), NOW(), NOW()
        )
      `, {
        replacements: { 
          testId: uuidv4(), 
          organizationId, 
          productId, 
          boxSkuId 
        }
      });
      
      console.log('   ❌ ERROR: Unique constraint should have prevented duplicate insertion');
    } catch (error) {
      if (error.message.includes('uk_current_inventory_product_sku_unit_type')) {
        console.log('   ✅ SUCCESS: Unique constraint is working correctly');
      } else {
        console.log(`   ⚠️  Unexpected error: ${error.message}`);
      }
    }
    
    console.log('\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Separate inventory records created for different unit types');
    console.log('   ✅ Transaction history maintains separation by unit type');
    console.log('   ✅ Unique constraint prevents duplicate records');
    console.log('   ✅ No merging occurs between different unit types');
    
    console.log('\n🧹 Cleaning up test data...');
    
    // Clean up test data
    await sequelize.query(`DELETE FROM inventory_transactions WHERE reference_id IN (:boxTransactionId, :looseTransactionId)`, {
      replacements: { boxTransactionId, looseTransactionId }
    });
    
    await sequelize.query(`DELETE FROM current_inventory WHERE product_id = :productId`, {
      replacements: { productId }
    });
    
    await sequelize.query(`DELETE FROM skus WHERE product_id = :productId`, {
      replacements: { productId }
    });
    
    await sequelize.query(`DELETE FROM products WHERE id = :productId`, {
      replacements: { productId }
    });
    
    console.log('   ✓ Test data cleaned up');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run the test
if (require.main === module) {
  testInventoryUnitTypeSeparation()
    .then(() => {
      console.log('\n✅ Test script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testInventoryUnitTypeSeparation };
