const { QueryTypes } = require('sequelize');
const sequelize = require('./FxDPartnerERPBackend/src/config/sequelize');

async function checkAndFixInventory() {
  try {
    console.log('🔍 Checking current inventory data...');
    
    // Check for duplicate inventory records (same product_id, sku_id, unit_type)
    const duplicates = await sequelize.query(`
      SELECT 
        product_id, 
        sku_id, 
        unit_type, 
        COUNT(*) as count,
        GROUP_CONCAT(id) as record_ids,
        GROUP_CONCAT(available_quantity) as quantities
      FROM current_inventory 
      GROUP BY product_id, sku_id, unit_type 
      HAVING COUNT(*) > 1
    `, { type: QueryTypes.SELECT });
    
    console.log(`📊 Found ${duplicates.length} duplicate inventory groups`);
    
    if (duplicates.length > 0) {
      console.log('🔧 Duplicate records found:');
      duplicates.forEach(dup => {
        console.log(`  - Product: ${dup.product_id}, SKU: ${dup.sku_id}, Unit: ${dup.unit_type}`);
        console.log(`    Records: ${dup.record_ids}, Quantities: ${dup.quantities}`);
      });
      
      // Fix duplicates by consolidating them
      for (const dup of duplicates) {
        const recordIds = dup.record_ids.split(',');
        const quantities = dup.quantities.split(',').map(q => parseFloat(q) || 0);
        const totalQuantity = quantities.reduce((sum, q) => sum + q, 0);
        
        console.log(`🔧 Consolidating ${recordIds.length} records for ${dup.product_id}/${dup.sku_id}/${dup.unit_type}`);
        console.log(`   Total quantity: ${totalQuantity}`);
        
        // Keep the first record and update its quantity
        const keepId = recordIds[0];
        const deleteIds = recordIds.slice(1);
        
        // Get additional data from the first record
        const [firstRecord] = await sequelize.query(`
          SELECT * FROM current_inventory WHERE id = ?
        `, { replacements: [keepId], type: QueryTypes.SELECT });
        
        // Update the first record with consolidated data
        await sequelize.query(`
          UPDATE current_inventory 
          SET available_quantity = ?, 
              last_updated_at = NOW()
          WHERE id = ?
        `, { replacements: [totalQuantity, keepId] });
        
        // Delete the duplicate records
        if (deleteIds.length > 0) {
          await sequelize.query(`
            DELETE FROM current_inventory WHERE id IN (${deleteIds.map(() => '?').join(',')})
          `, { replacements: deleteIds });
        }
        
        console.log(`   ✅ Consolidated into record ${keepId}, deleted ${deleteIds.length} duplicates`);
      }
    }
    
    // Check for inventory records without proper unit_type
    const noUnitType = await sequelize.query(`
      SELECT id, product_id, sku_id, unit_type, available_quantity
      FROM current_inventory 
      WHERE unit_type IS NULL OR unit_type = ''
    `, { type: QueryTypes.SELECT });
    
    console.log(`📊 Found ${noUnitType.length} records without unit_type`);
    
    if (noUnitType.length > 0) {
      console.log('🔧 Records missing unit_type:');
      for (const record of noUnitType) {
        console.log(`  - Record: ${record.id}, Product: ${record.product_id}, SKU: ${record.sku_id}`);
        
        // Try to get unit_type from SKU table
        const [skuData] = await sequelize.query(`
          SELECT unit_type FROM skus WHERE id = ?
        `, { replacements: [record.sku_id], type: QueryTypes.SELECT });
        
        if (skuData && skuData.unit_type) {
          await sequelize.query(`
            UPDATE current_inventory 
            SET unit_type = ?, last_updated_at = NOW()
            WHERE id = ?
          `, { replacements: [skuData.unit_type, record.id] });
          
          console.log(`    ✅ Updated unit_type to '${skuData.unit_type}'`);
        } else {
          // Default to 'box' if SKU not found
          await sequelize.query(`
            UPDATE current_inventory 
            SET unit_type = 'box', last_updated_at = NOW()
            WHERE id = ?
          `, { replacements: [record.id] });
          
          console.log(`    ✅ Set default unit_type to 'box'`);
        }
      }
    }
    
    // Show final inventory summary
    const summary = await sequelize.query(`
      SELECT 
        unit_type,
        COUNT(*) as record_count,
        SUM(available_quantity) as total_quantity
      FROM current_inventory 
      GROUP BY unit_type
      ORDER BY unit_type
    `, { type: QueryTypes.SELECT });
    
    console.log('📈 Final inventory summary:');
    summary.forEach(item => {
      console.log(`  ${item.unit_type}: ${item.record_count} records, ${item.total_quantity} total quantity`);
    });
    
    // Check for any remaining issues
    const issues = await sequelize.query(`
      SELECT 
        product_id, 
        sku_id, 
        unit_type,
        COUNT(*) as count
      FROM current_inventory 
      GROUP BY product_id, sku_id, unit_type 
      HAVING COUNT(*) > 1
    `, { type: QueryTypes.SELECT });
    
    if (issues.length === 0) {
      console.log('✅ All inventory data is now clean and properly separated!');
    } else {
      console.log(`❌ Still have ${issues.length} duplicate groups that need attention`);
    }
    
  } catch (error) {
    console.error('❌ Error checking inventory:', error);
  }
}

// Run the check
checkAndFixInventory()
  .then(() => {
    console.log('🎉 Inventory check completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Failed to check inventory:', error);
    process.exit(1);
  });
